﻿using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Types;

public class AuthorizationContext
{
    /// <summary>
    /// The Active permission depending on request path walletId.
    /// Can be <see langword="null"/>
    /// </summary>
    public IPermission ActivePermission { get; set; }
}

public enum AuthorizationTypes
{
    Admin,
    RegisteredWallet,
    TransactionView,
    Active,
    ApproveTransaction,
    SubmitTransaction,
    BalanceView
}
