﻿using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Implementation;

public abstract class PayTransBase(IHttpContextRepositoryService httpContextService, IAuthContextService authContextService)
{
    internal readonly IHttpContextRepositoryService _httpContextService = httpContextService;
    internal readonly IAuthContextService _authContextService = authContextService;

    internal (string UserId, string CustomerCode, string ExecutedAs) GetUserAndCustomerCode()
    {
        var context = _authContextService.GetContext();
        if (context.ActivePermission is null)
        {
            throw new PermissionNotFoundException();
        }

        var userId = _httpContextService.GetUserId();
        var customerCode = _httpContextService.GetCustomerCode();

        var executedAs = context.ActivePermission.PermissionType switch
        {
            PermissionType.USER => userId,
            PermissionType.WALLET => context.ActivePermission.AssignedBy,
            _ => throw new System.NotImplementedException(),
        };

        return (userId, customerCode, executedAs);
    }
}
