﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SanboxWalletPermissionRepositoryService : IWalletPermissionsRepositoryService
{
    private readonly IMapper _mapper;
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;

    public SanboxWalletPermissionRepositoryService(IMapper mapper,
        IHttpContextAccessor httpContextAccessor, ISandBoxRepositoryService sandBoxRepositoryService, IHttpContextRepositoryService httpContextRepositoryService)
    {
        _mapper = mapper;
        _sandBoxRepositoryService = sandBoxRepositoryService;
        _httpContextAccessor = httpContextAccessor;
        _httpContextRepositoryService = httpContextRepositoryService;
    }


    public async Task<List<RepoWalletPermission>> FindAllActiveByWalletIdAndExternalWalletIdAsync(Guid walletId, Guid externalWalletId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        //var userid = _httpContextRepositoryService.GetUserId();

        if (externalWalletId == Guid.Empty)
        {
            var perms = mEwalletSandbox.WalletsPermissions.FindAll(p => p.WalletId == walletId && p.ExpirationDate > DateTime.UtcNow);
            return _mapper.Map<List<RepoWalletPermission>>(perms);
        }
        else
        {
            var perms = mEwalletSandbox.WalletsPermissions
                .FindAll(p => p.TargetWalletId == externalWalletId && p.WalletId == walletId && p.ExpirationDate > DateTime.UtcNow);
            return _mapper.Map<List<RepoWalletPermission>>(perms);
        }
    }

    public async Task<List<RepoWalletPermission>> FindAllByExpiredAndWalletIdAndExternalWalletId(Guid walletId, Guid targetWalletId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        //var userid = _httpContextRepositoryService.GetUserId();

        if (targetWalletId == Guid.Empty)
        {
            var perms = mEwalletSandbox.WalletsPermissions
                .FindAll(p => p.WalletId == walletId)
                .Where(p => p.ExpirationDate < DateTime.UtcNow)
                .ToList();
            return _mapper.Map<List<RepoWalletPermission>>(perms);
        }
        else
        {
            var perms = mEwalletSandbox.WalletsPermissions
                .FindAll(p => p.TargetWalletId == targetWalletId && p.WalletId == walletId)
                .Where(p => p.ExpirationDate < DateTime.UtcNow)
                .ToList();
            return _mapper.Map<List<RepoWalletPermission>>(perms);
        }
    }


    public async Task<List<RepoWalletPermission>> FindAllByWalletIdAsync(Guid myWalletId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        //var userid = _httpContextRepositoryService.GetUserId();

        var perms = mEwalletSandbox.WalletsPermissions.FindAll(p => p.TargetWalletId == myWalletId && p.ExpirationDate > DateTime.UtcNow);
        return _mapper.Map<List<RepoWalletPermission>>(perms);
    }

    public async Task<List<RepoWalletPermission>> FindAllByWalletIdAndTargetWalletIdAsync(Guid walletId, Guid targetWalletId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        //var userid = _httpContextRepositoryService.GetUserId();

        var perms = mEwalletSandbox.WalletsPermissions.FindAll(p => p.TargetWalletId == targetWalletId && p.WalletId == walletId && p.ExpirationDate > DateTime.UtcNow);
        return _mapper.Map<List<RepoWalletPermission>>(perms);
    }

    public async Task<List<RepoWalletPermission>> FindAllActiveByWalletIdAndTargetWalletIdAsync(Guid walletId, Guid targetWalletId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        //var userid = _httpContextRepositoryService.GetUserId();

        var perms = mEwalletSandbox.WalletsPermissions
            .Where(wp => wp.TargetWalletId == targetWalletId)
            .Where(wp => wp.WalletId == walletId)
            .Where(wp => wp.ExpirationDate > DateTime.UtcNow)
            .ToList();

        return _mapper.Map<List<RepoWalletPermission>>(perms);
    }

    public async Task ExpireExistingPermissionsyWalletIdAsync(Guid myWalletId, IEnumerable<RepoWalletPermission> permissions)
    {
        var callersid = _httpContextRepositoryService.GetUserId();
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        var myWAllet = mEwalletSandbox.Wallets.First(x => x.OwnerUserId == callersid);

        foreach (var walletPermissionRequest in permissions)
        {
            // get existing permissios for user
            // and expire them
            var walletPermissions = mEwalletSandbox.WalletsPermissions
                .Where(x => x.TargetWalletId == walletPermissionRequest.TargetWalletId)
                .Where(x => x.WalletId == myWAllet.WalletId)
                .Where(x => x.ExpirationDate > DateTime.UtcNow);
            foreach (var walletPermission in walletPermissions)
            {
                walletPermission.ExpirationDate = DateTime.UtcNow;
            }
        }

        await _sandBoxRepositoryService.UpdateSandboxData(mEwalletSandbox);
    }

    public async Task SaveAllAsync(List<RepoWalletPermission> permissions)
    {
        //var callersid = _httpContextRepositoryService.GetUserId();
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        //var myWAllet = mEwalletSandbox.Wallets.First(x => x.OwnerUserId == callersid);

        // iterate and update
        // check test case here
        foreach (var walletPermissionRequest in permissions)
        {
            var mWalletPermissions = new SandboxWalletPermission();
            mWalletPermissions.WalletId = walletPermissionRequest.WalletId;
            mWalletPermissions.Id = walletPermissionRequest.Id;
            mWalletPermissions.Approve = walletPermissionRequest.Approve;
            mWalletPermissions.BalanceView = walletPermissionRequest.BalanceView;
            mWalletPermissions.Submit = walletPermissionRequest.Submit;
            mWalletPermissions.TransactionView = walletPermissionRequest.TransactionView;
            mWalletPermissions.ExpirationDate = DateTime.UtcNow.AddDays(180).Date.AddDays(1).AddTicks(-1);
            mWalletPermissions.TargetWalletId = walletPermissionRequest.TargetWalletId;
            mWalletPermissions.CreationDate = DateTime.UtcNow;
            mEwalletSandbox.WalletsPermissions.Add(mWalletPermissions);
        }

        await _sandBoxRepositoryService.UpdateSandboxData(mEwalletSandbox);
    }

    public async Task<RepoWalletPermission> SaveAsync(RepoWalletPermission walletPermission)
    {
        //var userid = _httpContextRepositoryService.GetUserId();
        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        var permission = sandbox.WalletsPermissions.Find(r => r.Id == walletPermission.Id);

        permission.ExpirationDate = walletPermission.ExpirationDate;
        permission.Approve = walletPermission.Approve;
        permission.BalanceView = walletPermission.BalanceView;
        permission.Submit = walletPermission.Submit;
        permission.TransactionView = walletPermission.TransactionView;
        await _sandBoxRepositoryService.UpdateSandboxData(sandbox);

        return _mapper.Map<RepoWalletPermission>(permission);
    }
}
