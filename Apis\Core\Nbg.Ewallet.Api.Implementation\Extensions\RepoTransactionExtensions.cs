﻿using System;
using ibank.ThirdParty.Types;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Types;
using Transaction = Nbg.Ewallet.Api.Types.Transfers.Transaction;
using TransactionStatus = Nbg.Ewallet.Repository.Types.TransactionStatus;

namespace Nbg.Ewallet.Api.Implementation.Extensions;

public static class RepoTransactionExtensions
{
    public static RepoTransaction UpdateTransactionSuccess(this RepoTransaction @this)
    {
        if (@this == null) return null;
        if (@this.TransactionType == TransactionType.Payment) @this.Reference = null;

        @this.UpdatedAt = DateTime.UtcNow;
        @this.TransactionDate = DateTime.UtcNow;

        @this.Status = TransactionStatus.EXECUTED;
        @this.Result = TransactionConstants.SuccessResult;
        return @this;
    }

    public static RepoTransaction UpdateCreditorDetails(this RepoTransaction @this, Creditor creditor)
    {
        if (@this == null || creditor == null) return null;
        @this.CreditorIban = creditor.CreditorAccount?.IBAN ?? string.Empty;
        @this.CreditorName = creditor.Name;
        return @this;
    }

    public static RepoTransaction UpdateTransactionFailure(this RepoTransaction @this, string errorMessage)
    {
        if (@this == null) return null;
        if (@this.TransactionType == TransactionType.Payment) @this.Reference = null;

        @this.UpdatedAt = DateTime.UtcNow;
        @this.Status = TransactionStatus.EXECUTED;
        @this.Result = TransactionConstants.FailureResult;
        @this.ResultReason = $"{errorMessage}";
        return @this;
    }

    public static Transaction MapToTransaction(this RepoTransaction repoTransaction)
    {
        var transaction = new Transaction
        {
            TransactionId = repoTransaction.TransactionId,
            WalletId = repoTransaction.WalletId,
            Amount = repoTransaction.Amount,
            DebitIban = repoTransaction.DebtorIban,
            DebitName = repoTransaction.DebtorName,
            CreditIban = repoTransaction.CreditorIban,
            CreditName = repoTransaction.CreditorName,
            TransactionDate = repoTransaction.TransactionDate,
            Timestamp = repoTransaction.CreatedAt,
            Valeur = repoTransaction.Valeur,
            Currency = repoTransaction.Currency,
            Reference = repoTransaction.Reference,
            Description = repoTransaction.Reason,
            BatchId = repoTransaction.BatchId,
            //BatchDescription = repoTransaction.BatchDescription,
            SubmittedBy = repoTransaction.SubmittedBy,
            Result = repoTransaction.Result,
            ResultReason = repoTransaction.ResultReason,
            TransactionSubType = repoTransaction.TransactionSubType,
            Status = repoTransaction.Status,
            TransactionType = repoTransaction.TransactionType,
            Instant = repoTransaction.IsInstant,
            ApprovedBy = repoTransaction.ApprovedBy,
            ExecutedBy = repoTransaction.ExecutedBy,
            RejectedBy = repoTransaction.RejectedBy,
            Commission = repoTransaction.Commission
        };
        return transaction;
    }

}
