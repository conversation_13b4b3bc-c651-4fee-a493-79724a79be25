﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Crrate Payment
/// </summary>
public class PaymentsCreateRequest : RequiresOtp
{
    /// <summary>
    /// Debtor Name
    /// </summary>
    [DataMember(Name = "debtorName")]
    public string DebtorName { get; set; }

    /// <summary>
    /// Debtor Iban
    /// </summary>
    [DataMember(Name = "debtorIban")]
    public string DebtorIban { get; set; }

    /// <summary>
    /// Debtor Telephone
    /// </summary>
    [DataMember(Name = "debtorTelephone")]
    public string DebtorTelephone { get; set; }

    /// <summary>
    /// List of Payments
    /// </summary>
    [DataMember(Name = "payments")]
    public List<Payment> Payments { get; set; }
}
