﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nbg.ewallet.repository.dbQueries;
using Nbg.Ewallet.Repository.dbQueries;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;

namespace Nbg.Ewallet.Repository;

public class WalletPermissionsRepositoryService : BaseRepositoryService, IWalletPermissionsRepositoryService
{
    public WalletPermissionsRepositoryService(
        IOptions<RepositorySettings> repositoryOptions,
        ILogger<WalletPermissionsRepositoryService> logger,
        IUnitOfWork unitOfWork) : base(unitOfWork, repositoryOptions, logger) { }

    public async Task<List<RepoWalletPermission>> FindAllByWalletIdAsync(Guid myWalletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var response = await conn.QueryAsync<RepoWalletPermission>(
                ProfileQueries.GetActiveByExternalWalletId,
                new { ExternalWalletId = myWalletId },
                tx);
            return response.ToList();
        }, "A database error occurred while fetching active wallet permissions by external wallet ID.");
    }

    public async Task<List<RepoWalletPermission>> FindAllByWalletIdAndTargetWalletIdAsync(Guid walletId, Guid targetWalletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var response = await conn.QueryAsync<RepoWalletPermission>(
                ProfileQueries.GetWalletPermissions,
                new { WalletId = walletId, TargetWalletId = targetWalletId },
                tx);
            return response.ToList();
        }, "A database error occurred while fetching wallet permissions by wallet ID and target wallet ID.");
    }

    public async Task<List<RepoWalletPermission>> FindAllActiveByWalletIdAndTargetWalletIdAsync(Guid walletId, Guid targetWalletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var response = await conn.QueryAsync<RepoWalletPermission>(
                ExternalWalletPermissionsQueries.GetExistingActiveExternalWalletPermission,
                new { walletid = walletId, ExternalWalletId = targetWalletId, CurrentDate = DateTime.UtcNow },
                tx);
            return response.ToList();
        }, "A database error occurred while fetching active wallet permissions by wallet ID and target wallet ID.");
    }

    public async Task ExpireExistingPermissionsyWalletIdAsync(Guid myWalletId, IEnumerable<RepoWalletPermission> permissions)
    {
        if (permissions == null || !permissions.Any()) return;

        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            foreach (var permission in permissions)
            {
                var existingPermission = await conn.QueryFirstOrDefaultAsync(
                    ExternalWalletPermissionsQueries.GetCurrentActiveExternalWalletPermission,
                    new { WalletId = myWalletId, ExternalWalletId = permission.TargetWalletId, CurrentDate = DateTime.UtcNow }, tx);

                if (existingPermission != null)
                {
                    await conn.ExecuteAsync(ExternalWalletPermissionsQueries.ExpireExternalWalletPermission,
                        new { ID = existingPermission.Id, ExpirationDate = DateTime.UtcNow }, tx);
                }
            }
        }, "A database error occurred while expiring existing wallet permissions.");
    }

    public async Task SaveAllAsync(List<RepoWalletPermission> permissions)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(ExternalWalletPermissionsQueries.InsertExternalWalletPermission, permissions, tx);
        }, "A database error occurred while inserting wallet permissions.");
    }

    public async Task<RepoWalletPermission> SaveAsync(RepoWalletPermission walletPermission)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(ExternalWalletPermissionsQueries.UpdateExternalWalletPermission, walletPermission, tx);
            return walletPermission;
        }, "A database error occurred while updating wallet permission.");
    }

    public async Task<List<RepoWalletPermission>> FindAllActiveByWalletIdAndExternalWalletIdAsync(Guid walletId, Guid externalWalletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            if (externalWalletId == Guid.Empty)
            {
                var response = await conn.QueryAsync<RepoWalletPermission>(
                    ExternalWalletPermissionsQueries.GetActiveWalletPermissions,
                    new { walletId },
                    tx);
                return response.ToList();
            }
            else
            {
                var response = await conn.QueryAsync<RepoWalletPermission>(
                    ExternalWalletPermissionsQueries.GetActiveWalletPermissionsForSpecificWallet,
                    new { walletId, externalWalletId },
                    tx);
                return response.ToList();
            }
        }, "A database error occurred while fetching active wallet permissions.");
    }

    public async Task<List<RepoWalletPermission>> FindAllByExpiredAndWalletIdAndExternalWalletId(Guid walletId, Guid externalWalletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            if (externalWalletId == Guid.Empty)
            {
                var response = await conn.QueryAsync<RepoWalletPermission>(
                    ExternalWalletPermissionsQueries.GetAllExpiredWalletPermissions,
                    new { walletId },
                    tx);
                return response.ToList();
            }
            else
            {
                var response = await conn.QueryAsync<RepoWalletPermission>(
                    ExternalWalletPermissionsQueries.GetAllExpiredWalletPermissionsForSpecificWallet,
                    new { walletId, externalWalletId },
                    tx);
                return response.ToList();
            }
        }, "A database error occurred while fetching all wallet permissions.");
    }
}
