﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Represents a request to set user permissions for a specific wallet.
/// </summary>
[DataContract]
public class SetWalletUserPermissionsRequest : RequiresOtp
{
    /// <summary>
    /// The wallet identifier for which permissions are being set.
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// The list of user permissions to set for the wallet.
    /// </summary>
    [DataMember(Name = "permissions")]
    public List<UserPermission> Permissions { get; set; }
}
