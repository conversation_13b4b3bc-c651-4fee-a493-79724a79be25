﻿<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>true</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Configurations>Debug;Release;Sandbox</Configurations>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <DebugSymbols>false</DebugSymbols>
    </PropertyGroup>

    <ItemGroup>
        <_WebToolingArtifacts Remove="Properties\PublishProfiles\SandboxProduction.pubxml" />
        <_WebToolingArtifacts Remove="Properties\PublishProfiles\SandboxQA.pubxml" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
        <PackageReference Include="Nbg.AspNetCore.Identity.iBank.OAuth2" Version="8.0.4" />
        <PackageReference Include="Nbg.AspNetCore.ProxyMiddleware" Version="8.1.1" />
        <PackageReference Include="Nbg.AspNetCore.ProxyMiddleware.Transform" Version="8.1.1" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Controllers" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Extensions" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Implementation" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Interfaces" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Types" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.Configuration.Extensions" Version="9.0.4" />
        <PackageReference Include="Nbg.NetCore.Data" Version="8.0.3" />
        <PackageReference Include="Nbg.NetCore.Healthchecks" Version="8.0.3" />
        <PackageReference Include="Nbg.NetCore.iBank.Profile.Implementation" Version="8.0.4" />
        <PackageReference Include="Nbg.NetCore.Proxy.Utilities" Version="8.0.1" />
        <PackageReference Include="Nbg.NetCore.SqlServer.ConfigurationProvider" Version="8.0.5" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
        <PackageReference Include="NLog.WindowsEventLog" Version="5.3.4" />
        <PackageReference Include="NWebsec.AspNetCore.Middleware" Version="3.0.0" />
        <!--<PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />-->
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Api.Core.Types\Nbg.Ewallet.Api.Types.csproj" />
        <ProjectReference Include="..\..\..\Apis\Sandbox\Nbg.Ewallet.Api.Sandbox.Types\Nbg.Ewallet.Api.Sandbox.Types.csproj" />
    </ItemGroup>

</Project>