﻿using System.Runtime.Serialization;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// SubscriptionPlan definition
/// </summary>
public class SandboxSubscriptionPlan
{
    /// <summary>
    /// Id of the SubscriptionPlan
    /// </summary>
    [DataMember(Name = "id")]
    public SubscriptionTier Id { get; set; }

    /// <summary>
    /// Defined discounts per transaction type
    /// </summary>
    [DataMember(Name = "discounts")]
    public List<DiscountTransaction> discounts { get; set; }
}
