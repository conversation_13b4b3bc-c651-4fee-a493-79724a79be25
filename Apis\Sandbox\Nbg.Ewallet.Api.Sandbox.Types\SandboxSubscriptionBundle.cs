﻿using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// SubscriptionPlan definition
/// </summary>
public class SandboxSubscriptionBundle
{
    /// <summary>
    /// The transaction type that discounts will be applied to.
    /// </summary>
    [DataMember(Name = "transactionType")]
    public SubscriptionTransactionType TransactionType { get; set; }

    /// <summary>
    /// The number of discounted transaction based on the agreed PlanId (Basic, Premium, etc.).
    /// When this number is consumed, the tranasction commission will default to the IB commissions.
    /// </summary>
    [DataMember(Name = "value")]
    public int Value { get; set; }
}
