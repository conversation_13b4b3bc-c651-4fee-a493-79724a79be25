﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.AspNetCore.Http.Extensions;
using Nbg.AspNetCore.Identity;
using Nbg.AspNetCore.ProxyMiddleware;

namespace nbg.ewallet.proxy.api;

public class CorporateUserValidationPlugin : IAuthorizationPlugin
{
    private List<string> _allowedRoles = ["PERSONAL_COMPANY"];
    private readonly CorporateUserAuthorizationPolicySettings _policySettings;
    private readonly IIdentityService _identityService;
    private readonly ILogger<CorporateUserValidationPlugin> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CorporateUserValidationPlugin(
        IIdentityService identityService,
        ILogger<CorporateUserValidationPlugin> logger,
        IOptions<CorporateUserAuthorizationPolicySettings> policySettings,
        IHttpContextAccessor httpContextAccessor)
    {
        _policySettings = policySettings.Value;
        _identityService = identityService;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task InvokeAsync(ProxyContext context, Func<Task> next)
    {
        if (!_policySettings.ApplyPolicy)
        {
            await next();
            return;
        }

        var isCorporateUser = _identityService.HasRole("CORPORATE");
        var userAuthorizationLevel = _identityService.ClaimValue("authorization_level") ?? string.Empty;
        var userNumberOfApprovals = _identityService.ClaimValue<int>("number_of_approvals");
        var roles = _httpContextAccessor.HttpContext.User?.Claims?.Where(x => x.Type == "Roles").Select(x => x.Value).ToList();
        //if corporate user,check user auth level to procced
        var canAccessEndpoint = isCorporateUser
            && _policySettings.AllowAuthorizationLevel.Contains(string.Concat(userAuthorizationLevel, userNumberOfApprovals));

        foreach (var role in _allowedRoles)
            if (_identityService.HasRole(role) || roles.Contains(role)) canAccessEndpoint = true;

        if (canAccessEndpoint)
        {
            await next();
            return;
        }

        _logger.LogError("User does not have any of the allowed roles or does not meet the requirements");
        await context.HttpContext.Response.WriteJsonAsync(HttpStatusCode.Unauthorized, new
        {
            desc = "Η συγκεκριμένη υπηρεσία είναι διαθέσιμη μόνο σε νομικά πρόσωπα."
        });
    }
}
