﻿using System;
using System.Threading.Tasks;
using ibank.ThirdParty.Types;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Api.Implementation.Extensions;

public static class PaymentResponseExtensions
{
    public static PaymentsResponse CreateEmptyResponse(this BatchPaymentsRequest request)
    {
        return new PaymentsResponse { Payments = [], BatchId = request?.BatchId ?? Guid.Empty, };
    }

    public static async Task HandleResultAsync(this Response<PaymentResponse> response, RepoTransaction transaction, ITransactionRepositoryService transactionRepositoryService)
    {
        if (response.Exception != null)
        {
            transaction.UpdateTransactionFailure(response.Exception.Description);
            _ = await transactionRepositoryService.SaveAsync(transaction);
            response.Exception.Description.ThrowExceptionDescription();
        }

        transaction.UpdateTransactionSuccess();
        transaction.UpdateCreditorDetails(response.Payload.Creditor);
        transaction.Reference = response.Payload.PaymentIdentification.ClrSysRef;
        transaction.Commission = response.Payload.CommissionInfo.Total;
        await transactionRepositoryService.SaveAsync(transaction);
    }

    public static void ThrowExceptionDescription(this string exceptionDesc)
    {
        throw exceptionDesc switch
        {
            _ when string.IsNullOrEmpty(exceptionDesc) => new GenericException(),
            _ when exceptionDesc.Contains("Invalid paymentOrgIdentificationId", StringComparison.OrdinalIgnoreCase) => new InvalidOrganizationIdException(),
            _ when exceptionDesc.Contains("ΑΝΕΠΑΡΚΕΣ ΥΠΟΛΟΙΠΟ", StringComparison.OrdinalIgnoreCase) => new InsufficientBalanceException(),
            _ when exceptionDesc.Contains("ΛΑΘΟΣ ΚΩΔΙΚΟΣ ΠΛΗΡΩΜΗΣ Ή ΠΟΣΟ", StringComparison.OrdinalIgnoreCase) => new InvalidPaymentCodeOrAmountException(),
            _ => new PaymentExecutionExceptionInput(exceptionDesc),
        };
    }
}
