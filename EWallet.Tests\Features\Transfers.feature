Feature: Transfer between Wallets

#Scenario Outline: As an internal wallet user I want to transfer an amount to another wallet
#	Given I have two registered wallets
#    And an internal user of the second wallet with <userPermission>
#    Then I make a Transfer from the second wallet to the first
#    
#    Examples:
#         | walletName              | externalWalletName | userPermission |
#         | first registered wallet |                    | { "userId": "Companyuserid1", "admin": true, "approve": true, "balanceView": true, "submit": true, "transactionView": true, "limits": [ { "transactionType": "WalletToWallet", "amount": 1000.0, "timeframe": "YEARLY" } ] } |
#         | second registered wallet | 6e305f28-16f4-4a2f-9c8d-84888bca3177 |{ "userId": "Companyuserid1", "admin": true, "approve": "LIMIT", "balanceView": true, "submit": true, "transactionView": true, "limits": [ { "transactionType": "WalletToWallet", "amount": 500.0, "timeframe": "YEARLY" } ] }  |
         
        
