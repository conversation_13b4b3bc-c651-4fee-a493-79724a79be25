namespace nbg.ewallet.repository.dbQueries;

internal static class TransactionQueries
{
    internal const string GetTransactionById =
        "SELECT [TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[<PERSON><PERSON><PERSON>cy]," +
        "[DebtorIban],[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status]," +
        "[Result],[ResultReason],[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur]," +
        "[RequestJson],[Commission],[RejectedBy],[IsInstant] " +
        "FROM [EWallet].[dbo].[Transactions] " +
        "WHERE TransactionId = @TransactionId";

    internal const string GetTransactionByWalletIdAndTransactionSubTypeAndTransactionDate =
        "SELECT [TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[Currency]," +
        "[DebtorIban],[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status]," +
        "[Result],[ResultReason],[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur]," +
        "[RequestJson],[Commission],[RejectedBy],[IsInstant] " +
        "FROM [EWallet].[dbo].[Transactions] " +
        "WHERE WalletId = @WalletId " +
        "AND TransactionSubType = @TransactionSubType " +
        "AND Status = 'EXECUTED' " +
        "AND Month(TransactionDate) = Month(GETDATE()) " +
        "AND Year(TransactionDate) = Year(GETDATE()) " +
        "AND TransactionDate >= @DateFrom " +
        "AND TransactionDate < @DateTo";

    internal const string GetTransactionByWalletIdAndTransactionSubTypeAndSubmittedBy =
        "SELECT [TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[Currency]," +
        "[DebtorIban],[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status]," +
        "[Result],[ResultReason],[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur]," +
        "[RequestJson],[Commission],[RejectedBy],[IsInstant] " +
        "FROM [EWallet].[dbo].[Transactions] " +
        "WHERE WalletId = @WalletId " +
        "AND TransactionSubType in @TransactionSubType " +
        "AND SubmittedBy = @UserId";

    internal const string GetTransactionByWalletIdAndTransactionSubTypeAndExecutedBy =
        "SELECT [TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[Currency]," +
        "[DebtorIban],[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status]," +
        "[Result],[ResultReason],[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur]," +
        "[RequestJson],[Commission],[RejectedBy],[IsInstant] " +
        "FROM [EWallet].[dbo].[Transactions] " +
        "WHERE WalletId = @WalletId " +
        "AND TransactionSubType in @TransactionSubType " +
        "AND (ExecutedBy = @UserId OR ApprovedBy = @UserId)";

    internal const string GetTransactionByWalletIdAndTransactionId =
        "SELECT [TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[Currency]," +
        "[DebtorIban],[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status]," +
        "[Result],[ResultReason],[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur]," +
        "[RequestJson],[Commission],[RejectedBy],[IsInstant] " +
        "FROM [EWallet].[dbo].[Transactions] " +
        "WHERE WalletId = @WalletId " +
        "AND TransactionId = @TransactionId";

    internal const string GetTransactionByWalletId =
        "SELECT [TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[Currency]," +
        "[DebtorIban],[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status]," +
        "[Result],[ResultReason],[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur]," +
        "[RequestJson],[Commission],[RejectedBy],[IsInstant] " +
        "FROM [EWallet].[dbo].[Transactions] " +
        "WHERE WalletId = @WalletId";

    internal const string AndWithBatchId = " AND BatchId = @BatchId";
    internal const string AndWithoutBatchId = " AND BatchId IS NULL"; // BatchId = null will not work
    internal const string AndStatus = " AND Status = @Status";
    internal const string OrderByCreatedAt = " ORDER BY CreatedAt ASC";
    internal const string SkipTake = " OFFSET @Skip ROWS FETCH NEXT @Take ROWS ONLY";

    internal const string GetTransactionsByWalletIdAndUserIdInApprovedExecutedSubmitted =
        "SELECT [TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[Currency]," +
        "[DebtorIban],[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status]," +
        "[Result],[ResultReason],[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur]," +
        "[RequestJson],[Commission],[RejectedBy],[IsInstant] " +
        "FROM [EWallet].[dbo].[Transactions] " +
        "WHERE WalletId = @WalletId " +
        "AND (ExecutedBy = @UserId " +
        "OR ApprovedBy = @UserId " +
        "OR SubmittedBy = @UserId " +
        "OR RejectedBy = @UserId)";

    internal const string InsertTransaction =
        "INSERT INTO [EWallet].[dbo].[Transactions] " +
        "([TransactionId],[CreatedAt],[UpdatedAt],[Amount],[TransactionType],[TransactionSubType],[Currency],[DebtorIban]," +
        "[DebtorName],[Reference],[CreditorIban],[CreditorName],[TransactionDate],[Reason],[Status],[Result],[ResultReason]," +
        "[BatchId],[WalletId],[SubmittedBy],[ApprovedBy],[ExecutedBy],[ExecutedAs],[Valeur],[RequestJson],[Commission]) " +
        "VALUES " +
        "(@TransactionId,@CreatedAt,@UpdatedAt,@Amount,@TransactionType,@TransactionSubType,@Currency,@DebtorIban," +
        "@DebtorName,@Reference,@CreditorIban,@CreditorName,@TransactionDate,@Reason,@Status,@Result,@ResultReason," +
        "@BatchId,@WalletId,@SubmittedBy,@ApprovedBy,@ExecutedBy,@ExecutedAs,@Valeur,@RequestJson,@Commission)";

    internal const string UpdateTransaction =
       @"Update [EWallet].[dbo].[Transactions]
                SET
                [CreatedAt] = @CreatedAt,
                [UpdatedAt] = @UpdatedAt,
                [Amount] = @Amount,
                [TransactionType] = @TransactionType,
                [TransactionSubType] = @TransactionSubType,
                [Currency] = @Currency,
                [DebtorIban] = @DebtorIban,
                [DebtorName] = @DebtorName,
                [Reference] = @Reference,
                [CreditorIban] = @CreditorIban,
                [CreditorName] = @CreditorName,
                [TransactionDate] = @TransactionDate,
                [Reason] = @Reason,
                [Status] = @Status,
                [Result] = @Result,
                [ResultReason] = @ResultReason,
                [BatchId] = @BatchId,
                [WalletId] = @WalletId,
                [SubmittedBy] = @SubmittedBy,
                [ApprovedBy] = @ApprovedBy,
                [ExecutedBy] = @ExecutedBy,
                [ExecutedAs] = @ExecutedAs,
                [RequestJson] = @RequestJson,
                [RejectedBy] = @RejectedBy,
                [Commission] = @Commission,
                [Valeur] = @Valeur,
                [IsInstant] = @IsInstant
           WHERE TransactionId = @TransactionId";
}
