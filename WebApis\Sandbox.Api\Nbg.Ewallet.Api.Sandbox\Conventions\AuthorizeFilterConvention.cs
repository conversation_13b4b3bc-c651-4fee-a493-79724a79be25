﻿using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.Authorization;

namespace Nbg.Ewallet.Api.Sandbox.conventions;

public class AuthorizeFilterConvention : IActionModelConvention
{
    private readonly List<string> _managementControllers = ["Management"];

    private readonly List<string> _clientCredsActions = ["SandboxController`1", "Subscriptions", "Consents"];

    private readonly List<string> _noAuthActions = [];

    public void Apply(ActionModel actionModel)
    {
        var controllerName = actionModel.Controller.ControllerName;
        var actionName = actionModel.ActionName;

        if (_managementControllers.Contains(controllerName))
        {
            actionModel.Filters.Add(new AuthorizeFilter("ManagementPolicy"));
        }
        else if (_clientCredsActions.Contains(controllerName))
        {
            actionModel.Filters.Add(new AuthorizeFilter("ClientCredentialsPolicy"));
        }
        else if (_noAuthActions.Contains(actionName))
        {
        }
        else
        {
            actionModel.Filters.Add(new AuthorizeFilter("AuthCodePolicy"));
        }
    }
}
