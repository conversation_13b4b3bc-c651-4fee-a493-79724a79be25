﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Terms and conditions
/// </summary>
public class Terms
{
    /// <summary>
    /// Title
    /// </summary>
    [DataMember(Name = "title")]
    public string title { get; set; }

    /// <summary>
    /// Subject
    /// </summary>
    [DataMember(Name = "subject")]
    public string subject { get; set; }

    /// <summary>
    /// Subject
    /// </summary>
    [DataMember(Name = "version")]
    public string version { get; set; }
}
