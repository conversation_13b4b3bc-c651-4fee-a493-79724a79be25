using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CreateSubscriptionRequest = Nbg.Ewallet.Api.Types.Subscriptions.CreateSubscriptionRequest;

namespace Nbg.Ewallet.Api.Implementation;

public class WalletSubscriptionService : IWalletSubscriptionService
{
    private readonly ILogger<WalletSubscriptionService> _logger;
    private readonly IMapper _mapper;
    private readonly ISubscriptionUsageCalculatorService _subscriptionUsageCalculatorService;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly ISubscriptionApiClientService _subscriptionApiClientService;

    public WalletSubscriptionService(
        ILogger<WalletSubscriptionService> logger,
        IMapper mapper,
        ISubscriptionUsageCalculatorService subscriptionUsageCalculatorService,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletRepositoryService walletRepositoryService,
        ISubscriptionApiClientService subscriptionApiClientService
    )
    {
        _logger = logger;
        _mapper = mapper;
        _subscriptionUsageCalculatorService = subscriptionUsageCalculatorService;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletRepositoryService = walletRepositoryService;
        _subscriptionApiClientService = subscriptionApiClientService;
    }

    public async Task<SubscriptionResponse> ExtendSubscription(CreateSubscriptionRequest request)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        SubscriptionApiResponse subscription = await _subscriptionApiClientService.UpdateSubscriptionAsync(new UpdateSubscriptionRequest
        {
            SubscriptionId = myWallet.ActiveSubscriptionId,
            Tier = request.Tier,
        });

        return subscription.ToSubscriptionResponse();
    }

    public async Task<SubscriptionResponse> GetWalletSubscription(Guid walletid)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        SubscriptionApiResponse subscription = await _subscriptionApiClientService.GetSubscriptionAsync(myWallet.ActiveSubscriptionId);
        return subscription.ToSubscriptionResponse();
    }

    public async Task<SubscriptionUsage> GetSubscriptionUsage(Guid walletid)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        var res = await _subscriptionUsageCalculatorService.CalculateSubscriptionUsageAsync(walletid);
        return res;
    }

    public async Task<SubscriptionResponse> OptOutSubscription()
    {

        var userId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        SubscriptionApiResponse subscription = await _subscriptionApiClientService.OptOutSubscriptionAsync(new Repository.Types.SubscriptionApi.OptOutSubscriptionRequest
        {
            SubscriptionId = myWallet.ActiveSubscriptionId,
        });
        return subscription.ToSubscriptionResponse();
    }
}
