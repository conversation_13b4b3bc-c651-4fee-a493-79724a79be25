﻿namespace nbg.ewallet.repository.dbQueries;

internal static class ProfileQueries
{
    internal const string GetActiveUserPermissions =
        "SELECT Id, UserId, WalletId, Submit, BalanceView, TransactionView, Admin, ExpirationDate, CreatedAt, AssignedBy, RevokedBy, " +
        "Approve, InheritsAuthorizations " +
        "FROM [EWallet].[dbo].[UserPermissions] " +
        "WHERE UserId = @userid " +
        "AND ExpirationDate > GETDATE() " +
        "ORDER BY ExpirationDate ASC";

    internal const string GetUserPermissionsWithWalletId =
        "SELECT  Id, UserId, WalletId, Submit, BalanceView, TransactionView, Admin, ExpirationDate, CreatedAt, AssignedBy, RevokedBy, " +
        "Approve, InheritsAuthorizations " +
        "FROM [EWallet].[dbo].[UserPermissions] " +
        "WHERE UserId = @userid " +
        "AND WalletId = @walletId " +
        "AND ExpirationDate > GETDATE() " +
        "ORDER BY ExpirationDate ASC";

    // add revoke checks
    internal const string GetWalletPermissions =
        "SELECT [Id],[WalletId],[ExternalWalletId] AS TargetWalletId,[Submit],[BalanceView],[TransactionView],[ExpirationDate]," +
        "[CreationDate] AS CreatedAt,[AssignedBy],[RevokedBy],[Approve] " +
        "FROM [EWallet].[dbo].[WalletPermissions] " +
        "WHERE ExternalWalletId = @TargetWalletId " +
        "AND WalletId = @WalletId";

    internal const string GetActiveByExternalWalletId =
        "SELECT [Id],[WalletId],[ExternalWalletId] AS TargetWalletId,[Submit],[BalanceView],[TransactionView],[ExpirationDate]," +
        "[CreationDate] AS CreatedAt,[AssignedBy],[RevokedBy],[Approve] " +
        "FROM [EWallet].[dbo].[WalletPermissions] " +
        "WHERE  ExternalWalletId = @ExternalWalletId " +
        "AND ExpirationDate > GETDATE() " +
        "ORDER BY ExpirationDate ASC";

    internal const string GetUserPermissionByUserIdAndWalletId =
        "SELECT Id, UserId, WalletId, Submit, BalanceView, TransactionView, Admin, ExpirationDate, CreatedAt, AssignedBy, RevokedBy, " +
        "Approve, InheritsAuthorizations " +
        "FROM [EWallet].[dbo].[UserPermissions] " +
        "WHERE UserId = @userid AND WalletId = @walletId " +
        "AND ExpirationDate > GETDATE() " +
        "ORDER BY ExpirationDate ASC";
}
