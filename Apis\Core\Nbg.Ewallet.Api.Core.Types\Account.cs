﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// User account
/// </summary>
[DataContract]
public class Account
{
    /// <summary>
    /// Account IBAN
    /// </summary>
    [DataMember(Name = "iban")]
    public string Iban { get; set; }

    /// <summary>
    /// Account currency ISO code
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>
    /// Account description
    /// </summary>
    [DataMember(Name = "description")]
    public string Description { get; set; }
}
