﻿using System.Threading.Tasks;
using AutoMapper;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Services;

public class TermsSandboxService : ITermsService
{
    private ISandBoxRepositoryService _sandboxRepositoryService;
    private readonly IMapper _mapper;

    public TermsSandboxService(ISandBoxRepositoryService sandboxRepositoryService,
        IMapper mapper)
    {
        _sandboxRepositoryService = sandboxRepositoryService;
        _mapper = mapper;
    }

    public async Task<Terms> GetTerms()
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();
        if (model.Terms == null)
        {
            throw new GenericException();
        }

        return _mapper.Map<Terms>(model.Terms);
    }
}
