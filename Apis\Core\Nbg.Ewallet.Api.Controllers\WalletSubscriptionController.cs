using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Subscriptions;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// This Controller Provides Wallet Subscription functionality.
/// </summary>
[ApiController]
[ApiExplorerSettings(GroupName = "Wallet")]
[Route("wallet")]
[Produces("application/json")]
[Consumes("application/json")]
public class WalletSubscriptionController : ControllerBase
{
    private readonly IWalletSubscriptionService _walletSubscriptionService;

    public WalletSubscriptionController(IWalletSubscriptionService walletSubscriptionService)
    {
        _walletSubscriptionService = walletSubscriptionService;
    }

    /// <summary>
    /// Registers a new wallet.
    /// </summary>
    /// <remarks>
    /// Registers a new wallet by providing a name for the wallet. This name can be used when searching for other wallets.
    /// A GUID is generated and connected to the newly created wallet, uniquely identifying it.
    /// Can be called from any user, since there are is not wallet yet, and therefore no permissions assigned.
    /// </remarks>
    /// <param name="request">The wallet registration request.</param>
    /// <returns>The newly registered wallet.</returns>
    /// <response code="200">Returns the newly registered wallet.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="500">If there is an internal server error.</response>
    //[ProducesResponseType(typeof(Wallet), 200)]
    //[ProducesResponseType(typeof(ErrorResponse), 400)]
    //[ProducesResponseType(typeof(ErrorResponse), 500)]
    //[HttpPost]
    //[Route("register", Name = "Register")]
    //public async Task<ActionResult<Wallet>> Register(WalletRegister request)
    //{
    //    return await _walletRegistrationService.Register(request);
    //}

    /// <summary>
    /// Creates the special purpose account in IB and connects it to the wallet.
    /// </summary>
    /// <remarks>
    /// This action will only succeed if there is an active, non-expired subscription for the wallet.
    /// Use the corresponding enpoint to create a subscription.
    /// If there i already a special purpose account connected with the wallet, this API call will fail.
    /// </remarks>
    /// <param name="walletId">The wallet identifier.</param>
    /// <returns>The created wallet account.</returns>
    /// <response code="200">Returns the created wallet account.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    //[HttpPost]
    //[Route("{walletId}/account", Name = "account")]
    //[EwalletAuthorize(AuthorizationTypes.Admin)]
    //[ProducesResponseType(typeof(Wallet), 200)]
    //[ProducesResponseType(typeof(ErrorResponse), 400)]
    //[ProducesResponseType(typeof(ErrorResponse), 401)]
    //[ProducesResponseType(typeof(ErrorResponse), 403)]
    //[ProducesResponseType(typeof(ErrorResponse), 500)]
    //public async Task<ActionResult<Wallet>> Create([FromRoute] Guid walletId)
    //{
    //    return await _walletRegistrationService.CreateAccountAndConnetToWallet(walletId);
    //}

    /// <summary>
    /// Updates an existing subscription.
    /// </summary>
    /// <remarks>
    /// Updates an existing subsciption.
    /// The new subscription can be of a different Plan Id than the current one.
    /// </remarks>
    /// <param name="request">The subscription update request.</param>
    /// <returns>The response of the subscription.</returns>
    /// <response code="200">Returns the created subscription response.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [HttpPost]
    [Route("{walletId}/subscription", Name = "subscription")]
    [EwalletAuthorize(AuthorizationTypes.Admin)]
    [ProducesResponseType(typeof(SubscriptionResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    public async Task<ActionResult<SubscriptionResponse>> CreateSubscription(CreateSubscriptionRequest request)
    {
        return await _walletSubscriptionService.ExtendSubscription(request);
    }

    /// <summary>
    /// Subscription opt out
    /// </summary>
    /// <remarks>
    /// Subscription opt out.
    /// If the user decides not to continue with the subscription plan.
    /// </remarks>
    /// <param name="request">The subscription opt out request.</param>
    /// <returns>The response of the subscription.</returns>
    /// <response code="200">Returns the created subscription response.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [HttpPost]
    [Route("{walletId}/subscription/opt-out", Name = "subscription-opt-out")]
    [EwalletAuthorize(AuthorizationTypes.Admin)]
    [ProducesResponseType(typeof(SubscriptionResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    public async Task<ActionResult<SubscriptionResponse>> OptOutSubscription()
    {
        return await _walletSubscriptionService.OptOutSubscription();
    }

    /// <summary>
    /// Returns the active subscription for the wallet.
    /// Will return an error message in case that no active subscription is found.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <returns>The subscription details.</returns>
    /// <response code="200">Returns the subscription details.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="404">If the subscription is not found.</response>
    /// <response code="500">If there is an internal server error.</response>
    [HttpGet]
    [Route("{walletId}/subscription", Name = "GetSubscription")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    [ProducesResponseType(typeof(SubscriptionResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 404)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    public async Task<ActionResult<SubscriptionResponse>> GetSubscription(Guid walletId)
    {
        return await _walletSubscriptionService.GetWalletSubscription(walletId);
    }

    /// <summary>
    /// Returns the current subscription usage, in terms of discount transactions consumption.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <returns>The subscription usage details.</returns>
    /// <response code="200">Returns the subscription usage details.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="404">If the subscription usage is not found.</response>
    /// <response code="500">If there is an internal server error.</response>
    [HttpGet]
    [Route("{walletId}/subscription/usage", Name = "GetSubscriptionUsage")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    [ProducesResponseType(typeof(SubscriptionUsage), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 404)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    public async Task<ActionResult<SubscriptionUsage>> GetSubscriptionUsage(Guid walletId)
    {
        return await _walletSubscriptionService.GetSubscriptionUsage(walletId);
    }
}
