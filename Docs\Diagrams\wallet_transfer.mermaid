sequenceDiagram
    autonumber
    Client->>EWallet.Proxy: /transfers/{walletId}/transfer POST
    EWallet.Proxy->>EWallet.Core: /transfers/{walletId}/trasfer POST
    EWallet.Core->>EWallet.DB: SELECT
    note over EWallet.DB: DB Table:  Wallets
    EWallet.DB->>EWallet.Core: {Wallet}
    EWallet.Core->>EWallet.DB: CHECK Submit/Approval Limit
    note over EWallet.DB: DB Table:  Limit, Transactions
    EWallet.Core->>CICS: Update Customer Commission
    EWallet.Core->>Accounts API:  {Transfers Request}
    Accounts API->>EWallet.Core: {Transfers Response}
    EWallet.Core->>EWallet.Proxy: {Extended Transfer Response}
    EWallet.Proxy->>Client: {Extended Transfer Response}