﻿using System;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Nbg.AspNetCore.Http.Extensions;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Utilities;
using Nbg.NetCore.CosmosConnector.Implementation;
using Xunit;

namespace Nbg.Ewallet.Repository.Test.Integrations;

public class IBANAccountUtilityTest : IClassFixture<IBANAccountUtilityTestFixture>
{
    private readonly IBANAccountUtilityTestFixture _fixture;
    private const string TestIban = "GR77011054700000**********2"; // Example IBAN for testing
    private const string TestAccount = "**********"; // Example Account for testing
    public IBANAccountUtilityTest(IBANAccountUtilityTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task TestGetIBANAccountAsync()
    {
        var ibanAccountUtility = _fixture.ServiceProvider.GetRequiredService<IIbanAccountUtility>();

        var account = await ibanAccountUtility.GetAccountFromIbanAsync(TestIban);
        Assert.NotNull(account);
        Assert.Equal(TestAccount, account);
    }

    [Fact]
    public async Task TestGetIBANFromAccountAsync()
    {
        var ibanAccountUtility = _fixture.ServiceProvider.GetRequiredService<IIbanAccountUtility>();
        var iban = await ibanAccountUtility.GetIbanFromAccountAsync(TestAccount);
        Assert.NotNull(iban);
        Assert.Equal(TestIban, iban);
    }

    [Fact]
    public void TestConfigurationIsLoaded()
    {
        Assert.NotNull(_fixture.Configuration);
        Assert.NotEmpty(_fixture.Configuration.GetChildren());
    }
    [Fact]
    public void TestServiceProviderIsNotNull()
    {
        Assert.NotNull(_fixture.ServiceProvider);
    }
}

public class IBANAccountUtilityTestFixture
{
    public IServiceProvider ServiceProvider { get; }

    public IConfigurationRoot Configuration { get; }

    public IBANAccountUtilityTestFixture()
    {
        ServiceCollection services = new ServiceCollection();
        Configuration = CreateConfigurationRoot();

        services.AddLogging(builder =>
        {
            builder.AddConsole(); // Enables console output
            builder.SetMinimumLevel(LogLevel.Trace); // Capture all logs (Trace and above)
        });

        services.AddMemoryCache();
        services.AddSingleton<IIbanAccountUtility, IbanAccountUtility>();
        services.AddSingleton<IConfiguration>(Configuration);
        services.AddAceConnector(Configuration, "AceConnectorOptions");
        services.AddScoped<IMainFrameConnector, CosmosConnector>();
        services.AddScoped<IAuditableAceConnector, AuditableAceConnector>();
        var serviceAuditRepositoryMock = new Mock<IServiceAuditRepositoryService>();
        services.AddScoped<IServiceAuditRepositoryService>(_ => serviceAuditRepositoryMock.Object);
        services.AddHttpClient("accountsApi", "HttpClient:accountsApi");

        var httpContextRepositoryServiceMock = new Mock<IHttpContextRepositoryService>();
        services.AddScoped<IHttpContextRepositoryService>(_ => httpContextRepositoryServiceMock.Object);

        ServiceProvider = services.BuildServiceProvider();
    }

    public static IConfigurationRoot CreateConfigurationRoot()
    {
        return new ConfigurationBuilder()
                    .SetBasePath(LocalPath)
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .Build();
    }

    public static string LocalPath => Path.GetDirectoryName(new Uri(Assembly.GetExecutingAssembly().Location).LocalPath);
}
