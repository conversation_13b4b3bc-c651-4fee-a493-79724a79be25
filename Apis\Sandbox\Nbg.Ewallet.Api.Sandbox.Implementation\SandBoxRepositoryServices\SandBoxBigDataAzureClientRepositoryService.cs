﻿using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandBoxBigDataAzureClientRepositoryService : IBigDataAzureClientService
{
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;

    public SandBoxBigDataAzureClientRepositoryService(ISandBoxRepositoryService sandBoxRepositoryService)
    {
        _sandBoxRepositoryService = sandBoxRepositoryService;
    }

    public async Task<AzureStatementsResponse> GetStatementsAsync(AzureStatementsRequest request)
    {
        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        var transactionsQuery = sandbox
            .Transactions
            .Where(x => x.Status == TransactionStatus.EXECUTED);

        transactionsQuery = transactionsQuery
            .Where(x => AccountHelpers.IbanToAccount(x.DebitIban) == request.Account
            || AccountHelpers.IbanToAccount(x.CreditIban) == request.Account);

        if (!string.IsNullOrWhiteSpace(request.DateFrom)
            && DateTime.TryParseExact(request.DateFrom, "dd.MM.yyyy", null, DateTimeStyles.None, out var dateFrom))
        {
            transactionsQuery = transactionsQuery.Where(x => x.Timestamp >= dateFrom);
        }

        if (!string.IsNullOrWhiteSpace(request.DateTo)
            && DateTime.TryParseExact(request.DateTo, "dd.MM.yyyy", null, DateTimeStyles.None, out var dateTo))
        {
            transactionsQuery = transactionsQuery.Where(x => x.Timestamp < dateTo);
        }

        var transactionsList = transactionsQuery
            .OrderBy(x => x.Timestamp)
            .ToList(); // materialize here before mutation

        var serialNumber = 1;
        foreach (var tx in transactionsList)
        {
            tx.SerialNum = (serialNumber++).ToString();
        }

        if (!string.IsNullOrWhiteSpace(request.LastRowKey))
        {
            var index = transactionsList.FindIndex(x => x.TransactionId.ToString() == request.LastRowKey);
            transactionsList = transactionsList.Skip(index >= 0 ? index + 1 : 0).ToList();
        }

        if (request.Limit.HasValue && transactionsList.Count > request.Limit.Value)
        {
            transactionsList = transactionsList.Take(request.Limit.Value).ToList();
        }

        var lastTransactionId = transactionsList.LastOrDefault()?.TransactionId;// ?? Guid.Empty;

        return new AzureStatementsResponse
        {
            Transactions = transactionsList.Select(x => ToStatement(x, request.Account)).ToList(),
            PaginationToken = lastTransactionId?.ToString(),
        };
    }

    private AzureStatementsTransaction ToStatement(SandboxTransaction transaction, string account)
    {
        return new AzureStatementsTransaction
        {
            Account = transaction.Account,
            AccountingBalance = transaction.AccountingBalance,
            AdditionalInfo = transaction.AdditionalInfo,
            Amount = transaction.Amount,
            AmountEquivalent = transaction.AmountEquivalent,
            Branch = transaction.Branch,
            CounterpartyBank = transaction.CounterpartyBank,
            CounterpartyName = transaction.CounterpartyName,
            CreditDebit = transaction.DebitIban == account ? "Debit" : "Credit",
            Currency = transaction.Currency,
            CustomerId = transaction.CustomerId,
            CustomerInfo = transaction.CustomerInfo,
            Date = transaction.TransactionDate.GetDottedDate(),
            Description = transaction.Description,
            ExternalSystem = transaction.ExternalSystem,
            Operation = transaction.Operation,
            OperationDesc = transaction.OperationDesc,
            ReasonInfo = transaction.ReasonInfo,
            Reference = transaction.Reference,
            RelatedAccount = transaction.RelatedAccount,
            RelatedName = transaction.RelatedName,
            SerialNum = int.Parse(transaction.SerialNum),
            Timestamp = transaction.Timestamp.ToString("O"),
            Trans = transaction.Trans,
            Valeur = transaction.TransactionDate.GetDottedDate()
        };
    }
}
