﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IWalletUserPermissionsService
{
    Task<UserPermissionResponse> SetWalletUserPermissions(SetWalletUserPermissionsRequest request);
    Task<UserPermissionResponse> GetWalletUserPermissions(Guid walletId, bool showAll);
    Task<List<UserPermission>> GetWalletPermissionForUser(Guid walletId, string userId, bool showAll);
    Task<UserPermission> RevokeUserPermissionsForUser(string userId);
    Task<AvailableUserResponse> GetOrganizationUsers(Guid walletid);
}
