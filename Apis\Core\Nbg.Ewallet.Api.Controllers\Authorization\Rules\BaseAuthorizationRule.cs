using System;
using Microsoft.AspNetCore.Mvc.Filters;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

public abstract class BaseAuthorizationRule : IAuthorizationRule
{
    public abstract void EnsureAuthorized(AuthorizationContext authContext, AuthorizationFilterContext context);

    /// <summary>
    /// Validates that the provided <paramref name="authContext"/> is not null and contains an active permission.
    /// Throws a <see cref="PermissionNotFoundException"/> if the validation fails.
    /// </summary>
    /// <param name="authContext">The authorization context to validate.</param>
    /// <exception cref="PermissionNotFoundException">Thrown when the authorization context or active permission is null.</exception>
    protected void CheckAuthorizationContext(AuthorizationContext authContext)
    {
        if (authContext?.ActivePermission == null) throw new PermissionNotFoundException();
    }

    /// <summary>
    /// Validates that the wallet ID in the route data matches the wallet ID in the active permission of the <paramref name="authContext"/>.
    /// Throws a <see cref="PermissionNotFoundException"/> if the validation fails.
    /// </summary>
    /// <param name="authContext">The authorization context containing the active permission.</param>
    /// <param name="context">The filter context containing the route data.</param>
    /// <exception cref="PermissionNotFoundException">
    /// Thrown when the route data is null, the wallet ID is missing or invalid, or the wallet IDs do not match.
    /// </exception>
    protected void CheckWalletId(AuthorizationContext authContext, AuthorizationFilterContext context)
    {
        if (context?.RouteData.Values is null) throw new PermissionNotFoundException();
        if (!context.RouteData.Values.TryGetValue("walletid", out var walletIdValue)) throw new PermissionNotFoundException();
        if (!Guid.TryParse(walletIdValue.ToString(), out var walletId)) throw new PermissionNotFoundException();
        if (walletId != authContext.ActivePermission.WalletId) throw new PermissionNotFoundException();
    }
}
