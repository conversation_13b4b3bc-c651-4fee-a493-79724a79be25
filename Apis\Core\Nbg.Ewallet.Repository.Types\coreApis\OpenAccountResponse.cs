﻿using System;
using System.Runtime.Serialization;

namespace Nbg.EWallet.Repository.Types.coreApis;

[DataContract]
public class OpenAccountResponse
{
    [DataMember(Name = "newAccountNumber")]
    public string NewAccountNumber { get; set; }

    [DataMember(Name = "openDate")]
    public DateTime OpenDate { get; set; }

    [DataMember(Name = "iban")]
    public string Iban { get; set; }

    [DataMember(Name = "customerFullName")]
    public string CustomerFullName { get; set; }
}
