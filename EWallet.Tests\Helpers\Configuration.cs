﻿//using Azure;
namespace SpecFlowConfiguration;

public class EnvironmentSettings
{
    public string Environment { get; set; }
    public Environments Environments { get; set; }

    #region

    public User GetUserId(string userName)
    {
        var index = Convert.ToInt32(userName);
        switch (Environment)
        {
            case "SandBox":
                {
                    var user = Environments.SandBox.Users[index];
                    return user;
                }
            case "Core":
                {
                    var user = Environments.Core.Users[index];
                    return user;
                }
            case "Production":
                {
                    var user = Environments.Production.Users[index];
                    return user;
                }
            case "SandBoxDirect":
                {
                    var user = Environments.SandBoxDirect.Users[index];
                    return user;
                }
            case "QA":
                {
                    var user = Environments.QA.Users[index];
                    return user;
                }
        }
        throw new Exception("User is not defined in the appsettings.json file");
    }

    public User GetUserId(int userName)
    {
        switch (Environment)
        {
            case "SandBox":
                {
                    var user = Environments.SandBox.Users[userName];
                    return user;
                }
            case "Core":
                {
                    var user = Environments.Core.Users[userName];
                    return user;
                }
            case "Production":
                {
                    var user = Environments.Production.Users[userName];
                    return user;
                }
            case "SandBoxDirect":
                {
                    var user = Environments.SandBoxDirect.Users[userName];
                    return user;
                }
            case "QA":
                {
                    var user = Environments.QA.Users[userName];
                    return user;
                }
        }
        throw new Exception("User is not defined in the appsettings.json file");
    }

    public string GetBaseAddress()
    {
        switch (Environment)
        {
            case "SandBox":
                {
                    return Environments.SandBox.BaseAddress;
                }
            case "Core":
                {
                    return Environments.Core.BaseAddress;
                }
            case "Production":
                {
                    return Environments.Production.BaseAddress;
                }
            case "SandBoxDirect":
                {
                    return Environments.SandBoxDirect.BaseAddress;
                }
            case "QA":
                {
                    return Environments.QA.BaseAddress;
                }
        }
        throw new Exception("User is not defined in the appsettings.json file");
    }
}

#endregion

public class Environments
{
    public EnvironmentDetails SandBox { get; set; }
    public EnvironmentDetails Core { get; set; }
    public EnvironmentDetails Production { get; set; }
    public EnvironmentDetails SandBoxDirect { get; set; }
    public EnvironmentDetails QA { get; set; }
}

public class EnvironmentDetails
{
    public string BaseAddress { get; set; }
    public List<User> Users { get; set; }
}

public class User
{
    public string userName { get; set; }
    public string passWord { get; set; }

    public string customerCode { get; set; }
    public string walletId { get; set; }
}
