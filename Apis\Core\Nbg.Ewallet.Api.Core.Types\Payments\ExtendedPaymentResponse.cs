﻿using System;
using System.Runtime.Serialization;
using ibank.ThirdParty.Types;

namespace Nbg.Ewallet.Api.Types.Payments;

/// <summary>
///
/// </summary>
public class ExtendedPaymentResponse : PaymentResponse
{
    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "requiresApproval")]
    public bool RequiresApproval { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "transactionId")]
    public Guid TransactionId { get; set; }

    /// <summary>Error reason</summary>
    [DataMember(Name = "errorReason")]
    public string ErrorReason { get; set; }
}

public class CommissionResponseBatch : CommissionResponse
{
    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "error")]
    public string Error { get; set; }

    [DataMember(Name = "transactionId")]
    public Guid? TransactionId { get; set; }

    public static CommissionResponseBatch ErrorFail(string errorMessage)
    {
        return new CommissionResponseBatch { Error = errorMessage };
    }

    public static CommissionResponseBatch CommissionSum()
    {
        return new CommissionResponseBatch
        {
            TransactionId = null,
            CommissionInfo = new()
        };
    }
}


