﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nbg.ewallet.repository.types;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.NetCore.CosmosConnector.Types.Constants;
using Nbg.NetCore.CosmosConnector.Types.Customer;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;
using Nbg.NetCore.HttpExceptions;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Implementation;

public class ValidationService : IValidationService
{
    private readonly ILogger<ValidationService> _logger;
    private readonly ValidationsControlPolicy _validationsControlSettings;
    //private const string ActiveFinancialProfileQuestionnaireType = "11000002";
    private readonly List<string> AuthCategories = ["A", "B", "C"];
    private const decimal AllowedApprovalsNum = 1;

    public ValidationService(
        ILogger<ValidationService> logger,
        IOptions<ValidationsControlPolicy> validationControlOptions)
    {
        _logger = logger;
        _validationsControlSettings = validationControlOptions.Value;
    }

    public async Task<CustomerData> ValidateCustomerDataAsync(
        Customer customerData,
        CustomerProductDetails customerProductDetails,
        CustomerAuthorizationLevel authCustomerData)
    {
        List<string> validationErrors = [];

        //TODO ENABLE IT
        if (_validationsControlSettings.ApplyPolicy)
        {
            _logger.LogDebug("{Customer}", JsonSerializer.Serialize(customerData));
            if (customerData.BasicInfo.DataCompleteness != CustomerDataCompletenessTypes.FullKnownCustomer
                && _validationsControlSettings.Controls.Contains("FullKnownCustomer"))
            {
                _logger.LogDebug("FullKnownCustomer is {DataCompleteness}", customerData.BasicInfo.DataCompleteness);
                validationErrors.Add("FullKnownCustomer");
            }

            if (!CheckActiveQuestionnaire(customerData)
                && _validationsControlSettings.Controls.Contains("FinancialProfile"))
            {
                _logger.LogDebug("FinancialProfile is false");
                validationErrors.Add("FinancialProfile");
            }

            if (customerData.Type == CustomerTypes.LegalEntity)
            {
                _logger.LogDebug("User is Corporate");
                if (_validationsControlSettings.Controls.Contains("LegalDoc") &&
                    !(customerData.RelatedParties != null &&
                      customerData.RelatedParties.Any(ur =>
                          ur.Relationship == RelationshipTypes.RealBeneficiaryRelationCode) &&
                      customerData.IdentificationDocuments != null &&
                      customerData.IdentificationDocuments.Any(ld =>
                          ld.DocumentType == LegalDocumentTypes.ActiveLegalDocType &&
                          ld.ExpiryDateDT > DateTime.UtcNow)))
                {
                    _logger.LogDebug("LegalDoc is false");
                    validationErrors.Add("LegalDoc");
                }

                _logger.LogDebug("{CustomerAuthorizationLevel}", JsonSerializer.Serialize(authCustomerData));
                if (authCustomerData?.Approvals != AllowedApprovalsNum ||
                    !AuthCategories.Contains(authCustomerData?.Category))
                    throw new UnauthorizedException("Η υπηρεσία δεν είναι διαθέσιμη για τον συγκεκριμένο χρήστη.");
            }
            else
            {
                if (!CheckActiveIdentity(customerData) &&
                    _validationsControlSettings.Controls.Contains("ActiveIdentity"))
                {
                    _logger.LogDebug("ActiveAdentity is false");
                    validationErrors.Add("ActiveIdentity");
                }

                //check "businessActivity": 1200002 gia atomiki epixeirish
                var isPersonalBusiness =
                    customerData.EmploymentDetails.Any(x => x.NatureOfEmployment.Clear() == BusinessActivity.PersonalCompany);
                if (!isPersonalBusiness)
                    throw new UnauthorizedException("Η υπηρεσία δεν είναι διαθέσιμη για τον συγκεκριμένο χρήστη.");
            }
        }

        _logger.LogDebug("{ValidationErrors}", JsonSerializer.Serialize(validationErrors));

        var hasMissingInformation = customerData.GeneralInfo != null &&
                        customerData.GeneralInfo.Any(gi =>
                            gi.InfoType == GeneralInfoCategoryTypes.IncompleteInformationCategoryCode
                            && gi.Value == GeneralInfoSubCategoryTypes.IncompleteInformationSubCategoryType);

        var isActive = CustomerCifStatusTypes.Active.Equals(customerData.BasicInfo.CifStatus, StringComparison.OrdinalIgnoreCase);

        const bool isFPbutNotBusiness = false;

        var response = new CustomerData
        {
            IsActive = isActive,
            HasMissingInformation = hasMissingInformation,
            isFPbutNotBusiness = isFPbutNotBusiness,
            Accounts = [],
            ValidationControls = validationErrors,
            isCorporate = customerData.Type == CustomerTypes.LegalEntity,
            VatNumber = customerData.BasicInfo.TaxIdentificationNumber,
            Name = customerData.BasicInfo.FullName ?? customerData.BasicInfo.CorporateName
        };

        if (customerProductDetails.ProductList == null || customerProductDetails.ProductList.Count == 0)
        {
            return response;
        }

        var accountList = customerProductDetails.ProductList
            .Where(a => IsValidProductCode(a.ProductType))
            .Select(a => new CustomerAccount
            {
                Iban = a.IBAN,
                Currency = a.AccountCurrency,
                Description = a.ProductTypeDescr
            })
            .ToList();

        response.Accounts = accountList;
        return response;
    }

    private bool CheckActiveQuestionnaire(Customer customerData)
    {
        var questionnaireEffectiveDate = GetQuestionnaireEffectiveDate(customerData);
        if (questionnaireEffectiveDate == null)
        {
            return false;
        }

        //if (questionnaireEffectiveDate.Value.Year < DateTime.UtcNow.Year - 3)
        if (questionnaireEffectiveDate.Value < DateTime.UtcNow.AddYears(-3))
        {
            return false;
        }

        return true;
    }

    private static DateTime? GetQuestionnaireEffectiveDate(Customer customerData)
    {
        return customerData?.CustomerQA?.StartDateDT;
    }

    private bool CheckActiveIdentity(Customer customerData)
    {
        var identityUserDocument = customerData?.IdentificationDocuments?.FirstOrDefault(doc =>
            doc.DocumentType
                .In(LegalDocumentTypes.Identity,
                    LegalDocumentTypes.Military,
                    LegalDocumentTypes.ForeignIdentity,
                    LegalDocumentTypes.Passport)
            && (doc.ExpiryDateDT ?? DateTime.MaxValue) > DateTime.UtcNow.Date);

        if (identityUserDocument == null)
            return false;

        //if (identityUserDocument.DocumentType == (decimal)LegalDocumentTypeEnum.ForeignIdentity
        //    || identityUserDocument.DocumentType == (decimal)LegalDocumentTypeEnum.Passport)
        //{
        //    var isHighRiskCountry = _validatorCommonService.CheckHighRiskCountry(null, null, identityUserDocument.IssuerCountry.ToString());
        //    if (isHighRiskCountry)
        //        return true;
        //}
        return true;
    }

    private bool IsValidProductCode(string productCode)
    {
        if (_validationsControlSettings.ValidAccountProductCodes.Length == 0)
        {
            return true;
        }

        if (productCode.Clear() == null)
        {
            return false;
        }

        return _validationsControlSettings.ValidAccountProductCodes.Contains(productCode);
    }
}
