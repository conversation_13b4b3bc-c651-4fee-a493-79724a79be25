﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;

namespace Nbg.Ewallet.Api.Controllers
{
    /// <summary>
    /// This Controller provides functionality for EWallet Payments
    /// </summary>
    [Produces("application/json")]
    [Consumes("application/json")]
    [ApiExplorerSettings(GroupName = "Terms")]
    public class TermsController : BaseController
    {
        private readonly ITermsService _termsService;

        public TermsController(ITermsService termsService, ILogger<TermsController> logger) : base(logger)
        {
            _termsService = termsService;
        }

        /// <summary>
        /// get terms and conditions
        /// </summary>
        [HttpGet]
        [Route("/getTerms", Name = "getTerms")]
        public async Task<ActionResult<Terms>> GetTerms()
        {
            return await _termsService.GetTerms();
        }
    }
}
