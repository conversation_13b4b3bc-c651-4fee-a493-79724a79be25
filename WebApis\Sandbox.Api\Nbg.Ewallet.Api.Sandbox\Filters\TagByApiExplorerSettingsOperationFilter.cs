﻿using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Nbg.Ewallet.Api.Sandbox.Filters;

public class TagByApiExplorerSettingsOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        if (context.ApiDescription.ActionDescriptor is not ControllerActionDescriptor controllerActionDescriptor)
        {
            return;
        }

        var apiExplorerSettings = controllerActionDescriptor
            .ControllerTypeInfo.GetCustomAttributes(typeof(ApiExplorerSettingsAttribute), true)
            .Cast<ApiExplorerSettingsAttribute>()
            .FirstOrDefault();

        if (apiExplorerSettings != null && !string.IsNullOrWhiteSpace(apiExplorerSettings.GroupName))
        {
            operation.Tags = [new OpenApiTag { Name = apiExplorerSettings.GroupName }];
        }
        else
        {
            operation.Tags = [new OpenApiTag { Name = controllerActionDescriptor.ControllerName }];
        }
    }
}
