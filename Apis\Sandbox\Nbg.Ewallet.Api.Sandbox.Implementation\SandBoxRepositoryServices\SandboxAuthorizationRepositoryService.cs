﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxAuthorizationRepositoryService : IAuthorizationRepositoryService
{
    private readonly IMapper _mapper;
    private readonly ILogger<SandboxAuthorizationRepositoryService> _logger;
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;

    public SandboxAuthorizationRepositoryService(IMapper mapper, ILogger<SandboxAuthorizationRepositoryService> logger,
        IHttpContextAccessor httpContextAccessor, ISandBoxRepositoryService sandBoxRepositoryService, IHttpContextRepositoryService httpContextRepositoryService)
    {
        _mapper = mapper;
        _sandBoxRepositoryService = sandBoxRepositoryService;
        _httpContextAccessor = httpContextAccessor;
        _httpContextRepositoryService = httpContextRepositoryService;
        _logger = logger;
    }

    public async Task<RepoAuthorizationRequest> RepoAuthorizationRequestFindOneByWalletIdAndAuthorizationRequestId(Guid walletId, Guid requestId)
    {
        var userid = _httpContextRepositoryService.GetUserId();

        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        //var walletId = sandbox.UserPermissions.Find(up => up.userid == userid).walletId;
        var wallet = sandbox.Wallets.Find(w => w.WalletId == walletId);
        var authorizationRequest = sandbox.WalletAuthorizationRequests.FirstOrDefault(war => war.Id == requestId);

        var response = _mapper.Map<RepoAuthorizationRequest>(authorizationRequest.TargetWallet);
        response.Id = authorizationRequest.Id;
        response.CreatedAt = authorizationRequest.CreatedAt;
        response.UpdatedAt = authorizationRequest.UpdatedAt;
        response.Status = authorizationRequest.Status;
        response.ExpiresAt = authorizationRequest.ExpiresAt;
        response.RequestorWalletId = wallet.WalletId;
        return response;
    }

    public async Task<List<RepoAuthorizationRequest>> RepoAuthorizationRequestFindAllByWalletIdAndRequestStatusAsync(Guid walletId, RequestStatus? status)
    {
        //var userid = _httpContextRepositoryService.GetUserId();
        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        //var walletId = sandbox.UserPermissions.Find(up => up.userid == userid).walletId;
        //var wallet = sandbox.Wallets.Find(w => w.WalletId == walletId);
        var authorizationRequests = sandbox.WalletAuthorizationRequests
            .Where(war => war.RequestorWallet.WalletId == walletId || war.TargetWallet.WalletId == walletId);

        if (status != null)
        {
            authorizationRequests = authorizationRequests.Where(war => war.Status == status.GetValueOrDefault());
        }

        return authorizationRequests.Select(ar =>
        {
            return new RepoAuthorizationRequest
            {
                TargetWalletId = ar.TargetWallet.WalletId,
                RequestorWalletId = ar.RequestorWallet.WalletId,
                CreatedAt = ar.CreatedAt,
                ExpiresAt = ar.ExpiresAt,
                Id = ar.Id,
                Status = ar.Status,
                UpdatedAt = ar.UpdatedAt
            };
        }).ToList();
    }

    public async Task RepoAuthorizationRequestSaveAsync(RepoAuthorizationRequest authRequest)
    {
        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();


        //check if user is admin
        //check if requests authorization to own wallet
        //check if requests authorization to non-existing wallet
        //check if authorization request already exists for target wallet
        var targetWalletId = authRequest.TargetWalletId;
        var userid = _httpContextRepositoryService.GetUserId();

        var requestorWalletId = sandbox.UserPermissions.Find(up => up.UserId == userid).WalletId;
        var requestorWallet = sandbox.Wallets.Find(w => w.WalletId == requestorWalletId);

        var targetWallet = sandbox.Wallets.Find(w => w.WalletId == targetWalletId);

        var walletAuthorizationResponse = new WalletAuthorizationResponse
        {
            Id = authRequest.Id,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddMonths(3).Date.AddDays(1).AddTicks(-1),
            Status = RequestStatus.Pending,
            TargetWallet = new TargetWalletAuthorization
            {
                WalletId = authRequest.TargetWalletId,
                OrganizationName = targetWallet.OrganizationName,
                VatNumber = targetWallet.VatNumber,
                WalletAccount = targetWallet.WalletAccount,
                WalletName = targetWallet.WalletName,
            },
            RequestorWallet = new RequestorWalletAuthorization
            {
                WalletId = authRequest.RequestorWalletId,
                OrganizationName = requestorWallet.OrganizationName,
                VatNumber = requestorWallet.VatNumber,
                WalletAccount = requestorWallet.WalletAccount,
                WalletName = requestorWallet.WalletName,
            }
        };

        sandbox.WalletAuthorizationRequests.Add(walletAuthorizationResponse);
        await _sandBoxRepositoryService.UpdateSandboxData(sandbox);
    }


    public async Task RepoAuthorizationRequestUpdateAuthorizationRequestAsync(RepoAuthorizationRequest request)
    {
        var requestId = request.Id;
        _logger.LogInformation("{RequestID}", requestId);
        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        var authRequest = sandbox.WalletAuthorizationRequests.Find(r => r.Id == requestId);

        authRequest.Status = request.Status.Value;
        authRequest.UpdatedAt = DateTime.UtcNow;

        await _sandBoxRepositoryService.UpdateSandboxData(sandbox);
    }


    public async Task<List<RepoAuthorizationRequest>> RepoAuthorizationRequestFindAllByRequestorWalletIdAndTargetWalletIdAndStatusAsync(Guid requestorWalletId, Guid targetWalletId,
        RequestStatus status)
    {
        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        return sandbox.WalletAuthorizationRequests
            .Where(x => x.RequestorWallet.WalletId == requestorWalletId)
            .Where(x => x.TargetWallet.WalletId == targetWalletId)
            .Where(x => x.Status == status)
            .Select(x => new RepoAuthorizationRequest
            {
                CreatedAt = x.CreatedAt,
                ExpiresAt = x.ExpiresAt,
                Id = x.Id,
                RequestorWalletId = requestorWalletId,
                TargetWalletId = targetWalletId,
                Status = status,
                UpdatedAt = x.UpdatedAt
            })
            .ToList();
    }
}
