﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types;

/// <summary>
///
/// </summary>
[DataContract]
public class AzureStatementsResponse
{

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "transactions")]
    public List<AzureStatementsTransaction> Transactions { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "count")]
    public int Count { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "paginationToken")]
    public string PaginationToken { get; set; }
}

/// <summary>
///
/// </summary>
[DataContract]
public class AzureStatementsTransaction : MultiTransactions
{
    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "actualUserID")]
    public string ActualUserId { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "actualDebtorCraCode")]
    public string ActualDebtorCraCode { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "actualDebtorName")]
    public string ActualDebtorName { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "accountingBalance")]
    public decimal AccountingBalance { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "amountEquivalent")]
    public decimal AmountEquivalent { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "branch")]
    public new string Branch { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "creditDebit")]
    public new string CreditDebit { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "date")]
    public string Date { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "description")]
    public string Description { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "externalSystem")]
    public string ExternalSystem { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "reference")]
    public string Reference { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "relatedAccount")]
    public string RelatedAccount { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "relatedName")]
    public string RelatedName { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "serialNum")]
    public int SerialNum { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "trans")]
    public string Trans { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "valeur")]
    public string Valeur { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "timestamp")]
    public string Timestamp { get; set; }
}

/// <summary>
///
/// </summary>
[DataContract]
public class MultiTransactions
{
    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "account")]
    public string Account { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "accountType")]
    public string AccountType { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "additionalInfo")]
    public string AdditionalInfo { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "branch")]
    public string Branch { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "branchDesc")]
    public string BranchDesc { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "card")]
    public string Card { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "counterpartyAccount")]
    public string CounterpartyAccount { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "counterpartyId")]
    public string CounterpartyId { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "counterpartyName")]
    public string CounterpartyName { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "currencyTrn")]
    public string CurrencyTrn { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "currencyAcc")]
    public string CurrencyAcc { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "customerId")]
    public string CustomerId { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "customerInfo")]
    public string CustomerInfo { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "entAmount")]
    public decimal EntAmount { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "exchangeRate")]
    public decimal ExchangeRate { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "id")]
    public string Id { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "intitiationTimestamp")]
    public string InitiationTimestamp { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "merchantId")]
    public string MerchantId { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "operation")]
    public string Operation { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "operationDesc")]
    public string OperationDesc { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "reasonCode")]
    public string ReasonCode { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "reasonInfo")]
    public string ReasonInfo { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "reasonCodeDesc")]
    public string ReasonCodeDesc { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "refId")]
    public string RefId { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "representativeCustomerId")]
    public string RepresentativeCustomerId { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "rowKey")]
    public string RowKey { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "scope")]
    public string Scope { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "source")]
    public string Source { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "terminalId")]
    public string TerminalId { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "terminalType")]
    public string TerminalType { get; set; }

    ///// <summary>
    /////
    ///// </summary>
    //[DataMember(Name = "timestamp")]
    //public DateTime Timestamp { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "trnAmount")]
    public decimal TrnAmount { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "type")]
    public string Type { get; set; }

    ///// <summary>
    /////
    ///// </summary>
    //[DataMember(Name = "valeur")]
    //public DateTime Valeur { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "creditDebit")]
    public string CreditDebit { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "counterpartyBank")]
    public string CounterpartyBank { get; set; }
}
