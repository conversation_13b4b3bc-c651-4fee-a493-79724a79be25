﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

public class MassStatement
{
    /// <summary>
    /// Payment Code
    /// </summary>
    [DataMember(Name = "paymentCode")]
    public string PaymentCode { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>
    /// Amount
    /// </summary>
    [DataMember(Name = "amount")]
    public string Amount { get; set; }
}
