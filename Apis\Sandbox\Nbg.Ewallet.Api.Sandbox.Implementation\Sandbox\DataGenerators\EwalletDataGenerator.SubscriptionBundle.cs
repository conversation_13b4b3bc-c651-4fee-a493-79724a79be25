﻿using System.Collections.Generic;
using System.Linq;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static List<SandboxSubscriptionBundle> GenerateSubscriptionBundles(List<SandboxSubscription> walletsubscriptions)
    {
        return walletsubscriptions.SelectMany(walletSub =>
        {
            //var rnd = new Random();
            return new List<SandboxSubscriptionBundle>() {
                new SandboxSubscriptionBundle
                {
                    TransactionType = SubscriptionTransactionType.Transfer,
                    Value = 500,
                },
                new SandboxSubscriptionBundle
                {
                    TransactionType = SubscriptionTransactionType.Payment,
                    Value = 500,
                }
            };
        }).ToList();
    }
}
