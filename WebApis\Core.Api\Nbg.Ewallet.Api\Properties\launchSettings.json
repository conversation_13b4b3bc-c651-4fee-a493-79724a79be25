{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iis": {"applicationUrl": "https://localhost/Nbg.Ewallet.Api", "sslPort": 443}, "iisExpress": {"applicationUrl": "http://localhost:52269", "sslPort": 44384}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS": {"commandName": "IIS", "launchBrowser": true, "launchUrl": "health", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IISExpress": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "health", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}