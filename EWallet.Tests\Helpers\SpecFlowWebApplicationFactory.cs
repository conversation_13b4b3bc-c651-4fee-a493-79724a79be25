﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;


// this is to be used for injecting different program classes according to configuration
// to be implemented later needs modifications to the program classes
// of the other projects sandbox, core and proxy
public class SpecFlowWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
{
    protected override IWebHostBuilder CreateWebHostBuilder()
    {
        // Dynamically load the Program class and create a web host builder
        // return Program.CreateHostBuilder(new string[] { })
        // for now just return null;
        return null;
    }
}
