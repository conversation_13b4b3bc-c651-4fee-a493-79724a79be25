﻿namespace Nbg.Ewallet.Api.Types.Payments;

public class PaymentsValidator
{
    private static readonly string FailedOrganizationId = "bcb0ae97-7e14-4011-8879-33edc5db7411";

    private static readonly string FailedPaymentCode = "543587623045";

    public static bool IsValidOrganizationId(string organizationId)
    {
        return !string.IsNullOrEmpty(organizationId) && OrganizationExists(organizationId);
    }

    public static bool IsValidAmount(decimal? amount)
    {
        return amount.HasValue && amount > 0;
    }

    public static bool IsValidPaymentCode(string paymentCode)
    {
        return !string.IsNullOrEmpty(paymentCode) && PaymentCodeExists(paymentCode);
    }

    private static bool OrganizationExists(string organizationId)
    {
        return organizationId != FailedOrganizationId;
    }

    private static bool PaymentCodeExists(string paymentCode)
    {
        return paymentCode != FailedPaymentCode;
    }
}
