﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.SubscriptionApi;

[DataContract]
public class SubscriptionApiResponse
{
    [DataMember(Name = "id")]
    public Guid SubscriptionId { get; set; }

    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    [DataMember(Name = "startDate")]
    public DateTime StartDate { get; set; }

    [DataMember(Name = "endDate")]
    public DateTime EndDate { get; set; }

    [DataMember(Name = "status")]
    public SubscriptionStatus Status { get; set; }

    [DataMember(Name = "paymentDue")]
    public DateTime PaymentDue { get; set; }

    [DataMember(Name = "dueAmount")]
    public decimal? DueAmount { get; set; }

    [DataMember(Name = "tier")]
    public SubscriptionTier Tier { get; set; }

    [DataMember(Name = "optOut")]
    public bool OptOut { get; set; }

    [DataMember(Name = "subscriptionBundles")]
    public List<SubscriptionBundle> SubscriptionBundles { get; set; } // TODO: remove this?
}
