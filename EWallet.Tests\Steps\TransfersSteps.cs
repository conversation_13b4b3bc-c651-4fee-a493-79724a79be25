﻿using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types.AccountApi;
using SpecFlowConfiguration;
using TechTalk.SpecFlow;

namespace EWallet.Tests.Steps;

[Binding]
public class TransfersSteps
{
    private readonly HttpClient _httpClient;
    private FeatureContext _featureContext;
    private ISpecFlownfigurationService _specFlownfigurationService;
    public TransfersSteps(HttpClient httpClient, FeatureContext featureContext, ISpecFlownfigurationService specFlownfigurationService)
    {
        _httpClient = httpClient;
        _featureContext = featureContext;
        _specFlownfigurationService = specFlownfigurationService;
        if (!_featureContext.ContainsKey("RegisteredWallets"))
        {
            _featureContext.Add("RegisteredWallets", new List<Wallet>());
        }
    }

    [Given(@"an internal user of the second wallet with (.*)")]
    public async Task GivenAnInternalUserOfTheSecondWalletWithTrueTrueTrueTrue(string userPermission)
    {
        var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];

        var result = JsonSerializer.Deserialize<UserPermission>(userPermission);
        var setWalletUserPermissionsRequest = new SetWalletUserPermissionsRequest
        {
            Permissions = new List<UserPermission> { result }
        };
        var walletId = walletsList[1].WalletId.ToString();
        var uri = $"wallet/{walletId}/user-permissions";
        var response = await _httpClient.PostAsJsonAsync(uri, setWalletUserPermissionsRequest);
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        //other checks should be included here
    }

    [Then(@"I make a Transfer from the second wallet to the first")]
    public async Task ThenIMakeATransferFromTheSecondWalletToTheFirst()
    {
        var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];

        var transfersRequest = new TransfersRequest
        {
            UserID = "Companyuserid1",
            Amount = 10,
            Currency = "EUR",
            BeneficiaryBic = "ETHNGRAA",
            DebitAccount = walletsList[1].WalletAccount,
            //debtorName = "Bill",
            ReceiverAcc = walletsList[0].WalletAccount,
            ReceiverGR = "credit name",
            Reason = "test",
            TransferType = TransferType.NBG
        };

        var walletId = walletsList[1].WalletId.ToString();
        var uri = $"transfers/{walletId}/transfer";
        var response = await _httpClient.PostAsJsonAsync(uri, transfersRequest);
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
    }
}
