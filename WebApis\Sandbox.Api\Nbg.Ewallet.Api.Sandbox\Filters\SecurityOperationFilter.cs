﻿using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Nbg.Ewallet.Api.Sandbox.Filters;

public class SecurityOperationFilter : IOperationFilter
{
    private readonly List<string> _authCodeActions = ["RegisterIssuer"];

    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        if (context.ApiDescription.ActionDescriptor is not ControllerActionDescriptor controllerActionDescriptor)
        {
            return;
        }

        var actionName = controllerActionDescriptor.ActionName;
        var isAuthCodeAction = _authCodeActions.Contains(actionName);

        operation.Security =
            [
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Client-Id"
                            }
                        },
                        new List<string>()
                    }
                }
            ];

        if (isAuthCodeAction)
        {
            operation.Security[0].Add(
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Authorization-Code-Token"
                    }
                },
                [
                    Extensions.SandboxScope + " offline_access",
                    Extensions.ProductionScope +" offline_access"
                ]
            );
        }
        else
        {
            operation.Security[0].Add(
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Client-Credentials-Token"
                    }
                },
                [
                    Extensions.SandboxScope,
                    Extensions.ProductionScope
                ]
            );
        }
    }
}
