﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Represents a search wallet result. Includes basic information of the Wallet entity.
/// </summary>
public class WalletLite
{
    /// <summary>
    /// The wallet Id
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// Wallet account IBAN
    /// </summary>
    [DataMember(Name = "walletAccount")]
    public string WalletAccount { get; set; }

    /// <summary>
    /// Company’s Name
    /// </summary>
    [DataMember(Name = "organizationName")]
    public string OrganizationName { get; set; }

    /// <summary>
    /// VAT number of company or individual connected with the wallet.
    /// </summary>
    [DataMember(Name = "vatNumber")]
    public string VatNumber { get; set; }

    /// <summary>
    /// Wallet Name - By default the company’s name
    /// </summary>
    [DataMember(Name = "walletName")]
    public string WalletName { get; set; }
}
