﻿Feature: External Wallet Permission
Scenario Outline: As an IB user, I want to give permissionss to an external wallet
  Given I am logged in as the user <userId>
    When I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    Then the external wallet must have <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
   
    #make sure the users under the userId column and the externalUserId are defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId | externalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0      | 1              | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |

 Scenario Outline: As an IB user, I want to revoke permissionss of an external user
  Given I am logged in as the user <userId>
    
    When I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    And I revoke permissions from <userId> to <externalUserId>
    Then the permission is revoked from <userId> to <externalUserId>
   
    #make sure the users under the userId column and the externalUserId are defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    |  externalUserId |  admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0         |     1           |  false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |

Scenario Outline: Fail to assign permissions to own wallet
  Given I am logged in as the user <userId>
    
   
    When I give permissions from <userId> to the same wallet with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    Then no persissions are assigned the for <userId>
       
    #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId      | externalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0           |    1           | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |

 Scenario Outline: Fail to revoke permissions to own wallet
  Given I am logged in as the user <userId>
    
    #When I give permissions from <walletName> to <targetWalletName>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    When I give permissions from <userId> to the same wallet with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    Then I cannot revoke the permissions as <userId> for <userId>
       
    #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    | externalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0         |    1           | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |


 Scenario Outline: Succesfully retrieve only active permissions when showAll flag is false for all external wallets
  Given I am logged in as the user <userId>
    
    When I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    #And  I revoke permissions from <walletName> to <targetWalletName>
    And I revoke permissions from <userId> to <externalUserId>

    And  I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    Then Only the Active permissions are retrieved for all wallets for <userId>
       
    #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    | externalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0         |    1           | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |


 Scenario Outline: Succesfully retrieve all active permissions when showAll flag is false for all external wallets
  Given I am logged in as the user <userId>
    
   
    When I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    And  I revoke permissions from <userId> to <externalUserId>
    And  I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    And  I revoke permissions from <userId> to <externalUserId>
       
    #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    | externalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0         |    1           | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |

 Scenario Outline: Succesfully retrieve only active permissions when showAll flag is false for a specific wallet
  Given I am logged in as the user <userId>
    
   
    When I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    And I revoke permissions from <userId> to <externalUserId>
    And  I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    Then Only the Active permissions are retrieved for <userId> and <externalUserId>
       
    #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    | externalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0         |    1           | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |

 Scenario Outline: Succesfully all permissions when showAll flag is false for a specific wallet
  Given I am logged in as the user <userId>
    
   
    When I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    And  I revoke permissions from <userId> to <externalUserId>
    And  I give permissions to <externalUserId>  with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <transactionType> and <amount> and <timeframe>
    Then All permissions are retrieved for <userId> and <externalUserId>
       
    #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    | externalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0         |    1           | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    |
