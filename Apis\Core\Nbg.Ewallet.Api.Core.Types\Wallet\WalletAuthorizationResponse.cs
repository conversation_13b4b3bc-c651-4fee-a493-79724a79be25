﻿using System;
using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Wallet Statements Response
/// </summary>
public class WalletAuthorizationResponse
{

    /// <summary>
    /// Id
    /// </summary>
    [DataMember(Name = "id")]
    public Guid Id { get; set; }

    /// <summary>
    /// Datetime of request creation
    /// </summary>
    [DataMember(Name = "createdAt")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Datetime of request creation
    /// </summary>
    [DataMember(Name = "updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Expiration Datetime of the authorization
    /// </summary>
    [DataMember(Name = "expiresAt")]
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Status of the authorization request
    /// </summary>
    [DataMember(Name = "status")]
    public RequestStatus Status { get; set; }

    /// <summary>
    /// Requestor Wallet
    /// </summary>
    [DataMember(Name = "requestorWallet")]
    public RequestorWalletAuthorization RequestorWallet { get; set; }

    /// <summary>
    /// Target Wallet
    /// </summary>
    [DataMember(Name = "targetWallet")]
    public TargetWalletAuthorization TargetWallet { get; set; }
}

/// <summary>
/// Wallet Authorization
/// </summary>
[DataContract]
public abstract class WalletAuthorizationBase
{
    /// <summary>
    /// Wallet Id
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// Wallet account IBAN
    /// </summary>
    [DataMember(Name = "walletAccount")]
    public string WalletAccount { get; set; }

    /// <summary>
    /// Company’s Name
    /// </summary>
    [DataMember(Name = "organizationName")]
    public string OrganizationName { get; set; }

    /// <summary>
    /// vatNumber
    /// </summary>
    [DataMember(Name = "vatNumber")]
    public string VatNumber { get; set; }

    /// <summary>
    /// Wallet Name - By default the company’s name
    /// </summary>
    [DataMember(Name = "walletName")]
    public string WalletName { get; set; }

}

/// <summary>
///
/// </summary>
[DataContract]
public class RequestorWalletAuthorization : WalletAuthorizationBase { }

/// <summary>
///
/// </summary>
[DataContract]
public class TargetWalletAuthorization : WalletAuthorizationBase { }
