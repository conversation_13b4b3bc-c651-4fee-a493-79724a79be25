﻿using System;

namespace Nbg.Ewallet.Api.Implementation.Extensions;

public static class DateTimeExtensions
{
    public static Tuple<DateTime, DateTime> GetCurrentMonth(this DateTime dateFrom, DateTime dateTo)
    {
        var now = DateTime.UtcNow;
        var from = dateFrom;
        var to = dateFrom.AddMonths(1);
        while (from.AddMonths(1) < now)
        {
            from = from.AddMonths(1);
            to = to.AddMonths(1);

            if (to > dateTo)
            {
                to = dateTo;
                break;
            }
        }

        return Tuple.Create(from, to);
    }
}
