﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.NetCore.ApiSandbox.Interfaces;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public class SandBoxRepositoryService : ISandBoxRepositoryService
{
    private readonly IHttpContextAccessor _contextAccessor;
    private ISandboxService _sandboxService;
    public SandBoxRepositoryService(IHttpContextAccessor httpContextAccessor, ISandboxService sandboxService)
    {
        _contextAccessor = httpContextAccessor;
        _sandboxService = sandboxService;
    }

    public async Task<EwalletSandbox> GetSandBoxModel()
    {
        string sandboxId = _contextAccessor.HttpContext.GetHeaderValue("sandboxid");
        if (string.IsNullOrEmpty(sandboxId))
        {
            throw new Exception("Invalid SandBox Id");
        }
        var EWalletDataModel = await _sandboxService.ExportSandbox<EwalletDataModel>(sandboxId).CheckModel();
        return EWalletDataModel.EwalletSandbox;
    }

    public async Task<bool> UpdateSandboxData(EwalletSandbox aDataModel)
    {
        string sandboxId = _contextAccessor.HttpContext.GetHeaderValue("sandboxid");
        if (string.IsNullOrEmpty(sandboxId))
        {
            throw new Exception("Invalid SandBox Id");
        }
        var EWalletDataModel = await _sandboxService.ExportSandbox<EwalletDataModel>(sandboxId).CheckModel();
        EWalletDataModel.EwalletSandbox = aDataModel;
        return _sandboxService.UpdateSandboxData(EWalletDataModel);
    }
}
