﻿using System;
using System.Runtime.Serialization;
using Nbg.Ewallet.Api.Types.TimeConverters;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Types.Transfers;

/// <summary>
/// Transaction definition
/// </summary>
public class Transaction
{
    /// <summary>
    /// The unique identifier of a transaction within the eWallet domain.
    /// This identifier is not shared with IB or any other system of the bank.
    /// </summary>
    [DataMember(Name = "transactionId")]
    public Guid TransactionId { get; set; }

    /// <summary>
    /// The Wallet related to the transaction, whose funds are used to execute the transaction.
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// The status indicates if a transaction has been sent for processing to the core system or is still waiting for approval.
    /// Status options: PENDING_APPROVAL, EXECUTED.
    /// It is not related to the execution success.
    /// </summary>
    [DataMember(Name = "status")]
    public TransactionStatus? Status { get; set; }

    /// <summary>
    /// The transaction subtype provides further segregation on the transaction type.
    /// Supported subtypes: WalletToWallet, WalletToIBAN, WalletToNBG, GenericPayment.
    /// </summary>
    [DataMember(Name = "transactionSubType")]
    public TransactionSubType? TransactionSubType { get; set; }

    /// <summary>
    /// Supported transaction types: Transfer, Payment.
    /// </summary>
    [DataMember(Name = "transactionType")]
    public TransactionType? TransactionType { get; set; }

    /// <summary>
    /// Transaction currency. Only EUR is supported currently.
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>
    /// The amount involved in the transaction.
    /// </summary>
    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    /// <summary>
    /// The IBAN to which the funds will be credited.
    /// </summary>
    [DataMember(Name = "creditIban")]
    public string CreditIban { get; set; }

    /// <summary>
    /// The name of the recipient for the credit transaction.
    /// </summary>
    [DataMember(Name = "creditName")]
    public string CreditName { get; set; }

    /// <summary>
    /// The IBAN from which the funds will be debited.
    /// </summary>
    [DataMember(Name = "debitIban")]
    public string DebitIban { get; set; }

    /// <summary>
    /// The name of the sender for the debit transaction.
    /// </summary>
    [DataMember(Name = "debitName")]
    public string DebitName { get; set; }

    /// <summary>
    /// Timestamp of the creation of the transaction record.
    /// </summary>
    [DataMember(Name = "timestamp")]
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Indicator for SEPA Instant transfer.
    /// Check getTransferOptions response for instant capability.
    /// </summary>
    [DataMember(Name = "instant")]
    public bool? Instant { get; set; }

    /// <summary>
    /// Date of the transaction execution. When the transaction status is Pending Approval, this property is expected to be empty.
    /// </summary>
    [System.Text.Json.Serialization.JsonConverter(typeof(SystemTextJsonDateConverter))]
    [DataMember(Name = "transactionDate")]
    public DateTime? TransactionDate { get; set; }


    /// <summary>
    /// Date when transaction amount is debited or credited.
    /// </summary>
    [System.Text.Json.Serialization.JsonConverter(typeof(SystemTextJsonDateConverter))]
    [DataMember(Name = "valeur")]
    public DateTime? Valeur { get; set; }

    /// <summary>
    /// Description of this transaction.
    /// </summary>
    [DataMember(Name = "description")]
    public string Description { get; set; }

    /// <summary>
    /// Transaction reference number (with the "externalSystem" combination is unique).
    /// </summary>
    [DataMember(Name = "reference")]
    public string Reference { get; set; }

    /// <summary>
    /// ID of the batch containing the transaction.
    /// </summary>
    [DataMember(Name = "batchId")]
    public Guid? BatchId { get; set; }

    /// <summary>
    /// Description of the batch containing the transaction.
    /// </summary>
    [DataMember(Name = "batchDescription")]
    public string BatchDescription { get; set; }

    /// <summary>
    /// The user who submitted the transaction.
    /// </summary>
    public string SubmittedBy { get; set; }

    /// <summary>
    /// The user who approved the transaction.
    /// </summary>
    public string ApprovedBy { get; set; }

    /// <summary>
    /// The user who executed the transaction.
    /// </summary>
    public string ExecutedBy { get; set; }

    /// <summary>
    /// The user who rejected the transaction.
    /// </summary>
    public string RejectedBy { get; set; }

    /// <summary>
    /// The result of the transaction. Options: Success, Failed.
    /// </summary>
    public string Result { get; set; }

    /// <summary>
    /// Message from CICS regarding the result of the transaction.
    /// </summary>
    public string ResultReason { get; set; }

    /// <summary>
    /// Creditor's bank branch identifier
    /// </summary>
    [DataMember(Name = "branchId")]
    public string BranchId { get; set; }

    /// <summary>
    /// Debtor's name
    /// </summary>
    [DataMember(Name = "debtorName")]
    public string DebtorName { get; set; }

    /// <summary>
    /// Transaction's Commission
    /// </summary>
    [DataMember(Name = "commission")]
    public decimal? Commission { get; set; }

}
