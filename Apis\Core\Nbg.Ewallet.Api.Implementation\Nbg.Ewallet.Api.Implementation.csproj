﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>True</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
    </PropertyGroup>
    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="Nbg.AspNetCore.Http.Extensions" Version="8.0.0" />
        <PackageReference Include="nbg.netcore.consent.types" Version="1.0.2" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector" Version="1.0.53" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.Customer" Version="1.0.28" />
        <PackageReference Include="Nbg.NetCore.Data" Version="8.0.3" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
        <PackageReference Include="Nbg.NetCore.HttpExceptions" Version="8.0.0" />
        <PackageReference Include="Nbg.NetStandard.Utilities" Version="1.0.5" />
        <PackageReference Include="Nbg.OpenBanking.ConsentTypes" Version="3.0.0" />
        <PackageReference Include="Nbg.OpenBanking.Utilities" Version="3.0.2" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Nbg.Ewallet.Api.Interfaces\Nbg.Ewallet.Api.Interfaces.csproj" />
        <ProjectReference Include="..\Nbg.Ewallet.Repository.Interfaces\Nbg.Ewallet.Repository.Interfaces.csproj" />
        <ProjectReference Include="..\Nbg.Ewallet.Repository.Types\Nbg.Ewallet.Repository.Types.csproj" />
    </ItemGroup>
</Project>