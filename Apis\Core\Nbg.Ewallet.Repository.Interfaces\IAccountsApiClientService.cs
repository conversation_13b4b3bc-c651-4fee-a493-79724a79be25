﻿using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.EWallet.Repository.Types.coreApis;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface IAccountsApiClientService
{
    Task<Response<TransfersResponseBase>> ExecuteTransactionAsync(TransfersRequestBase request);
    Task<GetAccountBalanceResponse> GetAccountBalanceAsync(GetAccountBalanceRequest request);
    Task<OpenAccountResponse> OpenAccountAsync(OpenAccountRequest request);
    Task<TransferExpensesCommissionsResponse> CalculateTransferExpensesCommissionsAsync(TransferExpensesCommissionsRequest request);
    Task<BalancesResponse> GetAccountsAsync(BalancesRequest request);
    Task<AccountsFullResponse> GetAccountsFullAsync(AccountsFullRequest request);
    Task<bool> ValidateUserAccount(string userId, string ibanAccount);
    Task<byte[]> RetrieveWalletStatementPdfExport(string userId, string account, StatementRequestQueryParams request);
}
