{
    "Logging": {
        "LogLevel": {
            "Default": "Warning",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Warning"
        }
    },

    "ConnectionStrings": {
        "Audit": "Server=V0000A0016,56811;database=InternetBankingAudit;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "IBank": "Server=V0000A0016,56811;database=InternetBanking;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "uniqueMessage": "Server=************,56811;database=BigDataibank;Integrated Security=SSPI;Persist Security Info=False;Connection Timeout=3;Encrypt=false",
        "appSettings": "Server=V0000a0016\\s1,56811;database=APIConfig;Integrated Security=SSPI;Persist Security Info=False;Encrypt=false"
    },

    "AuditMiddleware": {
        "ConnectionName": "Audit",
        "RequestIdHeaderName": "request-id",
        "UseSqlServer": false,
        "UseBigDataClient": true,
        "SetPrimarySqlServer": false,
        "SetPrimaryBigDataClient": true,
        "ServiceNamePrefix": "nbg.ewallet.api/v1",
        "DefaultApplicationId": "2DBA0863-647C-481E-93E9-6543D17B0F74",
        "IgnoredPaths": [
            "/favicon.ico"
        ],
        "RequestHttpHeaders": [
            "ByPassHeaders"
        ],
        "ResponseHttpHeaders": [
            "ByPassHeaders"
        ],
        "SensitiveProperties": []
    },

    "MultiSinkAuditWriter": {
        "primarySink": "kafka",
        "sinks": {
            "kafka": {
                "kafkaClient": {
                    "kafkaTopic": "qa-trlog-ibank-ingestion",
                    //"fallbackSqlConnectionString": "Server=V0000a0016\\s1,56811;database=BigDataIbank;Integrated Security=SSPI;Persist Security Info=False",
                    "producerOptions": {
                        "bootstrap.servers": "lkc-7y30gj-p8y3jg.westeurope.azure.glb.confluent.cloud:9092",
                        "security.protocol": "SASL_SSL",
                        "sasl.username": "36KPNI3QG35ZRZQC",
                        "sasl.mechanism": "PLAIN",
                        "delivery.timeout.ms": "5000",
                        "batch.size": "********",
                        "message.max.bytes": "********",
                        "acks": "1",
                        "compression.type": "snappy",
                        "linger.ms": "50"
                    }
                }
            }
        }
    },

    "Authentication": {
        "Authority": "https://myqa.nbg.gr/identity",
        "ApiName": "D8864CC2-56C2-43EB-ABD9-AF10484A2A20",
        "ApiSecret": "DFCABCA5-9BEC-4792-A8B8-134C6D7ACF01",
        "RequiredScope": "ewallet-api-v1",
        "EnableCaching": true,
        "CacheDuration": "00:30:00"
    },

    "ProxyMiddleware": {
        "Variables": {
            "CoreServiceUrl": "https://coreapplayerqa.nbg.gr/nbg.ewallet.core.api"
        }
    },

    "Healthcheck": {
        "CheckAllocatedMemory": true,
        "CheckUris": true,
        "UriCheckOptions": [
            {
                "Uri": "https://coreapplayerqa.nbg.gr/nbg.ewallet.core.api/health",
                "HttpClientName": "default",
                "HttpMethod": "GET",
                "TimeOutMilliseconds": 100000,
                "ExpectedCodes": [ 200 ]
            }
        ]
    },

    "CorporateUserAuthorizationPolicy": {
        "ApplyPolicy": false
    },

    "Sca": {
        "BaseAddress": "https://CoreAppLayerQA.nbg.gr",
        "ChallengeEndpoint": "/Nbg.NetCore.Sca.Core/sca/challenge",
        "ValidateEndpoint": "/Nbg.NetCore.Sca.Core/sca/validate",
        "DisableScaWhitelisting": false,
        "ScaTokenExpirationInSeconds": 300
    },

    "CustomRestSca": {
        "BaseAddress": "https://coreapplayerqa.nbg.gr/", //"http://localhost/"
        "ChallengeEndpoint": "sca/challenge",
        "ValidateEndpoint": "sca/validate",
        "DisableScaWhitelisting": false,
        "ScaTokenExpirationInSeconds": 120,
        "ApplicationId": "07255F0F-5BCC-4254-90B6-75E70A487333"
    },

    "HttpClient": {
        "ClientRestSca": {
            "BaseAddress": "https://coreapplayerqa.nbg.gr/Nbg.NetCore.Sca.Core/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": false,
            "Timeout": "00:03:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        }
    },

    "SmsOtpSettings": {
        "EnableSmsOtp": true
    },
    "UserIdRequestTransformer": {
        "HttpHeadersMappings": {
            "UserId": {
                "SourcePath": "ibank_user_id",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true,
                "Source": "Claim",
                "ThrowIfNotFoundFromSource": true
            }
        }
    }
}
