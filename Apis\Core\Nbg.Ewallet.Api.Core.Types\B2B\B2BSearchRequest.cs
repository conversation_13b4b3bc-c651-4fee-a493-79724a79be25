﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

public class B2BSearchRequest
{
    /// <summary>
    /// Vat Number
    /// </summary>
    [DataMember(Name = "vatNumber")]
    public string vatNumber { get; set; }

    /// <summary>
    /// Company Name
    /// </summary>
    [DataMember(Name = "name")]
    public string name { get; set; }

    /// <summary>
    /// Wallet Account
    /// </summary>
    [DataMember(Name = "walletAccount")]
    public string walletAccount { get; set; }

}
