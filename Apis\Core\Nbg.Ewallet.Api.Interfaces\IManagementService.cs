﻿using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IManagementService
{
    Task<AccountsResponse> GetUserAccountsAsync(AccountsRequest request);

    Task<ConsentResponse> GetConsentAsync(RetrieveConsentRequest request);

    Task<ConsentResponse> AuthorizeConsentAsync(UpdateConsentRequest request);

    Task<ConsentResponse> RejectConsentAsync(UpdateConsentRequest request);
}
