﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Controllers
{
    [Produces("application/json")]
    [Consumes("application/json")]
    [ApiExplorerSettings(GroupName = "Consents", IgnoreApi = true)]
    public class ConsentsController : BaseController
    {
        private readonly IConsentService _consentService;
        public ConsentsController(IConsentService consentService, ILogger<ConsentsController> logger) : base(logger)
        {
            _consentService = consentService;
        }

        /// <summary>
        /// Create a new consent
        /// </summary>
        /// <remarks>Create a new customer access consent</remarks>
        ///
        [HttpPost]
        [Route("create", Name = "createconsent")]
        public async Task<ActionResult<CreateConsentResponse>> CreateConsent(CreateConsentRequest request)
        {
            return await _consentService.CreateConsent(request);
        }

        /// <summary>
        /// Retrieve an access consent
        /// </summary>
        [HttpGet]
        [Route("{consentId}", Name = "GetAccessConsent")]
        public async Task<ActionResult<CreateConsentResponse>> GetConsent([FromRoute] Guid consentId)
        {
            if (consentId == null)
            {
                throw new InvalidInputException();
            }
            return await _consentService.GetConsent(consentId);
        }
    }
}
