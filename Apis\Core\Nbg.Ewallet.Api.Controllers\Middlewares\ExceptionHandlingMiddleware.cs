﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Middlewares;

public class EWalletExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;

    public EWalletExceptionHandlingMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task Invoke(HttpContext context, ILogger<EWalletExceptionHandlingMiddleware> logger)
    {
        //var unitOfWork = context.RequestServices.GetRequiredService<IUnitOfWork>();
        try
        {
            await _next(context);
        }
        catch (ClientErrorException ex)
        {
            ///await unitOfWork.RollbackFinalAsync();

            // Log the exception
            logger.LogError(ex, ex.Message);
            await _handledClientErrorExceptionAsync(context, ex);
        }
        catch (ValidationException ex)
        {
            // await unitOfWork.RollbackFinalAsync();

            // Log the exception
            logger.LogError(ex, ex.Message);
            await _handledValidationExceptionAsync(context, ex);
        }
        catch (Exception ex)
        {
            //await unitOfWork.RollbackFinalAsync();

            logger.LogError(ex, ex.Message);
            await _handledExceptionAsync(context, ex);
        }
    }

    private static Task _handledValidationExceptionAsync(HttpContext context, ValidationException exception)
    {
        var response = new ErrorResponse(context, exception);

        if (exception.Value is not ModelStateDictionary modelState || modelState is null)
        {
            return _writeContext(context, response);
        }

        var allErrors = modelState.Values
            .SelectMany(v => v.Errors)
            .Select(e => e.ErrorMessage)
            .ToList();
        response.Errors = new ErrorDetails(allErrors);

        return _writeContext(context, response);
    }

    private static Task _handledClientErrorExceptionAsync(HttpContext context, ClientErrorException exception)
    {
        var response = new ErrorResponse(context, exception);
        return _writeContext(context, response);
    }

    private static Task _handledExceptionAsync(HttpContext context, Exception exception)
    {
        var response = new ErrorResponse(context, exception);
        return _writeContext(context, response);
    }

    private static Task _writeContext(HttpContext context, ErrorResponse response)
    {
        var result = JsonSerializer.Serialize(response);
        context.Response.ContentType = "application/json";
        context.Response.StatusCode = response.Status;
        return context.Response.WriteAsync(result);
    }
}

/// <summary>
/// Represents an error response returned by the API.
/// </summary>
public class ErrorResponse
{
    /// <summary>
    /// Gets or sets the error code or title of the error.
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// Gets or sets the HTTP status code of the response.
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// Gets or sets the trace identifier for the request, useful for debugging.
    /// </summary>
    public string TraceId { get; set; }

    /// <summary>
    /// Gets or sets the details of the error.
    /// </summary>
    public ErrorDetails Errors { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="ErrorResponse"/> class using the specified HTTP context and exception.
    /// </summary>
    /// <param name="context">The HTTP context for the current request.</param>
    /// <param name="exception">The exception that triggered the error response.</param>
    public ErrorResponse(HttpContext context, Exception exception)
    {
        TraceId = context.TraceIdentifier;
        switch (exception)
        {
            case ClientErrorException clientError:
                Code = clientError.GetErrorCode();
                Status = (int)clientError.GetHttpStatus();
                Errors = new ErrorDetails([clientError.Message]);
                break;
            case ValidationException validationError:
                Code = "Validation Error";
                Status = 400;
                break;
            default:
                Code = "Unexpected Server Error";
                Status = 500;
                Errors = new ErrorDetails([exception.Message]);
                break;
        }
    }
}

/// <summary>
/// Represents the details of an error.
/// </summary>
public class ErrorDetails
{
    /// <summary>
    /// Gets or sets the list of error messages.
    /// </summary>
    public List<string> Message { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="ErrorDetails"/> class.
    /// </summary>
    /// <param name="message">The list of error messages.</param>
    public ErrorDetails(List<string> message)
    {
        Message = message;
    }
}

public static class EWalletExceptionHandlingMiddlewareExtensions
{
    public static IApplicationBuilder UseEWalletExceptionHandling(
        this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<EWalletExceptionHandlingMiddleware>();
    }
}
