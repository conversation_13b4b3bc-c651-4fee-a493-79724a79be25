﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Payments;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Extensions;
using Nbg.Ewallet.Repository.Types.Records;

namespace Nbg.Ewallet.Api.Implementation.Extensions;
public static class PaymentsPayRequestExtensions
{
    public static RepoTransaction ToPendingRepoTransaction(this ExtendedPaymentsRequest @this, RepoWallet wallet, string userId)
    {
        var transaction = new RepoTransaction
        {
            TransactionId = @this.TransactionId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Amount = @this.SettlementInfo.Amount,
            Status = TransactionStatus.PENDING_APPROVAL,
            TransactionSubType = TransactionSubType.GenericPayment,
            TransactionType = TransactionType.Payment,
            WalletId = wallet.WalletId,
            CreditorIban = string.Empty,
            CreditorName = string.Empty,
            Currency = "EUR",
            DebtorIban = @this.Debtor.DebtorAccount.IBAN,
            DebtorName = @this.Debtor.Name,
            Reason = @this.RemittanceInfo?.Unstructured?.line ?? string.Empty,
            SubmittedBy = userId,
            PaymentCoreRequest = @this
        };
        return transaction;
    }

    public static ExtendedPaymentResponse EnrichResponse(this ExtendedPaymentsRequest @this, ExtendedPaymentResponse paymentsResponse)
    {
        if (@this == null || paymentsResponse == null) return null;
        paymentsResponse.Debtor = @this.Debtor;
        paymentsResponse.PaymentIdentification = @this.PaymentIdentification;
        paymentsResponse.SettlementInfo = @this.SettlementInfo;
        return paymentsResponse;
    }

    [Obsolete("This method is not used now", true)]
    public static void ValidateAccountRegisteredOnWallet(this ExtendedPaymentsRequest @this, RepoWallet wallet)
    {
        if (@this == null || wallet == null) return;
        var isNotSameAccount = wallet.WalletAccount.TrimEqual(@this.Debtor?.DebtorAccount?.IBAN);
        if (isNotSameAccount) throw new AccountNotRegisteredOnWalletException();
    }

    /// <summary>
    /// Checks if the debit account in the payment request is available to the user.
    /// Returns true if the account matches the wallet account; otherwise, validates the account via the Accounts API.
    /// Returns false if the account is valid but not the wallet account.
    /// Throws <see cref="AccountIsNotAvailableToUser"/> if the account is not available to the user.
    /// </summary>
    /// <param name="this">The payment request containing the debit account information.</param>
    /// <param name="wallet">The wallet associated with the user, used to compare the account.</param>
    /// <param name="userId">The user ID to validate account ownership against.</param>
    /// <param name="accountsApiClientService">The accounts API client service used for external account validation.</param>
    /// <returns>
    /// A <see cref="ValidateRequestResult"/> indicating:
    /// <list type="bullet">
    /// <item><description>True if the debit account matches the wallet account.</description></item>
    /// <item><description>False if the account is available to the user but does not match the wallet account.</description></item>
    /// </list>
    /// Throws <see cref="AccountIsNotAvailableToUser"/> if the account is not available to the user.
    /// </returns>
    public static async Task<ValidateRequestResult> ValidateAccountAvailableToUser(this ExtendedPaymentsRequest @this, RepoWallet wallet, string userId, IAccountsApiClientService accountsApiClientService)
    {
        var iban = @this.Debtor?.DebtorAccount?.IBAN?.Trim();
        // If the account matches the wallet account, return true
        if (@this.IsAccountLinkedToWallet(wallet))
        {
            // The account is the same as the wallet account, no need to validate.
            return new ValidateRequestResult(true, iban);
        }

        var availableToUser = await accountsApiClientService.ValidateUserAccount(userId, iban);
        if (!availableToUser)
        {
            throw new AccountIsNotAvailableToUser();
        }

        //Valid, but the account is not the same as the wallet account.

        return new ValidateRequestResult(false, iban);
    }

    /// <summary>
    /// Determines whether the IBAN of the debtor account in the payment request matches the wallet's account.
    /// Throws <see cref="ArgumentNullException"/> if the debtor IBAN is null or empty.
    /// Throws <see cref="ArgumentException"/> if the wallet account is null or whitespace.
    /// </summary>
    public static bool IsAccountLinkedToWallet(this ExtendedPaymentsRequest @this, RepoWallet wallet)
    {
        ArgumentNullException.ThrowIfNull(@this.Debtor?.DebtorAccount?.IBAN?.Trim());
        ArgumentException.ThrowIfNullOrWhiteSpace(wallet.WalletAccount);
        return wallet.WalletAccount.TrimEqual(@this.Debtor.DebtorAccount.IBAN);
    }
}
