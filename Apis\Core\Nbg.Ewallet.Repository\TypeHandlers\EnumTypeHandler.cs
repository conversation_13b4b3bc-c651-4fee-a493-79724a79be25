﻿using System;
using System.Data;
using Dapper;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.TypeHandlers;

public class EnumTypeHandler<TEnum> : SqlMapper.ITypeHandler
    where TEnum : struct
{
    public object Parse(Type destinationType, object value)
    {
        if (destinationType != typeof(DapperableEnum<TEnum>))
            throw new InvalidCastException($"Can't parse string value {value} into enum type {typeof(TEnum).Name}");
        return new DapperableEnum<TEnum>((string)value);
    }

    public void SetValue(IDbDataParameter parameter, object value)
    {
        parameter.DbType = DbType.String;
        parameter.Value = ((DapperableEnum<TEnum>)value).Value.ToString();
    }
}
