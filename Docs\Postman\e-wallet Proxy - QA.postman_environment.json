{"id": "53d22024-0709-4419-805a-427a1f009bbe", "name": "e-wallet Proxy - QA", "values": [{"key": "callback-url", "value": "https://www.getpostman.com/oauth2/callback/", "enabled": true}, {"key": "auth-url", "value": "https://myqa.nbg.gr/identity/connect/authorize", "enabled": true}, {"key": "access-token-url", "value": "https://myqa.nbg.gr/identity/connect/token", "enabled": true}, {"key": "client-id", "value": "9E7EBA58-3A10-4675-948F-903228854E25", "enabled": true}, {"key": "client-secret", "value": "29E3D299-7E65-4BDF-8829-9EEC23671B7F", "enabled": true}, {"key": "scope", "value": "ewallet-api-v1 ewallet-management", "enabled": true}, {"key": "host", "value": "https://ibankretailqa.nbg.gr/apis.proxy.myqa/ewallet.proxy.api/v1/", "enabled": true}, {"key": "consent-id", "value": "", "enabled": true}, {"key": "wallet-id", "value": "", "type": "default", "enabled": true}, {"key": "externalwalletId", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-05-20T13:19:10.524Z", "_postman_exported_using": "Postman/11.0.6"}