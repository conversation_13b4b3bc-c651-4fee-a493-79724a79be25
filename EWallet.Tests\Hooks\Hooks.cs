using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using System.Text.Json.Serialization;
using BoDi;
using Microsoft.AspNetCore.Mvc.Testing;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using specflow.tests.Helpers;
using SpecFlowConfiguration;
using TechTalk.SpecFlow;
using Xunit;

[assembly: CollectionBehavior(DisableTestParallelization = true)]
namespace EWallet.Tests.Hooks;

[Binding]
public class Hooks
{
    private static async Task<string> GetAuthTokenAsync(User user)
    {
        return await AuthCodeAuthorizationHelper.GetAuthCodeAuthorizationHeaderAsync(user.userName, user.passWord);
    }

    [BeforeFeature]
    private static async Task TestComposeUp(WebApplicationFactory<Program> webApplicationFactory, IObjectContainer objectContainer,
        FeatureContext featureContext, ISpecFlownfigurationService specFlownfigurationService)
    {
        var configuration = specFlownfigurationService.GetConfiguration();
        HttpClient httpClient = null;
        switch (configuration.Environment)
        {
            case "SandBox":
                {
                    var token = await GetAuthTokenAsync(configuration.Environments.SandBox.Users[0]);

                    httpClient = new HttpClient
                    {
                        BaseAddress = new Uri(configuration.GetBaseAddress()),
                    };

                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

                    var ok = await httpClient.GetAsync("health");

                    var requestPayload = new RequestModel
                    {
                        sandboxId = Guid.NewGuid().ToString(),
                        // Add other properties as needed
                    };

                    var createSandBoxResponse = await httpClient.PostAsJsonAsync("sandbox", requestPayload);
                    var statusCode = (int)createSandBoxResponse.StatusCode;

                    if (statusCode != 200)
                    {
                        Assert.Fail($"Expected status code 200, but got {statusCode}");
                    }
                    httpClient.DefaultRequestHeaders.Add("sandboxId", requestPayload.sandboxId);
                }
                break;

            case "SandBoxDirect":
                {
                    httpClient = new HttpClient
                    {
                        BaseAddress = new Uri(configuration.GetBaseAddress()),
                    };

                    var ok = await httpClient.GetAsync("health");

                    var requestPayload = new RequestModel
                    {
                        sandboxId = Guid.NewGuid().ToString(),
                        // Add other properties as needed
                    };

                    var createSandBoxResponse = await httpClient.PostAsJsonAsync("sandbox", requestPayload);
                    var statusCode = (int)createSandBoxResponse.StatusCode;

                    if (statusCode != 200)
                    {
                        Assert.Fail($"Expected status code 200, but got {statusCode}");
                    }
                    httpClient.DefaultRequestHeaders.Add("sandboxId", requestPayload.sandboxId);
                    httpClient.DefaultRequestHeaders.Add("UserId", configuration.Environments.Core.Users[0].userName);
                    httpClient.DefaultRequestHeaders.Add("Customer-Code", configuration.Environments.Core.Users[0].customerCode);

                    await InitailizeSandBox(httpClient);
                }
                break;
            case "Core":
                {
                    httpClient = new HttpClient
                    {
                        BaseAddress = new Uri(configuration.GetBaseAddress()),
                    };

                    httpClient.DefaultRequestHeaders.Add("UserId", configuration.Environments.Core.Users[0].userName);
                    httpClient.DefaultRequestHeaders.Add("Customer-Code", configuration.Environments.Core.Users[0].customerCode);
                }
                break;
            case "Proxy":
                {
                    var token = await GetAuthTokenAsync(configuration.Environments.SandBox.Users[0]);

                    httpClient = new HttpClient
                    {
                        BaseAddress = new Uri(configuration.GetBaseAddress()),
                    };

                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

                    var ok = await httpClient.GetAsync("health");

                    var requestPayload = new RequestModel
                    {
                        sandboxId = Guid.NewGuid().ToString(),
                        // Add other properties as needed
                    };

                    var createSandBoxResponse = await httpClient.PostAsJsonAsync("sandbox", requestPayload);
                    var statusCode = (int)createSandBoxResponse.StatusCode;

                    if (statusCode != 200)
                    {
                        Assert.Fail($"Expected status code 200, but got {statusCode}");
                    }
                    httpClient.DefaultRequestHeaders.Add("sandboxId", requestPayload.sandboxId);
                }
                break;
            case "QA":
                {
                    httpClient = new HttpClient
                    {
                        BaseAddress = new Uri(configuration.GetBaseAddress()),
                    };
                }
                break;
        }

        objectContainer.RegisterInstanceAs(httpClient);
        featureContext.Add("httpClient", httpClient);
    }
    public class RequestModel
    {
        [JsonPropertyName("sandboxId")]
        public string sandboxId { get; set; }
    }

    private static async Task InitailizeSandBox(HttpClient httpClient)
    {
        var registerRequest = new WalletRegister { WalletName = "Sandbox initial wallet" };
        var response = await httpClient.PostAsJsonAsync("wallet/register", registerRequest);

        var walletRegistrationResponse = await response.Content.ReadAsStringAsync();
        var newWallet = JsonSerializer.Deserialize<Wallet>(walletRegistrationResponse);

        var createSubscriptionRequest = new CreateSubscriptionRequest
        {
            Tier = SubscriptionTier.Basic
        };

        var uri = $"wallet/{newWallet.WalletId}/subscription";
        var walletCreationSubscriptionResponse = await httpClient.PostAsJsonAsync(uri, createSubscriptionRequest);

        var createSubscriptionResponseString = await walletCreationSubscriptionResponse.Content.ReadAsStringAsync();
        var createSubscriptionResponse = JsonSerializer.Deserialize<SubscriptionResponse>(createSubscriptionResponseString);

        var createAccountUri = $"wallet/{newWallet.WalletId}/account";
        var createAccountResponse = await httpClient.PostAsJsonAsync(createAccountUri, new { });

        var walletAccountResponse = await createAccountResponse.Content.ReadAsStringAsync();
        //var newWalletWithAccount = JsonSerializer.DeserializeObject<Wallet>(walletAccountResponse);
    }
}
