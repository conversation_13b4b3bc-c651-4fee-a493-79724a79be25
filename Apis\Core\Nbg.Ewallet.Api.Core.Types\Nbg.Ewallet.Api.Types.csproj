﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>True</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <DocumentationFile>Nbg.Ewallet.Api.Types.xml</DocumentationFile>
    </PropertyGroup>
    <ItemGroup>
        <Compile Remove="UploadCsvLog\**" />
        <EmbeddedResource Remove="UploadCsvLog\**" />
        <None Remove="UploadCsvLog\**" />
    </ItemGroup>
    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Azure.Identity" Version="1.11.4" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Interfaces" Version="6.0.2" />
        <PackageReference Include="nbg.netcore.consent.types" Version="1.0.2" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Nbg.Ewallet.Repository.Types\Nbg.Ewallet.Repository.Types.csproj" />
    </ItemGroup>
</Project>