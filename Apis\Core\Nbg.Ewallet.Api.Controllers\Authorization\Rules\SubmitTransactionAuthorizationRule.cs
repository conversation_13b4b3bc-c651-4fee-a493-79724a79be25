﻿using Microsoft.AspNetCore.Mvc.Filters;
using Nbg.Ewallet.Api.Types;

using Nbg.Ewallet.Repository.Types.Exceptions;
public class SubmitTransactionAuthorizationRule : BaseAuthorizationRule
{
    public override void EnsureAuthorized(AuthorizationContext authContext, AuthorizationFilterContext context)
    {
        CheckAuthorizationContext(authContext);
        if (!authContext.ActivePermission.Submit) throw new PermissionNotFoundException();
        CheckWalletId(authContext, context);
    }
}
