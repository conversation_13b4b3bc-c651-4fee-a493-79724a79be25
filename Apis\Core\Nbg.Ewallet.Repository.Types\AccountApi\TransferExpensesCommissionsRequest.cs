﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

/// <summary>
/// Calculate Expenses for a transfer
/// </summary>
[DataContract]
public class TransferExpensesCommissionsRequest
{
    /// <summary> userID</summary>
    [DataMember(Name = "userId")]
    public string UserID { get; set; }

    #region Debit Account Info

    /// <summary> Debit Account Number / IBAN </summary>
    [DataMember(Name = "debitAccount2")]
    public string DebitAccount { get; set; }

    /// <summary> Νόμισμα λογαριασμού χρέωσης (3-letter code EUR, USD, GBP, ..).
    /// Μπορεί να είναι κενό αν ο λογαριασμός χρέωσης <see cref="DebitAccount2"/> είναι σε μορφή IBAN</summary>
    [DataMember(Name = "debitAccountCurrency2")]
    public string DebitAccountCurrency { get; set; }

    #endregion Debit Account Info

    #region Receiver Info

    /// <summary>Λογαριασμός παραλήπτη (σε μορφή ΙΒΑΝ/free ΤΕΧΤ) </summary>
    [DataMember(Name = "receiverAcc2")]
    public string ReceiverAcc { get; set; }

    /// <summary>(IBAN/TEXT)Τύπος μεταφοράς, με χρήση IBAN ή free Text.
    /// Αν είναι κενό θα πάρει τιμή ανάλογα με τον τύπο του λογαριασμού παραλήπτη <see cref="ReceiverAcc2"/> και θα υπολογιστεί η αντίστοιχη χρέωση
    /// </summary>
    [DataMember(Name = "receiverAccType2")]
    public ReceiverAccType? ReceiverAccType { get; set; }

    /// <summary>Bic τράπεζας παραλήπτη.
    /// Μπορεί να είναι κενό αν ο λογαριασμός πίστωσης <see cref="ReceiverAcc2"/> είναι σε μορφή IBAN</summary>
    [DataMember(Name = "beneficiaryBic2")]
    public string BeneficiaryBic { get; set; }

    #endregion Receiver Info

    #region Foreign Transfer Wihtout IBAN

    /// <summary></summary>
    [DataMember(Name = "systType2")]
    public string SystType { get; set; }
    /// <summary></summary>
    [DataMember(Name = "systCode2")]
    public string SystCode { get; set; }
    /// <summary></summary>
    [DataMember(Name = "systBic2")]
    public string SystBic { get; set; }
    /// <summary></summary>
    [DataMember(Name = "systCountry2")]
    public string SystCountry { get; set; }

    #endregion Foreign Transfer Wihtout IBAN

    #region Amount & Expenses

    /// <summary>Ποσό μεταφοράς στο νόμισμα του λογαριασμού</summary>
    [DataMember(Name = "amount2")]
    public decimal Amount { get; set; }

    /// <summary>Νόμισμα Μεταφοράς</summary>
    [DataMember(Name = "remCurrency2")]
    public string RemCurrency { get; set; }

    /// <summary>Επιμερισμός Χρεώσεων SHA / BEN / OUR </summary>
    [DataMember(Name = "expenses2")]
    public ExpensesType? Expenses { get; set; }

    #endregion Amount & Expenses

    #region Other Transfer Data

    /// <summary>Επείγουσα αποστολή</summary>
    [DataMember(Name = "emergency2")]
    public bool Emergency { get; set; }

    /// <summary>
    /// Αποστολή άμεσα σε τράπεζα εσωτερικού ισχυει για  ATTIGRAA, CRBAGRAA, ERBKGRAA, PIRBGRAA
    /// </summary>
    [DataMember(Name = "onlineSend2")]
    public bool? OnlineSend { get; set; }

    /// <summary> Για SEPA instant payments. </summary>
    [DataMember(Name = "instantSend2")]
    public bool? InstantSend { get; set; }

    #endregion Other Transfer Data

    #region Other Optional Fields

    /// <summary></summary>
    [DataMember(Name = "remCategory2")]
    public string RemCategory { get; set; }
    /// <summary></summary>
    [DataMember(Name = "massFlag")]
    public string MassFlag { get; set; }

    #endregion Other Optional Fields

    [DataMember(Name = "remType2")]
    public string RemType { get; set; }
}
