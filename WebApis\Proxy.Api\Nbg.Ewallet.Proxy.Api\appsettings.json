{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },
    "AllowedHosts": "*",

    "ServicePointManager": {
        "DefaultConnectionLimit": 100,
        "UseNagleAlgorithm": false,
        "Expect100Continue": false,
        "EnableDnsRoundRobin": true,
        "ReusePort": true,
        "DnsRefreshTimeout": 120000
    },


    "SqlConfigurationProvider": {
        "ConnectionStringName": "appSettings",
        "ApplicationNames": [ "BigDataClient" ]
    },

    // This is irrelevant. No matter what values, it will not be used in this API
    "ProfileService": {
        "ConnectionStringName": "IBank"
    },

    // This is irrelevant. No matter what values, it will not be used in this API
    "CicsHttpConnector": {
        "Host": "http://***********",
        "Port": 2500,
        "MaxConnectionsLimit": 10,
        "Timeout": 20000,
        "LocalCertificateFingerprint": "",
        "DangerousAcceptAnyServerCertificate": false
    },

    "CorporateUserAuthorizationPolicy": {
        "ApplyPolicy": true,
        "AllowAuthorizationLevel": [ "A1", "B1", "C1" ]
    },

    "HttpClient": {
        "default": {
            "MaxConnectionsPerServer": 100,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:02:00",
            "UseProxy": false
        }
    },

    "ConsentIdRequestTransformer": {
        "HttpHeadersMappings": {
            "Consent-Id": {
                "SourcePath": "consent_id",
                "Source": "Claim",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true
            }
        }
    },

    "SubjectRequestTransformer": {
        "HttpHeadersMappings": {
            "Sub": {
                "SourcePath": "sub",
                "Source": "Claim",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true
            }
        }
    },

    "ClientIdRequestTransformer": {
        "HttpHeadersMappings": {
            "Client-Id": {
                "SourcePath": "client_id",
                "Source": "Claim",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true
            }
        }
    },

    "CustomerCodeRequestTransformer": {
        "HttpHeadersMappings": {
            "Customer-Code": {
                "SourcePath": "customer_code",
                "Source": "Claim",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true
            }
        }
    },

    "AuthorizationLevelRequestTransformer": {
        "HttpHeadersMappings": {
            "Authorization_Level": {
                "SourcePath": "authorization_level",
                "Source": "Claim",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true
            }
        }
    },

    "NumberOfApprovalsRequestTransformer": {
        "HttpHeadersMappings": {
            "Number_Of_Approvals": {
                "SourcePath": "number_of_approvals",
                "Source": "Claim",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true
            }
        }
    },

    "CompanyNameRequestTransformer": {
        "HttpHeadersMappings": {
            "CompanyName": {
                "SourcePath": "CompanyName",
                "Source": "Claim",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true
            }
        }
    }
}
