﻿using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.NetCore.CosmosConnector.Types.Constants;

namespace Nbg.Ewallet.Repository;

public class HostCommonConfigurationService : IHostCommonConfigurationService
{
    private readonly HostCommonConfigurationSettings _configuration;
    private readonly IMainFrameConnector _mainFrameConnector;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;

    public HostCommonConfigurationService(
        IMainFrameConnector mainFrameConnector,
        IOptions<HostCommonConfigurationSettings> configuration,
        IHttpContextRepositoryService httpContextRepositoryService)
    {
        _configuration = configuration.Value;
        _mainFrameConnector = mainFrameConnector;
        _httpContextRepositoryService = httpContextRepositoryService;
    }

    public HostCommonConfigurationSettings GetConfiguration()
    {
        return _configuration;
    }

    public async Task<string> GetProductCodeAsync()
    {
        var customerCode = _httpContextRepositoryService.GetCustomerCode();
        var customerData = await _mainFrameConnector.GetCustomerDataAsync(customerCode);
        return customerData.Type == CustomerTypes.LegalEntity
            ? _configuration.LegalEntityProductCode
            : _configuration.PhysicalPersonProductCode;
    }

    public async Task<string> GetBranchAsync()
    {
        return await Task.FromResult(_configuration.Branch);
    }

    public async Task<string> GetSubBranchAsync()
    {
        return await Task.FromResult(_configuration.SubBranch);
    }
}
