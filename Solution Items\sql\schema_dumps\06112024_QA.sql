USE [EWallet]
GO
/****** Object:  Table [dbo].[AuthorizationRequests]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AuthorizationRequests](
	[Id] [uniqueidentifier] NOT NULL,
	[RequestorWalletId] [uniqueidentifier] NOT NULL,
	[TargetWalletId] [uniqueidentifier] NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[ExpiresAt] [datetime2](7) NOT NULL,
	[UpdatedAt] [datetime2](7) NULL,
	[Status] [nvarchar](25) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DiscountTransactions]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DiscountTransactions](
	[DiscountTransactionId] [uniqueidentifier] NOT NULL,
	[Value] [decimal](10, 2) NOT NULL,
	[TransactionType] [nvarchar](25) NULL,
	[SubscriptionPlanType] [nvarchar](25) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Limits]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Limits](
	[Id] [uniqueidentifier] NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[Active] [bit] NOT NULL,
	[TimeFrame] [nvarchar](25) NULL,
	[TransactionType] [nvarchar](25) NULL,
	[PermissionId] [uniqueidentifier] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ServiceAudit]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ServiceAudit](
	[Id] [uniqueidentifier] NOT NULL,
	[Timestamp] [datetime2](7) NOT NULL,
	[Host] [varchar](100) NOT NULL,
	[ServiceName] [varchar](400) NOT NULL,
	[Application] [uniqueidentifier] NULL,
	[ClientRequestPath] [varchar](200) NULL,
	[ClientSession] [varchar](100) NULL,
	[RequestTextData] [nvarchar](max) NULL,
	[ResponseTextData] [nvarchar](max) NULL,
	[ErrorData] [nvarchar](max) NULL,
	[ExecutionTime] [time](7) NULL,
	[EndTime] [datetime2](7) NULL,
	[ExtraTextData] [nvarchar](max) NULL,
 CONSTRAINT [PK_ServiceAudit_1] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionBundles]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionBundles](
	[Id] [uniqueidentifier] NOT NULL,
	[SubscriptionId] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[Value] [int] NOT NULL,
	[TransactionType] [nvarchar](25) NULL,
 CONSTRAINT [PK_SubscriptionBundles] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionPlans]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionPlans](
	[SubscriptionPlanid] [uniqueidentifier] NOT NULL,
	[SubscriptionPlanType] [nvarchar](25) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transactions]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transactions](
	[TransactionId] [uniqueidentifier] NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[UpdatedAt] [datetime2](7) NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[TransactionType] [nvarchar](255) NULL,
	[TransactionSubType] [nvarchar](255) NULL,
	[Currency] [nvarchar](255) NOT NULL,
	[DebtorIban] [nvarchar](255) NOT NULL,
	[DebtorName] [nvarchar](255) NULL,
	[Reference] [nvarchar](255) NULL,
	[CreditorIban] [nvarchar](255) NOT NULL,
	[CreditorName] [nvarchar](255) NOT NULL,
	[TransactionDate] [datetime2](7) NULL,
	[Reason] [nvarchar](255) NOT NULL,
	[Status] [nvarchar](255) NOT NULL,
	[Result] [nvarchar](max) NULL,
	[ResultReason] [nvarchar](max) NULL,
	[BatchId] [uniqueidentifier] NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[SubmittedBy] [nvarchar](255) NOT NULL,
	[ApprovedBy] [nvarchar](255) NULL,
	[ExecutedBy] [nvarchar](255) NULL,
	[ExecutedAs] [nvarchar](255) NULL,
	[RequestJson] [nvarchar](max) NULL,
	[RejectedBy] [nvarchar](255) NULL,
	[Valeur] [datetime2](7) NULL,
	[IsInstant] [bit] NULL,
	[Commission] [decimal](10, 2) NULL,
 CONSTRAINT [PK_Transactions] PRIMARY KEY CLUSTERED 
(
	[TransactionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserPermissions]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserPermissions](
	[Id] [uniqueidentifier] NOT NULL,
	[UserId] [nvarchar](250) NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[Submit] [bit] NOT NULL,
	[BalanceView] [bit] NOT NULL,
	[TransactionView] [bit] NOT NULL,
	[Admin] [bit] NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[AssignedBy] [nvarchar](250) NOT NULL,
	[RevokedBy] [nvarchar](250) NULL,
	[Approve] [bit] NULL,
	[InheritsAuthorizations] [bit] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WalletPermissions]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WalletPermissions](
	[Id] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[ExternalWalletId] [uniqueidentifier] NOT NULL,
	[Submit] [bit] NOT NULL,
	[BalanceView] [bit] NOT NULL,
	[TransactionView] [bit] NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreationDate] [datetime2](7) NOT NULL,
	[AssignedBy] [nvarchar](250) NOT NULL,
	[RevokedBy] [nvarchar](250) NULL,
	[Approve] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Wallets]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Wallets](
	[WalletId] [uniqueidentifier] NOT NULL,
	[ConnectedIban] [nvarchar](40) NULL,
	[VatNumber] [nvarchar](25) NOT NULL,
	[WalletAccount] [nvarchar](40) NULL,
	[WalletName] [nvarchar](250) NOT NULL,
	[RegistrationDate] [datetime2](7) NOT NULL,
	[WalletAccountCreatedAt] [datetime2](7) NULL,
	[OwnerUserId] [nvarchar](250) NOT NULL,
	[OrganizationName] [nvarchar](250) NULL,
	[OwnerCustomerCode] [nvarchar](25) NULL,
	[IsCorporateUser] [bit] NOT NULL,
 CONSTRAINT [PK_Wallets] PRIMARY KEY CLUSTERED 
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WalletSubscriptions]    Script Date: 6/11/2024 1:57:44 μμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WalletSubscriptions](
	[SubscriptionId] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[Duration] [smallint] NOT NULL,
	[AllowedUsers] [smallint] NOT NULL,
	[StartDate] [datetime2](7) NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[SubscriptionPlanType] [nvarchar](20) NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
 CONSTRAINT [PK_WalletSubscriptions] PRIMARY KEY CLUSTERED 
(
	[SubscriptionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[DiscountTransactions] ADD  CONSTRAINT [DF_DiscountTransactions_DiscountTransactionId]  DEFAULT (newid()) FOR [DiscountTransactionId]
GO
ALTER TABLE [dbo].[Limits] ADD  CONSTRAINT [DF_Limits_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[Limits] ADD  DEFAULT ((1)) FOR [Active]
GO
ALTER TABLE [dbo].[SubscriptionPlans] ADD  CONSTRAINT [DF_SubscriptionPlans_SubscriptionPlanid]  DEFAULT (newid()) FOR [SubscriptionPlanid]
GO
ALTER TABLE [dbo].[Transactions] ADD  DEFAULT ((0)) FOR [Commission]
GO
ALTER TABLE [dbo].[UserPermissions] ADD  CONSTRAINT [DF_UserPermissions_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[UserPermissions] ADD  DEFAULT ((0)) FOR [InheritsAuthorizations]
GO
ALTER TABLE [dbo].[WalletPermissions] ADD  CONSTRAINT [DF_WalletPermissions_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[Wallets] ADD  CONSTRAINT [DF_Wallets_walletId]  DEFAULT (newid()) FOR [WalletId]
GO
ALTER TABLE [dbo].[Wallets] ADD  DEFAULT ((0)) FOR [IsCorporateUser]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Title', @value=N'ServiceAudit' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ServiceAudit'
GO
