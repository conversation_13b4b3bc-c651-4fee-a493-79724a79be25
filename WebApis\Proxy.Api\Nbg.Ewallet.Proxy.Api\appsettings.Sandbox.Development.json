{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },
    "SandboxServiceConfig": {
        "Sandbox": "SandboxApps",
        "StorageService": "SQLServer"
    },

    "ProxySettings": {
        "SkipAddSqlServer": true
    },

    "ConnectionStrings": {
        "Audit": "Server=v000080065;database=InternetBankingAudit;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "IBank": "Server=v000080065;database=InternetBanking;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "uniqueMessage": "Server=V000080065;initial catalog=BigDataIbank;Integrated Security=SSPI;Persist Security Info=False;Encrypt=false",
        "appSettings": "Server=v000010349\\S1;database=APIConfig;Integrated Security=SSPI;Persist Security Info=False;Encrypt=false"
    },

    "AuditMiddleware": {
        "ConnectionName": "Audit",
        "UseSqlServer": true,
        "UseBigDataClient": false,
        "SetPrimarySqlServer": true,
        "SetPrimaryBigDataClient": false,
        "RequestIdHeaderName": "request-id",
        "ServiceNamePrefix": "nbg.ewallet.sandbox.api/v1",
        "IgnoredPaths": [
            "/favicon.ico"
        ],
        "RequestHttpHeaders": [
            "ByPassHeaders"
        ],
        "ResponseHttpHeaders": [
            "ByPassHeaders"
        ],
        "SensitiveProperties": []
    },

    //"MultiSinkAuditWriter": {
    //    "primarySink": "kafka",
    //    "sinks": {
    //        "kafka": {
    //            "kafkaClient": {
    //                "kafkaTopic": "dev-trlog-ibank-ingestion-16partitions",
    //                "producerOptions": {
    //                    "bootstrap.servers": "pkc-1wvvj.westeurope.azure.confluent.cloud:9092",
    //                    "security.protocol": "Plaintext",
    //                    "sasl.username": "",
    //                    "sasl.mechanism": "PLAIN",
    //                    "delivery.timeout.ms": "5000",
    //                    "batch.size": "********",
    //                    "message.max.bytes": "********",
    //                    "acks": "1",
    //                    "compression.type": "snappy",
    //                    "linger.ms": "50"
    //                }
    //            }
    //        }
    //    }
    //},

    "Authentication": {
        "Authority": "https://myqa.nbg.gr/identity",
        "ApiName": "D8864CC2-56C2-43EB-ABD9-AF10484A2A20",
        "ApiSecret": "01718ECF-A3C7-4F2C-8AAD-252EC793EC23",
        "RequiredScope": "sandbox-ewallet-api-v1",
        "ManagementApiScope": "ewallet-management"
    },

    "ProxyMiddleware": {
        "Variables": {
            "CoreServiceUrl": "https://localhost/Nbg.Ewallet.Api.Sandbox"
        }
    },

    "Healthcheck": {
        "CheckAllocatedMemory": true,
        "CheckUris": true,
        "UriCheckOptions": [
            {
                "Uri": "https://localhost/Nbg.Ewallet.Api.Sandbox/index.html",
                "HttpClientName": "default",
                "HttpMethod": "GET",
                "TimeOutMilliseconds": 100000,
                "ExpectedCodes": [ 200 ]
            }
        ]
    },

    "CorporateUserAuthorizationPolicy": {
        "ApplyPolicy": false
    },

    "Sca": {
        "BaseAddress": "https://CoreAppLayerQA.nbg.gr",
        "ChallengeEndpoint": "/Nbg.NetCore.Sca.Core/sca/challenge",
        "ValidateEndpoint": "/Nbg.NetCore.Sca.Core/sca/validate",
        "DisableScaWhitelisting": false,
        "ScaTokenExpirationInSeconds": 300
    },

    //"CustomRestSca": {
    //    "BaseAddress": "https://CoreAppLayerQA.nbg.gr",
    //    "ChallengeEndpoint": "/Nbg.NetCore.Sca.Core/sca/challenge",
    //    "ValidateEndpoint": "/Nbg.NetCore.Sca.Core/sca/validate",
    //    "DisableScaWhitelisting": false,
    //    "ScaTokenExpirationInSeconds": 300
    //},

    //"HttpClient": {
    //    "ClientRestSca": {
    //        "BaseAddress": "https://CoreAppLayerQA.nbg.gr", //"BaseAddress": "https://localhost/Nbg.NetCore.Sca.Core/",
    //        "MaxConnectionsPerServer": 20,
    //        "DangerousAcceptAnyServerCertificate": false,
    //        "Timeout": "00:03:00",
    //        "UseProxy": false,
    //        "ProxyUrl": "http://wsa.central.nbg.gr:8080"
    //    }
    //},

    "SmsOtpSettings": {
        "enableSmsOtp": true,
        "UseSandboxSca": false
    },
    "UserIdRequestTransformer": {
        "HttpHeadersMappings": {
            "UserId": {
                "SourcePath": "preferred_username",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true,
                "Source": "Claim",
                "ThrowIfNotFoundFromSource": true
            }
        }
    }

}
