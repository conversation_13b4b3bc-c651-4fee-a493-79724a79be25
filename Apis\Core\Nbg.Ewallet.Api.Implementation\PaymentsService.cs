﻿using System;
using System.Threading.Tasks;
using ibank.ThirdParty.Types;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Payments;

namespace Nbg.Ewallet.Api.Implementation;
public class PaymentsService : IPaymentsService
{
    private readonly ITransactionService _transactionService;
    private readonly IPaymentsRequestProvider _paymentsRequestProvider;
    private readonly IBatchPaymentsRequestProvider _paymentsExecuteRequestProvider;

    public PaymentsService(
        ITransactionService transactionService,
        IPaymentsRequestProvider paymentsRequestProvider,
        IBatchPaymentsRequestProvider paymentsExecuteRequestProvider)
    {
        _transactionService = transactionService;
        _paymentsRequestProvider = paymentsRequestProvider;
        _paymentsExecuteRequestProvider = paymentsExecuteRequestProvider;
    }

    public async Task<CommissionResponse> CommissionAsync(ExtendedPaymentsRequest request, Guid walletId)
    {
        var response = await _transactionService.GetCommissionAsync(_paymentsRequestProvider, request, walletId);
        return response;
    }

    public async Task<BatchCommissionResponse> BatchCommissionAsync(BatchPaymentsCommissionRequest request, Guid walletId)
    {
        BatchPaymentsRequest batchPaymentsRequest = new()
        {
            Batch = request.Batch,
            BatchId = request.BatchId,
        };
        var response = await _transactionService.GetCommissionAsync(_paymentsExecuteRequestProvider, batchPaymentsRequest, walletId);
        return response;
    }

    public async Task<PaymentsResponse> ExecuteAsync(BatchPaymentsRequest request, string walletId)
    {
        var response = await _transactionService.ExecuteAsync(_paymentsExecuteRequestProvider, request, walletId);
        return response;
    }

    public async Task<ExtendedPaymentResponse> PayAsync(ExtendedPaymentsRequest request, string walletId)
    {
        var response = await _transactionService.ExecuteAsync(_paymentsRequestProvider, request, walletId);
        return response;
    }

    public async Task<PaymentsForecastResponse> ExecuteForecastAsync(BatchPaymentsRequest request, string walletId)
    {
        var response = await _transactionService.ExecuteForecastAsync(_paymentsExecuteRequestProvider, request, walletId);
        return PaymentsForecastResponse.ToForecastResponse(response);
    }

    public async Task<ExtendedPaymentForecastResponse> PayForecastAsync(ExtendedPaymentsRequest request, string walletId)
    {
        var response = await _transactionService.ExecuteForecastAsync(_paymentsRequestProvider, request, walletId);
        return ExtendedPaymentForecastResponse.ToForecastResponse(response);
    }
}
