﻿Feature: Wallet internal user permissions
Scenario Outline: As an IB user, I want to give permissionss to an internal user
  Given I am logged in as the user <userId>
    #And As <userId> i am an owner of a wallet
    And  As an owner of a wallet
    When I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    Then the <internalUserId> must have <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe> permissions
   
 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId          | internalUserId  | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe | inheritsAuthorizations |
        | 0               | 2               | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    | true                   |
        
Scenario Outline: As an IB user, I want to retrive permissionss for an internal user
  Given I am logged in as the user <userId>
    And As an owner of a wallet
    Then the <internalUserId> must have <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe> permissions

 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId          | internalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe | inheritsAuthorizations |
        | 0               | 2              | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    | true                   |

 Scenario Outline: Revoke permissions for an internal user
  Given I am logged in as the user <userId>
    And As an owner of a wallet
    When I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    And I revoke permissions for user <internalUserId>
    Then Permissions are revoked for <internalUserId>
   
 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId          |  internalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe | inheritsAuthorizations |
        | 0               |  2              | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    | true                   |

 Scenario Outline: Retrieve only active permissions when sowAll flag is false
  Given I am logged in as the user <userId>
    And As an owner of a wallet
    When I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    And I revoke permissions for user <internalUserId> for wallet <walletName>
    And I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    Then only the active permission is retrieved for user <internalUserId> for wallet <walletName>
   
 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    | internalUserId | admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe | inheritsAuthorizations |
        | 0         | 2              | false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    | true                   |

 Scenario Outline: Fail to assign permission to self
  Given I am logged in as the user <userId>
    And As an owner of a wallet
    When I give permissions to user <internalUserId> and this user is me
    Then no permission is applied to user <internalUserId> for wallet <walletName>
   
 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
        | userId    |  internalUserId        | admin  | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
        | 0         |   0                    | false  | true    | true        | true   | true            |  WalletToWallet | 1000   |    YEARLY |

 Scenario Outline: Fail to revoke permission from self
  Given I am logged in as the user <userId>
    #And I have registered a <walletName>
    And As an owner of a wallet
    When I have permissions as user <internalUserId>
    And I revoke permissions for user <userId>
    Then no permission is revoked to user <internalUserId>
   
 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
    | userId    |  internalUserId        | admin  | approve | balanceView | submit | transactionView | transactionType | amount | timeframe |
    | 0         |   2                    | false  | true    | true        | true   | true            |  WalletToWallet | 1000   | YEARLY    |

 Scenario Outline: Retrieve only active permissions for user when showAll flag is false for all wallet users
  Given I am logged in as the user <userId>
    #And I have registered a <walletName>
    And As an owner of a wallet
    When I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    And I revoke permissions for user <internalUserId>
    And I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    Then only the active permissions are retrieved for <walletName> users
   
   
 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
    | userId    | internalUserId |  admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe | inheritsAuthorizations |
    | 0         | 2              |  false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    | true                   |


 Scenario Outline: Retrieve all active permissions for user when showAll flag is false for all wallet users
  Given I am logged in as the user <userId>
    And As an owner of a wallet
    When I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    And I revoke permissions for user <internalUserId>
    And I give <internalUserId> permissions with <admin> and <approve> and <balanceView> and <submit> and <transactionView> and <inheritsAuthorizations> and <transactionType> and <amount> and <timeframe>
    Then all the active permissions are retrieved
   
   
 #make sure the user under the userId Column is defined in appsettings.Development.json so the password can be retrieved
    Examples:
    | userId    | internalUserId |  admin | approve | balanceView | submit | transactionView | transactionType | amount | timeframe | inheritsAuthorizations |
    | 0         | 2              |  false | true    | true        | true   | true            | WalletToWallet  | 1000   | YEARLY    | true                   |
