﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Admin;

public class B2BChannel
{
    /// <summary>
    /// Channel id
    /// </summary>
    [DataMember(Name = "id")]
    public Guid ChannelId { get; set; }

    /// <summary>
    /// name
    /// </summary>
    [DataMember(Name = "name")]
    public string ChannelName { get; set; }

    /// <summary>
    /// FirstName
    /// </summary>
    [DataMember(Name = "url")]
    public string ChannelUrl { get; set; }

    /// <summary>
    /// LastName
    /// </summary>
    [DataMember(Name = "productid")]
    public string ProductId { get; set; }
}
