﻿//using System;
//using System.Globalization;
//using Newtonsoft.Json;

//namespace Nbg.Ewallet.Api.Types.TimeConverters;

//public class UtcDateConverter : JsonConverter<DateTime>
//{
//    private const string Format = "yyyy-MM-dd";

//    public override DateTime ReadJson(JsonReader reader, Type objectType, DateTime existingValue, bool hasExistingValue, JsonSerializer serializer)
//    {
//        return DateTime.ParseExact(reader.Value.ToString(), Format, CultureInfo.InvariantCulture).Date;
//    }

//    public override void WriteJson(JsonWriter writer, DateTime value, JsonSerializer serializer)
//    {
//        writer.WriteValue(value.Date.ToString(Format, CultureInfo.InvariantCulture));
//    }
//}
