﻿using System;
using System.Collections.Generic;
using Nbg.Ewallet.Api.Sandbox.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static Random _random = new();
    private static List<SandboxAvailableAccount> GenerateAvailableAccounts(List<SandboxWallet> wallets)
    {
        var result = new List<SandboxAvailableAccount>();

        foreach (var wallet in wallets)
        {
            result.Add(new SandboxAvailableAccount
            {
                IBAN = wallet.WalletAccount,
                Alias = SandBoxRandomDataHelper.GenerateRandomText(10),
                AvailableBalance = 10_000M,
                LedgerBalance = 10_000M,
                UserId = wallet.OwnerUserId,
                Currency = "EUR",
            });

            // Randomly decide whether to add a second account for the same user
            if (_random.Next(0, 3) == 0)
            {
                // Add a second account for the same user
                result.Add(new SandboxAvailableAccount
                {
                    IBAN = SandBoxRandomDataHelper.GenerateIBAN(),
                    <PERSON>as = SandBoxRandomDataHelper.GenerateRandomText(10),
                    AvailableBalance = 5_000M,
                    LedgerBalance = 5_000M,
                    UserId = wallet.OwnerUserId,
                    Currency = "EUR",
                });
            }
        }

        return result;
    }
}
