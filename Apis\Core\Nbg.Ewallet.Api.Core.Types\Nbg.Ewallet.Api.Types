<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Nbg.Ewallet.Api.Types</name>
    </assembly>
    <members>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ChannelId">
            <summary>
            Channel id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ChannelName">
            <summary>
            name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ChannelUrl">
            <summary>
            FirstName
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ProductId">
            <summary>
            LastName
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Admin.CommonSuccessResult">
            <summary>
            Status Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.CommonSuccessResult.Success">
            <summary>
            Request Status Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.Rule.RuleName">
            <summary>
            name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.Rule.Owner">
            <summary>
            owner
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ConfigureB2bRulesRequest.Rules">
            <summary>
            name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ConfigureB2bRulesRequest.rulesType">
            <summary>
            owner
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchRequest.vatNumber">
            <summary>
            Vat Number
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchRequest.name">
            <summary>
            Company Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchRequest.walletAccount">
            <summary>
            Wallet Account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchResponse.name">
            <summary>
            Company Name used inside wallet environment
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchResponse.vatNumber">
            <summary>
            Company vat number
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchResponse.walletAccount">
            <summary>
            Company’s wallet account
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.AvailableUsersResult">
            <summary>
            Available Users Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.AvailableUsersResult.AvailableUsers">
            <summary>
            List of Available Users
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.CompanyDetails">
            <summary>
            CompanyDetails Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.CompanyId">
            <summary>
            the id of the company
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.Name">
            <summary>
            the Name of the company
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.IBAN">
            <summary>
            Account that will be used to load wallet account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.RegistrationDate">
            <summary>
            Registration date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.VatNumber">
            <summary>
            Vat Number
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.Title">
            <summary>
            Company’s Name used inside wallet environment
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.RegisteredSubscriptionPlans">
            <summary>
            Plans company is registered to
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyDetails.WalletAccount">
            <summary>
            Wallet Account that is created during registration procedure
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.CompanyProfile">
            <summary>
            Get Company’s profile settings
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyProfile.Name">
            <summary>
            Company Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyProfile.Iban">
            <summary>
            Company Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyProfile.VatNumber">
            <summary>
            Company Vat Number
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyProfile.Users">
            <summary>
            Company’s Registered Users
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyProfile.RegistrationDate">
            <summary>
            Company’s Registration Date
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse">
            <summary>
            CompanyRegister information response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.CompanyId">
            <summary>
            the CompanyId
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.Name">
            <summary>
            User Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.RegistrationDate">
            <summary>
            Registration Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.Iban">
            <summary>
            Company IBAN
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.VatNumber">
            <summary>
            Company VAT
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.WalletAccount">
            <summary>
            Company Waallet Account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.RegisteredSubscriptionPlans">
            <summary>
            Company Registered subscriptions
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanyRegisterResponse.RegisteredUsers">
            <summary>
            Compny's  registered users
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanRequest">
            <summary>
            Company Subscription Plan Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanRequest.CompanyId">
            <summary>
            Company Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanRequest.PlanId">
            <summary>
            Subscription Plan Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanRequest.CreationDate">
            <summary>
            Creation Datetime
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanResponse">
            <summary>
            Company Subscription Plan Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanResponse.PlanId">
            <summary>
            Registered Subscription Plan Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanResponse.StartDate">
            <summary>
            Start Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanResponse.EndDate">
            <summary>
            End Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanResponse.Description">
            <summary>
            Subscription plan Description
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.CompanySubscriptionPlanResponse.Name">
            <summary>
            Subscription plan Name
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.RegisteredSubscriptionPlan">
            <summary>
            Registered Subscription Plan
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.RegisteredSubscriptionPlan.Id">
            <summary>
            Id of RegisteredSubscriptionPlan
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.RegisteredSubscriptionPlan.SubscriptionPlanId">
            <summary>
            Subscription Plan Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.RegisteredSubscriptionPlan.StartDate">
            <summary>
            Start Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.RegisteredSubscriptionPlan.EndDate">
            <summary>
            End Date
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.RegisteredUser">
            <summary>
            Company's Registered UsersResult
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.RegisteredUser.id">
            <summary>
            Id of Registered User
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.RegisteredUser.craCode">
            <summary>
            User Sydipel
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.RegisteredUsersResult">
            <summary>
            Company's Registered UsersResult
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.RegisteredUsersResult.RegisteredUsers">
            <summary>
            Company's Registered Users List
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.SearchUserRequest">
            <summary>
            Search for a Company User Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.SearchUserRequest.vatNumber">
            <summary>
            Vat Number
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.SearchUserRequest.name">
            <summary>
            Name
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.TerminateSubscriptionPlanRequest">
            <summary>
            Terminate a company's subscription plan
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.TerminateSubscriptionPlanRequest.CompanyId">
            <summary>
            Company Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.TerminateSubscriptionPlanRequest.SubscriptionPlanId">
            <summary>
            Subscription Plan Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Company.UpdateCompanyRequest">
            <summary>
            UpdateCompany Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.UpdateCompanyRequest.CompanyId">
            <summary>
            the CompanyId
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.UpdateCompanyRequest.WalletName">
            <summary>
            Company’s Wallet Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Company.UpdateCompanyRequest.Iban">
            <summary>
            Account that will be credited
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.CompanyRegisterRequest">
            <summary>
            Company Register Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.CompanyRegisterRequest.Name">
            <summary>
            Company Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.CompanyRegisterRequest.Iban">
            <summary>
            Account that will be credited
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Consents.Consent">
            <summary>
            Consent response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.Consent.ConsentId">
            <summary>
            Consent id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.Consent.Status">
            <summary>
            Consent Status
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.DebtorName">
            <summary>
            Debtor Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.DebtorIban">
            <summary>
            Debtor Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.DebtorTelephone">
            <summary>
            Debtor Telephone
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.Payments">
            <summary>
            List of Payments
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.MassTransactionId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.MassStatement.PaymentCode">
            <summary>
            Payment Code
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.MassStatement.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.MassStatement.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Payment">
            <summary>
            Debtor Telephone
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payment.PaymentCode">
            <summary>
            Payment Code
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payment.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payment.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.PaymentsCreateRequest">
            <summary>
            Crrate Payment
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.DebtorName">
            <summary>
            Debtor Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.DebtorIban">
            <summary>
            Debtor Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.DebtorTelephone">
            <summary>
            Debtor Telephone
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.Payments">
            <summary>
            List of Payments
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsExecuteRequest.MassTransactionId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.PaymentsPayRequest">
            <summary>
            Payments Pay Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsPayRequest.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsPayRequest.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsPayRequest.PaymentCode">
            <summary>
            Payment Code
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsPayRequest.DebtorName">
            <summary>
            Debtor Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsPayRequest.DebtorIban">
            <summary>
            Debtor Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsPayRequest.DebtorTelephone">
            <summary>
            Debtor Telephone
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.PaymentsResponse">
            <summary>
            Payments Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsResponse.Success">
            <summary>
            Indication of whether transaction was successful
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsResponse.MassTransactionId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsStatementsResponse.Statements">
            <summary>
            List of Statements
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.Success">
            <summary>
            Indication of whether transaction was successful
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.TransactionId">
            <summary>
            Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersWalletRequest.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersWalletRequest.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersWalletRequest.DebtorName">
            <summary>
            Debtor Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersWalletRequest.DebtorIban">
            <summary>
            Debtor Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersWalletRequest.CreditWalletAccount">
            <summary>
            Credit Wallet Account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersWalletRequest.CreditName">
            <summary>
            Credit Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersWalletRequest.RemittanceInformation">
            <summary>
            Remittance Information
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.GetLimitsResponse">
            <summary>
            Get Limits Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetLimitsResponse.Limits">
            <summary>
            List of Limits
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.GetRightResponse">
            <summary>
            Get Right Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetRightResponse.Rights">
            <summary>
            List of Rights
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.GetUserResponse">
            <summary>
            Get User Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.UserId">
            <summary>
            User Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.Username">
            <summary>
            UserName
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.Type">
            <summary>
            User Type
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.RegistrationDate">
            <summary>
            Registration Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.CompanyId">
            <summary>
            Company Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.CraCode">
            <summary>
            User Sydipel
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.Name">
            <summary>
            Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.Telephone">
            <summary>
            telephone
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetUserResponse.RegisteredUser">
            <summary>
            Ib Username
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.GetWalletResponse">
            <summary>
            Get Wallet Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetWalletResponse.Wallets">
            <summary>
            List of Wallets
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Limit">
            <summary>
            Limits
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Limit.Id">
            <summary>
            Limit Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Limit.Description">
            <summary>
            Limit Description
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Limit.Value">
            <summary>
            Limit Value
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Limit.StartDate">
            <summary>
            Start Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Limit.EndDate">
            <summary>
            End Date
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Right">
            <summary>
            Right Definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Right.Id">
            <summary>
            Right Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Right.Description">
            <summary>
            Right Description
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Right.Value">
            <summary>
            Right Value
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Right.StartDate">
            <summary>
            Start Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Right.EndDate">
            <summary>
            End Date
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserAddRequest">
            <summary>
            Adds a new user to an existing company
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddRequest.Username">
            <summary>
            UserName
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddRequest.CompanyId">
            <summary>
            Company Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddRequest.CraCode">
            <summary>
            User Sydipel
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddRequest.Type">
            <summary>
            User Type
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddRequest.Name">
            <summary>
            Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddRequest.Telephone">
            <summary>
            Telephone
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserAddResponse">
            <summary>
            User Add Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddResponse.Success">
            <summary>
            Indication of whether the request was successful or not 
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserAddResponse.UserId">
            <summary>
            User Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserLimitsRequest">
            <summary>
            User Limits Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserLimitsRequest.UserId">
            <summary>
            User Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserLimitsRequest.Limits">
            <summary>
            List of Limits
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserLimitsResponse">
            <summary>
            User Limits Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserLimitsResponse.Success">
            <summary>
            Indication of whether Subscription plan was terminated or not
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserRemoveRequest">
            <summary>
            User Remove Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserRemoveRequest.UserId">
            <summary>
            The User Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserRemoveResponse">
            <summary>
            User Remove Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserRemoveResponse.Success">
            <summary>
            Indication of whether the request was successful or not 
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserRightsRequest">
            <summary>
            User Rights Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserRightsRequest.UserId">
            <summary>
            UserId
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserRightsRequest.Rights">
            <summary>
            List Rights
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserUpdateRequest">
            <summary>
            Update User
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateRequest.UserId">
            <summary>
            User Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateRequest.CompanyId">
            <summary>
            UserName
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateRequest.CraCode">
            <summary>
            User Type
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateRequest.Type">
            <summary>
            User Type
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateRequest.Name">
            <summary>
            Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateRequest.Telephone">
            <summary>
            telephone
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.UserUpdateResponse">
            <summary>
            User Update Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateResponse.Success">
            <summary>
            Indication of whether the request was successful or not 
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.UserUpdateResponse.UserId">
            <summary>
            User Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet">
            <summary>
            Wallet
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Id">
            <summary>
            Wallet Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAccount">
            <summary>
            Wallet Account
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Statement">
            <summary>
            Statement
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Id">
            <summary>
            StatementId
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Description">
            <summary>
            Statement Description
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.DebtorIban">
            <summary>
            Debtor Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.CreditIban">
            <summary>
            Credit Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.TransactionDate">
            <summary>
            Transaction Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.BookingDate">
            <summary>
            Booking Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Type">
            <summary>
            Type
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletBalanceRequest">
            <summary>
            Wallet Balance Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletBalanceRequest.WalletId">
            <summary>
            WalletId
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletBalanceResponse">
            <summary>
            Wallet Balance Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletBalanceResponse.LedgerBalance">
            <summary>
            Account Ledger Balance
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletBalanceResponse.AvailableBalance">
            <summary>
            Account Available Balance
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletInfoResponse">
            <summary>
            Wallet Info Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletInfoResponse.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletInfoResponse.WalletAccount">
            <summary>
            Wallet Account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletInfoResponse.CompanyId">
            <summary>
            Company Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletLoadRequest">
            <summary>
            Wallet Load Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadRequest.WalletId">
            <summary>
            wallet Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadRequest.Amount">
            <summary>
            Load the Wallet
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadRequest.Currency">
            <summary>
            Load the Wallet
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletLoadResponse">
            <summary>
            Wallet Load Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadResponse.Success">
            <summary>
            Indication of whether Account was loaded
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadResponse.Balance">
            <summary>
            Account Balance after loading
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletStatementsRequest">
            <summary>
            Wallet Statements Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.FromDate">
            <summary>
            From Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.ToDate">
            <summary>
            To Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.Export">
            <summary>
            Export to file
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.FileType">
            <summary>
            File type: csv/pdf
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletStatementsResponse">
            <summary>
            Wallet Statements Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsResponse.Statements">
            <summary>
            List of Wallet Statements
            </summary>
        </member>
    </members>
</doc>
