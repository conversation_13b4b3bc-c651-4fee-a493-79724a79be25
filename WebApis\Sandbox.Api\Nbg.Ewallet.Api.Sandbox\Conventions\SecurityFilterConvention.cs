﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Nbg.AspNetCore.Common.Security;

namespace Nbg.Ewallet.Api.Sandbox.Conventions;

public class SecurityFilterConvention : IControllerModelConvention
{
    public void Apply(ControllerModel controller)
    {
        var controllerName = controller.ControllerName;

        if (controllerName.StartsWith("Management"))
        {
            controller.Filters.Add(new Allowed4CorporateAttribute());
            controller.Filters.Add(new TypeFilterAttribute(typeof(RequestValidationFilter)));
        }
    }
}
