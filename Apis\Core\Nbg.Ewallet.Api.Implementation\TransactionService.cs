﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Extensions;
using Nbg.Ewallet.Repository.Types.Records;
using Nbg.NetCore.Utilities;
using Transaction = Nbg.Ewallet.Api.Types.Transfers.Transaction;
using TransactionStatus = Nbg.Ewallet.Repository.Types.TransactionStatus;

namespace Nbg.Ewallet.Api.Implementation;

public class TransactionService : ITransactionService
{
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletService _walletService;
    private readonly ITransferRequestProvider _transferRequestProvider;
    private readonly IPaymentsRequestProvider _paymentsRequestProvider;
    private readonly IMapper _mapper;
    private readonly ILogger<TransactionService> _logger;
    private readonly ILimitCalculatorService _limitCalculatorService;
    private readonly ITransactionRepositoryService _transactionRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;

    public TransactionService(
        IWalletRepositoryService walletRepositoryService,
        ILimitCalculatorService limitCalculatorService,
        ITransactionRepositoryService transactionRepositoryService,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletService walletService,
        ITransferRequestProvider transferRequestProvider,
        IPaymentsRequestProvider paymentsRequestProvider,
        IMapper mapper,
        ILogger<TransactionService> logger)
    {

        _walletRepositoryService = walletRepositoryService;
        _limitCalculatorService = limitCalculatorService;
        _transactionRepositoryService = transactionRepositoryService;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletService = walletService;
        _transferRequestProvider = transferRequestProvider;
        _paymentsRequestProvider = paymentsRequestProvider;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<TResponse> ExecuteAsync<TRequest, TResponse, TCommissionResponse>(
        ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider,
        TRequest request,
        string walletId)
    {
        ArgumentNullException.ThrowIfNull(provider);

        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId);
        var validationResult = await provider.ValidateRequest(request, wallet);
        var userId = _httpContextRepositoryService.GetUserId();

        var transactions = await provider.InitiateRepoTransactions(request, wallet, userId);

        var transactionsGrouped = transactions
            .GroupBy(x => x.TransactionSubType)
            .Select(x => new TransactionSubTypeGroup { TransactionSubType = x.Key, Amount = x.Sum(y => y.Amount) })
            .ToList();

        var calculatorContext = new LimitCalculatorContext { WalletId = wallet.WalletId, TransactionSubTypeGroups = transactionsGrouped };
        if (!await _limitCalculatorService.HasSubmitTransferPermissionAsync(calculatorContext))
        {
            throw new SubmitLimitReached();
        }

        if (!await _limitCalculatorService.HasApproveTransferPermissionAsync(calculatorContext))
        {
            transactions.ForEach(x => x.Status = TransactionStatus.PENDING_APPROVAL);
            await _transactionRepositoryService.SaveAllAsync(transactions);
            return provider.EnrichResponse(request, transactions);
        }

        //check if the account is linked to the wallet
        if (validationResult.IsAccountLinkedToWallet)
        {
            await _transactionRepositoryService.SaveAllAsync(transactions);
            return await provider.HandleTransactionExecutionsAsync(request, transactions, wallet);
        }

        return await LoadWalletAndExecute(provider, request, transactions, wallet, validationResult);
    }


    private async Task<Transaction> ApproveAsync<TRequest, TResponse, TCommissionResponse>(
        ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider,
        List<RepoTransaction> transactions,
        RepoWallet wallet)
    {
        var transaction = transactions.First();
        var transferRequest = _mapper.Map<TRequest>(transaction.TransfersRequest);
        if (wallet.WalletAccount.TrimEqual(transaction.DebtorIban))
        {
            await provider.HandleTransactionExecutionsAsync(transferRequest, [transaction], wallet);
            return transaction.MapToTransaction();
        }

        await LoadWalletAndExecute(provider, transferRequest, transactions, wallet);
        return transaction.MapToTransaction();
    }

    private async Task<TResponse> LoadWalletAndExecute<TRequest, TResponse, TCommissionResponse>(
        ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider,
        TRequest request,
        List<RepoTransaction> transactions,
        RepoWallet wallet,
        ValidateRequestResult? validationResult = null)
    {

        validationResult ??= await provider.ValidateRequest(request, wallet);

        //1. calculate final amount with commission
        var commissionResponse = await provider.GetCommissionAsync(request, wallet.WalletId);
        var totalAmountWithCommission = provider.GetTotalAmountWithCommission(request, commissionResponse);

        //2.try to load the wallet with the total amount
        var loadRequest = new WalletLoadRequest
        {
            Amount = totalAmountWithCommission.TotalAmount,
            WalletId = wallet.WalletId,
            Iban = validationResult.IBAN
        };
        var loadResult = await _walletService.Load(wallet.WalletId, loadRequest);

        //3. try to execute the transaction and unload if fails
        Exception paymentException;
        try
        {
            transactions.ForEach(x => x.DebtorIban = wallet.WalletAccount);
            provider.SetNewDebitAccount(request, wallet.WalletAccount);
            await _transactionRepositoryService.SaveAllAsync(transactions);
            return await provider.HandleTransactionExecutionsAsync(request, transactions, wallet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Transaction execution failed. Attempting to unload wallet.");
            paymentException = ex;
        }

        //4. unload the wallet if the transaction execution fails
        try
        {
            await _walletService.Unload(wallet.WalletId, loadRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unload wallet after transaction execution failure.");
            throw;
        }

        if (paymentException is ClientErrorException errorException)
            throw errorException;

        throw new GenericException(paymentException.Message);
    }

    public async Task<Transaction> ApproveAsync(Guid walletId, string transactionId)
    {
        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString());

        var transaction = await _transactionRepositoryService.FindOneByWalletIdAndTransactionIdAsync(walletId.ToString(), transactionId);
        if (transaction.Status != TransactionStatus.PENDING_APPROVAL)
        {
            throw new ApproveTransferWrongStatusException();
        }

        var calculatorContext = new LimitCalculatorContext(walletId, transaction.TransactionSubType, transaction.Amount);
        var hasApproveTransferPermission = await _limitCalculatorService.HasApproveTransferPermissionAsync(calculatorContext);
        if (!hasApproveTransferPermission)
        {
            throw new ApproveTransferPermissionException();
        }

        //this need to be checked
        //var executedAs = permission.AssignedBy;
        //// Internal Users can execute transactions and no impersonation is needed.
        //if (context.ActivePermission.WalletId == walletId) executedAs = userId;

        switch (transaction.TransactionType.Value)
        {
            case TransactionType.Transfer:
                return await ApproveAsync(_transferRequestProvider, [transaction], wallet);
            case TransactionType.Payment:
                return await ApproveAsync(_paymentsRequestProvider, [transaction], wallet);
            default:
                throw new GenericException($"Transaction type {transaction.TransactionType.Value} is not supported for approval.");
        }
    }

    public async Task<Transaction> RejectAsync(Guid walletId, string transactionId)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var transaction = await _transactionRepositoryService.FindOneByWalletIdAndTransactionIdAsync(walletId.ToString(), transactionId);
        if (transaction.Status != TransactionStatus.PENDING_APPROVAL) throw new ApproveTransferWrongStatusException();

        transaction.Status = TransactionStatus.REJECTED;
        transaction.RejectedBy = userId;

        await _transactionRepositoryService.SaveAsync(transaction);

        return transaction.MapToTransaction();
    }

    public async Task<TransactionSubType> GetTransactionSubTypeAsync(string receiverAccount)
    {
        var wallet = await _walletRepositoryService.FindOneByWalletAccountAsync(receiverAccount);
        if (wallet != null) return TransactionSubType.WalletToWallet;
        if (AccountHelpers.IbanIsNbg(receiverAccount)) return TransactionSubType.WalletToNBG;
        else return TransactionSubType.WalletToIBAN;
    }

    public async Task<TCommissionResponse> GetCommissionAsync<TRequest, TResponse, TCommissionResponse>(
        ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider,
        TRequest request,
        Guid walletId)
    {
        return await provider.GetCommissionAsync(request, walletId);
    }

    public async Task<TResponse> ExecuteForecastAsync<TRequest, TResponse, TCommissionResponse>(ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider, TRequest request, string walletId)
    {
        ArgumentNullException.ThrowIfNull(provider);

        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId);
        await provider.ValidateRequest(request, wallet);
        return await provider.TransactionForecastExecutionsAsync(request, wallet);
    }
}
