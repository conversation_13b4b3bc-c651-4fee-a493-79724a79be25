﻿<?xml version="1.0" encoding="utf-8"?>
<!-- https://go.microsoft.com/fwlink/?LinkID=208121 -->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <WebPublishMethod>FileSystem</WebPublishMethod>
        <PublishProvider>FileSystem</PublishProvider>
        <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
        <LastUsedPlatform>Any CPU</LastUsedPlatform>
        <SiteUrlToLaunchAfterPublish />
        <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
        <ExcludeApp_Data>False</ExcludeApp_Data>
        <TargetFramework>net8.0</TargetFramework>
        <ProjectGuid>02A3A2B3-BF4E-42E2-BAAC-098331296B5F</ProjectGuid>
        <SelfContained>false</SelfContained>

        <!-- Update to match the correct environment -->
        <EnvironmentName>QA</EnvironmentName>
        <AppEnvironment>Live</AppEnvironment>
        
        <!-- Optional: Publish path based on environment -->
        <PublishUrl>C:\publish\sandbox\proxy.api</PublishUrl>
        <DeleteExistingFiles>True</DeleteExistingFiles>
    </PropertyGroup>

    <ItemGroup>
        <!-- Exclude wildcarded configs from publish -->
        <Content Update="appsettings.*.json" CopyToPublishDirectory="Never" />
        <Content Update="nlog.*.config" CopyToPublishDirectory="Never" />

        <!-- Explicitly include correct config files -->
        <ResolvedFileToPublish Include="appsettings.Live.json">
            <RelativePath>appsettings.Live.json</RelativePath>
        </ResolvedFileToPublish>
        
        <ResolvedFileToPublish Include="appsettings.Live.QA.json">
            <RelativePath>appsettings.Live.QA.json</RelativePath>
        </ResolvedFileToPublish>

        <ResolvedFileToPublish Include="nlog.Live.QA.config">
            <RelativePath>nlog.Live.QA.config</RelativePath>
        </ResolvedFileToPublish>
    </ItemGroup>
</Project>
