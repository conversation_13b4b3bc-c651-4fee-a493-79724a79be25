﻿using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Implementation.Extensions;
public static class StringExtensions
{
    #region [Accounts]

    public static string GetRemType(this string account)
    {
        return AccountHelpers.IbanIsNbg(account) ? "RNB" : "ROT";
    }

    public static TransferType GetTransferType(this string receiverAccount)
    {
        return AccountHelpers.IbanIsNbg(receiverAccount) ? TransferType.NBG : TransferType.OTHER;
    }

    #endregion
}
