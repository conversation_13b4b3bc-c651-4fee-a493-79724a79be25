﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.CorporateApi;

[DataContract]
public class LinkedProductsResponse
{
    /// <summary>
    /// Version
    /// </summary>
    [DataMember(Name = "version")]
    public int Version { get; set; }

    /// <summary>
    /// List of product info
    /// </summary>
    [DataMember(Name = "products")]
    public List<ProductInfoResponse> Products { get; set; }
}
