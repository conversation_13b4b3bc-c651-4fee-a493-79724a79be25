﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IWalletPermissionsService
{
    Task<WalletPermissionResponse> SetWalletPermissionsForOtherWallets(WalletPermissionsRequest request);
    Task<List<WalletPermission>> GetWalletPermissionsForOtherWallets(Guid walletId, bool showAll);
    Task<List<WalletPermission>> GetWalletPermissionsForExternalWallet(Guid walletId, Guid externalwalletId, bool showAll);
    Task<WalletPermission> RevokeExternalWalletPermissions(Guid walletId, Guid targetWalletId);
}
