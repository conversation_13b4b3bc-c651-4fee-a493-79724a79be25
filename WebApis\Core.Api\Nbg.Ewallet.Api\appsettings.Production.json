{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },

    "ConnectionStrings": {
        "EWallet": "Server=***************,2544;database=EWallet;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False;MultipleActiveResultSets=True",
        "Consents": "Server=S0000A2A035,2544;database=BusinessConsent;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "AccountingEngine": "Server=***************,2544;DataBase=AccountingEngine;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False"
    },

    "CicsHttpConnector": {
        "Host": "https://***************",
        "Port": 2550,
        "MaxConnectionsLimit": 10,
        "Timeout": 20000,
        "LocalCertificateFingerprint": "6829e7791c867090957835852975565fdd21a221",
        "DangerousAcceptAnyServerCertificate": true
    },

    "ServiceAuditSettings": {
        "EnableAudit": true,
        "Application": "8B2D8AD6-83EB-4CAD-A384-22CA1B65F080",
        "PrefixServiceName": "",
        "Database": "EWallet"
    },

    "BigDataAzureClientSettings": {
        "Client": "bigDataStetementsAzureApi",
        "GetStatements": "statements/getStatements/"
    },

    "CorporateApiClientSettings": {
        "Client": "corporateApi",
        "GetAvailableProducts": "corporateManagement/getAvailableProducts/",
        "GetLinkedProducts": "corporateManagement/getLinkedProducts/",
        "UpdateProducts": "corporateManagement/updateProducts/",
        "GetCompanyUsers": "corporateManagement/getCompanyUsers/"
    },

    "AccountsApiClientSettings": {
        "Client": "accountsApi",
        "CallGenericTransfer": "transfers/genericTransferV4/",
        "GetAccountDetails": "accounts/details",
        "Accounts": "accounts/accounts",
        "OpenAccount": "accounts/openAccount",
        "CalculateTransferExpensesCommissions": "transfers/calculateTransferExpensesCommissionsV1/",
        "GetStatementsPdfExport": "accounts/statementpdfexport"
    },

    "TPPApiClientSettings": {
        "Client": "tppApi",
        "Pay": "payments/pay",
        "Commission": "payments/commission",
        "Origin": {
            "NetworkId": "B4C6C6B7-F700-4597-A857-AA9FCDC3A466",
            "TransactionTerminalMachineId": "EWALLETMACHINE",
            "TransactionTerminalId": "724A6B2E-8807-456C-8145-F5B82FC08001", //TODO is this ok?
            "TransactionTerminalSpotId": "DFCE19C8-7E48-45B1-8A9A-FD6834B50ACC", //TODO is this ok?
            "UserId": "456277"
        }
    },

    "AccountsCoreClientSettings": {
        "Client": "accountsCore",
        "OpenAccount": "https://***************:2550/JBOPAC"
    },

    "HttpClient": {
        "default": {
            "MaxConnectionsPerServer": 100,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:1:30",
            "UseProxy": false
        },

        "bigDataStetementsAzureApi": {
            "BaseAddress": "https://bigdataapps.az.nbg.gr/statements/api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "corporateApi": {
            "BaseAddress": "http://localhost/corporatemanagement.api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "accountsApi": {
            "BaseAddress": "http://localhost/accounts.api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "tppApi": {
            "BaseAddress": "https://localhost/thirdpartypayments.ib.core.payments/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "accountsCore": {
            "BaseAddress": "", // 
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "subscriptionApi": {
            "BaseAddress": "", //TODO 
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        }
    },

    "ValidationsControlPolicy": {
        "ApplyPolicy": true,
        "Controls": [ "FullKnownCustomer", "LegalDoc", "FinancialProfile", "ActiveIdentity" ],
        "ValidAccountProductCodes": [ "20018", "20218", "21000", "21201", "22800", "22801", "40018", "40063", "40218", "40240", "40242", "40243", "41000", "41016", "41020", "41022", "41031", "41077", "41201", "41220", "41231", "41240", "41242", "41243", "41277", "41500", "41514", "41600", "41604GL", "41604GV", "41604PL", "41604PV", "41609RE", "41609SE", "41609VA", "41616", "41623", "41630", "41645", "41646", "41647", "41655", "41656", "41658", "41660", "41662", "41664", "41666", "41694", "42800", "42801", "43000", "43009RE", "43009SE", "43009VA", "43014", "43044", "43047", "43055", "43055EVM", "43055EVY", "43055VPM", "43055VPY", "43056", "43058", "43066", "43071", "71000BA", "71000EL", "71000PR", "71201BA", "71201EL", "71201PR", "71240BA", "71240EL", "71240PR", "71242BA", "71242EL", "71242PR", "71243BA", "71243EL", "71243PR", "72800BA", "72800EL", "72800PR", "72801BA", "72801EL", "72801PR" ]
    },

    "AccountingEngine": {
        "ConnectionStringName": "AccountingEngine" //must match the name given in ConnectionStrings section
    },

    // TODO : Update this
    "UrlSettings": {
        "AuthorizationUrl": "https://my.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope=ewallet-api-v1&redirect_uri={{redirect_uri}}&response_type=code"
    },

    "RepositorySettings": {
        "ConnectionName": "EWallet",
        "ApplicationConsentTemplateId": "db77f37f-7113-4460-b602-98868af119d6"
    },

    "CoreApisUrls": {

    },

    "HostCommonConfigurationSettings": {
        "Branch": "700",
        "SubBranch": "4",
        "LegalEntityProductCode": "41201W",
        "PhysicalPersonProductCode": "41000W"
    },

    "CreateAccountSettings": {
        "enableValidations": true
    }
}
