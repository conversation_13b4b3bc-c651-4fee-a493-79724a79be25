sequenceDiagram
	autonumber
	Client->>EWallet.Proxy: /wallet/{walletId}/authorization-requests GET
	EWallet.Proxy->>EWallet.Core: /wallet/{walletId}/authorization-requests GET
	EWallet.Core->>EWallet.DB: SELECT
	note over EWallet.DB: DB Table:  AuthorizationRequests
	EWallet.DB->>EWallet.Core: {List<authorizationRequest>}
	EWallet.Core->>EWallet.Proxy: {List<authorizationRequest>}
	EWallet.Proxy->>Client: {List<authorizationRequest>}