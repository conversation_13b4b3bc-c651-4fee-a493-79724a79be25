{"ConnectionStrings": {"SandboxApps": "Server=(localdb)\\MSSQLLocalDB;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True;Encrypt=False;"}, "SandboxServiceConfig": {"Sandbox": "SandboxApps", "StorageService": "SQLServer"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "SandboxSettings": {"ImportFilePath": "Assets/SandboxImport.json"}}