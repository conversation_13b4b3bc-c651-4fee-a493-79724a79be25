﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// Terms and conditions
/// </summary>
public class SandboxTerms
{
    /// <summary>
    /// Title
    /// </summary>
    [DataMember(Name = "title")]
    public string Title { get; set; }

    /// <summary>
    /// Subject
    /// </summary>
    [DataMember(Name = "subject")]
    public string Subject { get; set; }

    /// <summary>
    /// Subject
    /// </summary>
    [DataMember(Name = "version")]
    public string Version { get; set; }
}
