﻿USE [master]
GO
/****** Object:  Database [EWallet]    Script Date: 19/3/2024 10:50:46 πμ ******/
CREATE DATABASE [EWallet]
GO
ALTER DATABASE [EWallet] SET COMPATIBILITY_LEVEL = 150
GO
IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
begin
EXEC [EWallet].[dbo].[sp_fulltext_database] @action = 'enable'
end
GO
ALTER DATABASE [EWallet] SET ANSI_NULL_DEFAULT OFF 
GO
ALTER DATABASE [EWallet] SET ANSI_NULLS OFF 
GO
ALTER DATABASE [EWallet] SET ANSI_PADDING OFF 
GO
ALTER DATABASE [EWallet] SET ANSI_WARNINGS OFF 
GO
ALTER DATABASE [EWallet] SET ARITHABORT OFF 
GO
ALTER DATABASE [EWallet] SET AUTO_CLOSE OFF 
GO
ALTER DATABASE [EWallet] SET AUTO_SHRINK OFF 
GO
ALTER DATABASE [EWallet] SET AUTO_UPDATE_STATISTICS ON 
GO
ALTER DATABASE [EWallet] SET CURSOR_CLOSE_ON_COMMIT OFF 
GO
ALTER DATABASE [EWallet] SET CURSOR_DEFAULT  GLOBAL 
GO
ALTER DATABASE [EWallet] SET CONCAT_NULL_YIELDS_NULL OFF 
GO
ALTER DATABASE [EWallet] SET NUMERIC_ROUNDABORT OFF 
GO
ALTER DATABASE [EWallet] SET QUOTED_IDENTIFIER OFF 
GO
ALTER DATABASE [EWallet] SET RECURSIVE_TRIGGERS OFF 
GO
ALTER DATABASE [EWallet] SET  DISABLE_BROKER 
GO
ALTER DATABASE [EWallet] SET AUTO_UPDATE_STATISTICS_ASYNC OFF 
GO
ALTER DATABASE [EWallet] SET DATE_CORRELATION_OPTIMIZATION OFF 
GO
ALTER DATABASE [EWallet] SET TRUSTWORTHY OFF 
GO
ALTER DATABASE [EWallet] SET ALLOW_SNAPSHOT_ISOLATION OFF 
GO
ALTER DATABASE [EWallet] SET PARAMETERIZATION SIMPLE 
GO
ALTER DATABASE [EWallet] SET READ_COMMITTED_SNAPSHOT OFF 
GO
ALTER DATABASE [EWallet] SET HONOR_BROKER_PRIORITY OFF 
GO
ALTER DATABASE [EWallet] SET RECOVERY FULL 
GO
ALTER DATABASE [EWallet] SET  MULTI_USER 
GO
ALTER DATABASE [EWallet] SET PAGE_VERIFY CHECKSUM  
GO
ALTER DATABASE [EWallet] SET DB_CHAINING OFF 
GO
ALTER DATABASE [EWallet] SET FILESTREAM( NON_TRANSACTED_ACCESS = OFF ) 
GO
ALTER DATABASE [EWallet] SET TARGET_RECOVERY_TIME = 60 SECONDS 
GO
ALTER DATABASE [EWallet] SET DELAYED_DURABILITY = DISABLED 
GO
ALTER DATABASE [EWallet] SET ACCELERATED_DATABASE_RECOVERY = OFF  
GO
EXEC sys.sp_db_vardecimal_storage_format N'EWallet', N'ON'
GO
ALTER DATABASE [EWallet] SET QUERY_STORE = OFF
GO
USE [EWallet]
GO
/****** Object:  Table [dbo].[ApproveTypes]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ApproveTypes](
	[id] [uniqueidentifier] NOT NULL,
	[typeid] [smallint] NOT NULL,
	[description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_ApproveTypes] PRIMARY KEY CLUSTERED 
(
	[typeid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Limits]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Limits](
	[Id] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[UserId] [nvarchar](250) NOT NULL,
	[TransactionType] [smallint] NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[TimeFrame] [smallint] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Payments]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Payments](
	[PaymentId] [uniqueidentifier] NOT NULL,
	[CreationDate] [datetime2](7) NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[DebtorIban] [nvarchar](25) NOT NULL,
	[DebtorName] [nvarchar](50) NOT NULL,
	[DebtorTelephone] [nvarchar](15) NOT NULL,
	[CreditorIban] [nvarchar](25) NOT NULL,
	[CreditorName] [nvarchar](50) NOT NULL,
	[PaymentCode] [nvarchar](25) NOT NULL,
	[TransactionDate] [datetime2](7) NOT NULL,
	[BookingDate] [datetime2](7) NOT NULL,
	[isSmart] [bit] NOT NULL,
	[Status] [smallint] NOT NULL,
 CONSTRAINT [PK_Payments] PRIMARY KEY CLUSTERED 
(
	[PaymentId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionBundles]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionBundles](
	[Id] [uniqueidentifier] NOT NULL,
	[SubscriptionId] [nvarchar](25) NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[TransactionType] [smallint] NOT NULL,
	[Value] [int] NOT NULL,
 CONSTRAINT [PK_SubscriptionsBundles] PRIMARY KEY CLUSTERED 
(
	[SubscriptionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionPlanTypes]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionPlanTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[SubscriptionPlanTypeId] [smallint] NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_SubscriptionPlanTypes] PRIMARY KEY CLUSTERED 
(
	[SubscriptionPlanTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Subscriptions]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Subscriptions](
	[SubscriptionId] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[PlanId] [smallint] NOT NULL,
	[AllowedUsers] [smallint] NOT NULL,
	[StartDate] [datetime2](7) NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
 CONSTRAINT [PK_Subscriptions] PRIMARY KEY CLUSTERED 
(
	[SubscriptionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionsCommission]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionsCommission](
	[Id] [uniqueidentifier] NOT NULL,
	[PlanId] [smallint] NOT NULL,
	[CommissionId] [nvarchar](250) NOT NULL,
 CONSTRAINT [PK_SubscriptionsCommission] PRIMARY KEY CLUSTERED 
(
	[PlanId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TimeFrameTypes]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TimeFrameTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[TimeFrameTypeId] [smallint] NOT NULL,
	[Description] [nchar](50) NOT NULL,
 CONSTRAINT [PK_TimeFrameTypes] PRIMARY KEY CLUSTERED 
(
	[TimeFrameTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transactions]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transactions](
	[TransactionId] [uniqueidentifier] NOT NULL,
	[CreationDate] [datetime2](7) NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[DebtorIban] [nvarchar](25) NOT NULL,
	[DebtorName] [nvarchar](25) NOT NULL,
	[DebtorTelephone] [nvarchar](15) NOT NULL,
	[CreditorIban] [nvarchar](25) NOT NULL,
	[CreditorName] [nvarchar](250) NOT NULL,
	[TransactionDate] [datetime2](7) NOT NULL,
	[Status] [smallint] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL
 CONSTRAINT [PK_Transactions] PRIMARY KEY CLUSTERED 
(
	[TransactionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TransactionStatuses]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TransactionStatuses](
	[id] [uniqueidentifier] NOT NULL,
	[TransactionStatusId] [smallint] NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_TransactionStatuses] PRIMARY KEY CLUSTERED 
(
	[TransactionStatusId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TransactionTypes]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TransactionTypes](
	[id] [uniqueidentifier] NOT NULL,
	[TransactionTypeId] [smallint] NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_TransactionTypes] PRIMARY KEY CLUSTERED 
(
	[TransactionTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserPermissions]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserPermissions](
	[Id] [uniqueidentifier] NOT NULL,
	[UserId] [nvarchar](250) NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[Submit] [bit] NOT NULL,
	[Approve] [smallint] NOT NULL,
	[BalanceView] [bit] NOT NULL,
	[TransactionView] [bit] NOT NULL,
	[Admin] [bit] NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[AssignedBy] [nvarchar](250) NOT NULL,
	[RevokedBy] [nvarchar](250) NULL,
 CONSTRAINT [PK_UserPermissions] PRIMARY KEY CLUSTERED 
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WalletPermissions]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WalletPermissions](
	[Id] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[ExternalWalletId] [uniqueidentifier] NOT NULL,
	[Submit] [bit] NOT NULL,
	[Approve] [smallint] NOT NULL,
	[BalanceView] [bit] NOT NULL,
	[TransactionView] [bit] NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreationDate] [datetime2](7) NOT NULL,
	[AssignedBy] [nvarchar](250) NOT NULL,
	[RevokedBy] [nvarchar](250) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Wallets]    Script Date: 19/3/2024 10:50:47 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Wallets](
	[WalletId] [uniqueidentifier] NOT NULL,
	[ConnectedIban] [nvarchar](25) NULL,
	[VatNumber] [nvarchar](25) NOT NULL,
	[WalletAccount] [nvarchar](25) NULL,
	[WalletName] [nvarchar](250) NOT NULL,
	[RegistrationDate] [datetime2](7) NOT NULL,
	[WalletAccountCreatedAt] [datetime2](7) NULL,
	[OwnerUserId] [nvarchar](250) NOT NULL,
	[OrganizationName] [nvarchar](250) NULL,
 CONSTRAINT [PK_Wallets] PRIMARY KEY CLUSTERED 
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Index [IX_WalletId]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_WalletId] ON [dbo].[Limits]
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_CreditorIban]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_CreditorIban] ON [dbo].[Payments]
(
	[CreditorIban] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_DebtorIban]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_DebtorIban] ON [dbo].[Payments]
(
	[DebtorIban] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
/****** Object:  Index [PK_WalletId]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [PK_WalletId] ON [dbo].[SubscriptionBundles]
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
/****** Object:  Index [ExpirationDate]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [ExpirationDate] ON [dbo].[Subscriptions]
(
	[ExpirationDate] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
/****** Object:  Index [Wallet ID]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [Wallet ID] ON [dbo].[Subscriptions]
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_CommissionId]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_CommissionId] ON [dbo].[SubscriptionsCommission]
(
	[CommissionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_CreditorIban]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_CreditorIban] ON [dbo].[Transactions]
(
	[CreditorIban] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [IX_DebtorIban]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_DebtorIban] ON [dbo].[Transactions]
(
	[DebtorIban] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
/****** Object:  Index [IX_UserPermissions_expirationDate]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_UserPermissions_expirationDate] ON [dbo].[UserPermissions]
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
/****** Object:  Index [IX_UserPermissions_userid]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_UserPermissions_userid] ON [dbo].[UserPermissions]
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
/****** Object:  Index [IX_WalletPermissions_ExpirationDate]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_WalletPermissions_ExpirationDate] ON [dbo].[WalletPermissions]
(
	[ExpirationDate] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
/****** Object:  Index [IX_WalletPermissions_userid]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [IX_WalletPermissions_userid] ON [dbo].[WalletPermissions]
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [OwnerUserId]    Script Date: 19/3/2024 10:50:47 πμ ******/
CREATE NONCLUSTERED INDEX [OwnerUserId] ON [dbo].[Wallets]
(
	[OwnerUserId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[ApproveTypes] ADD  CONSTRAINT [DF_ApproveTypes_id]  DEFAULT (newid()) FOR [id]
GO
ALTER TABLE [dbo].[Limits] ADD  CONSTRAINT [DF_Limits_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[SubscriptionPlanTypes] ADD  CONSTRAINT [DF_SubscriptionPlanTypes_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[TimeFrameTypes] ADD  CONSTRAINT [DF_TimeFrameTypes_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[TransactionStatuses] ADD  CONSTRAINT [DF_TransactionStatuses_id]  DEFAULT (newid()) FOR [id]
GO
ALTER TABLE [dbo].[TransactionTypes] ADD  CONSTRAINT [DF_TransactionTypes_id]  DEFAULT (newid()) FOR [id]
GO
ALTER TABLE [dbo].[UserPermissions] ADD  CONSTRAINT [DF_UserPermissions_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[WalletPermissions] ADD  CONSTRAINT [DF_WalletPermissions_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[Wallets] ADD  CONSTRAINT [DF_Wallets_walletId]  DEFAULT (newid()) FOR [WalletId]
GO
USE [master]
GO
ALTER DATABASE [EWallet] SET  READ_WRITE 
GO
