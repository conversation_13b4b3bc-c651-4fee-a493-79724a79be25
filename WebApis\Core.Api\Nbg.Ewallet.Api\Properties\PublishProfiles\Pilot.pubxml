﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <DeleteExistingFiles>true</DeleteExistingFiles>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <EnvironmentName>Pilot</EnvironmentName>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <PublishProvider>FileSystem</PublishProvider>
    <PublishUrl>C:\publish\Pilot\ActionLists</PublishUrl>
    <WebPublishMethod>FileSystem</WebPublishMethod>
    <SiteUrlToLaunchAfterPublish />
    <TargetFramework>net8.0</TargetFramework>
    <ProjectGuid>2ca5a744-a80e-4a06-8086-e7775366186c</ProjectGuid>
    <SelfContained>false</SelfContained>
  </PropertyGroup>
  <ItemGroup>
    <Content Update="appsettings.*.json" CopyToPublishDirectory="Never" />
    <Content Update="nlog.*.config" CopyToPublishDirectory="Never" />
    <ResolvedFileToPublish Include="appsettings.Pilot.json">
      <RelativePath>appsettings.Pilot.json</RelativePath>
    </ResolvedFileToPublish>
    <ResolvedFileToPublish Include="nlog.pilot.config">
      <RelativePath>nlog.pilot.config</RelativePath>
    </ResolvedFileToPublish>
  </ItemGroup>
</Project>
