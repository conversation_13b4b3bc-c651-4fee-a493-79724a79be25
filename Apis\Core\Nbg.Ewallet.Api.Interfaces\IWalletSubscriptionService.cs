using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IWalletSubscriptionService
{
    Task<SubscriptionResponse> ExtendSubscription(CreateSubscriptionRequest request);
    Task<SubscriptionResponse> OptOutSubscription();
    Task<SubscriptionResponse> GetWalletSubscription(Guid walletid);
    Task<SubscriptionUsage> GetSubscriptionUsage(Guid walletid);
}
