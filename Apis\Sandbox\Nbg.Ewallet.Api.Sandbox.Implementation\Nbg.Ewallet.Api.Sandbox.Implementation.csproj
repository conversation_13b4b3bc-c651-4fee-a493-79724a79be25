﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>true</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Controllers" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Interfaces" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Types" Version="6.0.2" />
        <PackageReference Include="Nbg.OpenBanking.ConsentConnector" Version="4.0.1" />
        <!--<PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />-->
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\Core\Nbg.Ewallet.Api.Implementation\Nbg.Ewallet.Api.Implementation.csproj" />
        <ProjectReference Include="..\..\Core\Nbg.Ewallet.Api.Interfaces\Nbg.Ewallet.Api.Interfaces.csproj" />
        <ProjectReference Include="..\..\Core\Nbg.Ewallet.Repository\Nbg.Ewallet.Repository.csproj" />
        <ProjectReference Include="..\Nbg.Ewallet.Api.Sandbox.Types\Nbg.Ewallet.Api.Sandbox.Types.csproj" />
    </ItemGroup>

</Project>