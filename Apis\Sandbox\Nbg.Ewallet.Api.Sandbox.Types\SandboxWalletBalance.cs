﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// Wallet Load Request
/// </summary>
public class SandboxWalletBalance
{
    /// <summary>
    /// wallet Id
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// amount
    /// </summary>
    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    /// <summary>
    /// currency
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>
    /// currency
    /// </summary>
    [DataMember(Name = "iban")]
    public string Iban { get; set; }
}
