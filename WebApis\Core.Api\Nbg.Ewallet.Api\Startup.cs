﻿using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using nbg.ewallet.repository;
using nbg.netcore.consent.implementation;
using Nbg.AspNetCore.Http.Extensions;
using Nbg.Ewallet.Api.Controllers;
using Nbg.Ewallet.Api.Implementation;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Types.TimeConverters;
using Nbg.Ewallet.Repository;
using Nbg.Ewallet.Repository.HttpClients;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.TypeHandlers;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.Ewallet.Repository.UnitOfWork;
using Nbg.Ewallet.Repository.Utilities;
using Nbg.NetCore.CosmosConnector.Implementation;
using Nbg.NetCore.Data;
using Nbg.NetCore.Healthchecks.Checks;
using Nbg.NetCore.Healthchecks.Reports;
using Nbg.NetCore.HttpExceptions;
using Nbg.NetCore.Services.Cics.Http;

namespace Nbg.Ewallet.Api;

public class Startup
{
    private readonly IConfiguration _configuration;

    public Startup(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        RegisterHttpClients(services);
        RegisterApiServices(services);
        RegisterConfigurations(services);
        RegisterControllers(services);
    }

        private void RegisterHttpClients(IServiceCollection services)
        {
            services.AddHttpClient("bigDataStetementsAzureApi", "HttpClient:bigDataStetementsAzureApi");
            services.AddHttpClient("corporateApi", "HttpClient:corporateApi");
            services.AddHttpClient("accountsApi", "HttpClient:accountsApi");
            services.AddHttpClient("tppApi", "HttpClient:tppApi");
            services.AddHttpClient("subscriptionApi", "HttpClient:subscriptionApi");
        }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        _configuration.ConfigureServicePointManager();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseDefaultFiles();
        app.UseStaticFiles();
        app.UseRouting();

        // added by JerryC for logging
        app.UseEWalletExceptionHandling();
        app.UseEWalletAuthorization();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();

            // register health check endpoint
            endpoints.MapHealthChecks("/health", new HealthCheckOptions { ResponseWriter = HealthCheckReports.WriteJson, AllowCachingResponses = false });

        });
    }

    private void RegisterApiServices(IServiceCollection services)
    {
        var healthChecker = services
            .AddHealthChecks()
            .AddDbConnectionCheck()
            .AddAllocatedMemoryCheck();

        //For accessing user claims
        services.AddHttpContextAccessor();
        services.AddDbConnectorFactory();
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IUnitOfWorkFactory, UnitOfWorkFactory>();
        services.Configure<UnitOfWorkOptions>(cfg => cfg.DisposeBehavior = DisposeBehavior.Commit);

        if (_configuration.GetSection("RepositorySettings").GetValue("UseAceConnector", false))
        {
            services.AddAceConnector(_configuration, "AceConnectorOptions");
            services.AddScoped<IMainFrameConnector, CosmosConnector>();
            services.AddScoped<IAuditableAceConnector, AuditableAceConnector>();
            //todo implement health check for cosmos
            //healthChecker.AddCosmosConnectionCheck();
            healthChecker.AddCheck<DummyCosmosHealthCheck>("dummy_cosmos_health_check");
        }
        else
        {
            services.AddCicsHttpConnector("CicsHttpConnector");
            services.AddScoped<IMainFrameConnector, CicsConnector>();
            services.AddScoped<IAuditableCicsConnector, AuditableCicsConnector>();
            healthChecker.AddCicsConnectionCheck();
        }

        //For accessing consent management db
        services.AddConsentsCore();
        //For communicating with BAE system
        //services.AddAccountingEngineFeed();

        //Mail Service
        // services.AddTransient<IResourceResolver, ResourceResolver>();
        //services.AddEmailService();


        //we have to ook at all these transients - technical debt
        services.AddScoped<IConsentService, ConsentService>();
        services.AddScoped<IManagementService, ManagementService>();
        services.AddScoped<IValidationService, ValidationService>();

        services.AddScoped<IConsentsRepositoryService, ConsentsRepositoryService>();
        services.AddScoped<IWalletRepositoryService, WalletRepositoryService>();
        services.AddScoped<IHttpContextRepositoryService, HttpContextRepositoryService>();
        services.AddScoped<IUserPermissionsRepositoryService, UserPermissionRepositoryService>();
        services.AddScoped<IWalletPermissionsRepositoryService, WalletPermissionsRepositoryService>();
        services.AddScoped<ILimitRepositoryService, LimitRepositoryService>();
        services.AddScoped<ITransactionRepositoryService, TransactionRepositoryService>();

        services.AddScoped<IBigDataAzureClientService, BigDataAzureClientService>();
        services.AddScoped<ICorporateApiClientService, CorporateApiClientService>();
        services.AddScoped<IAccountsApiClientService, AccountsApiClientService>();
        services.AddScoped<ITPPApiClientService, TPPApiClientService>();
        services.AddScoped<ICreateAccountService, CreateAccountService>();
        services.AddScoped<ISubscriptionApiClientService, SubscriptionApiClientService>();

            services.AddScoped<ISubscriptionPlansService, SubscriptionPlansService>();

        services.AddScoped<ITransactionService, TransactionService>();
        services.AddScoped<IPaymentsRequestProvider, PaymentsRequestProvider>();
        services.AddScoped<ITransferRequestProvider, TransferRequestProvider>();
        services.AddScoped<IBatchTransferRequestProvider, BatchTransferRequestProvider>();
        services.AddScoped<IBatchPaymentsRequestProvider, BatchPaymentsRequestProvider>();
        services.AddScoped<IAuthorizationRepositoryService, AuthorizationRepositoryService>();
        services.AddScoped<ISubscriptionUsageCalculatorService, SubscriptionUsageCalculatorService>();
        services.AddScoped<ILimitCalculatorService, LimitCalculatorService>();
        services.AddScoped<ICustomerCommissionService, CustomerCommissionService>();

        services.AddScoped<IAdminService, AdminService>();
        services.AddScoped<IWalletService, WalletService>();
        services.AddScoped<IWalletRegistrationService, WalletRegistrationService>();
        services.AddScoped<IWalletSubscriptionService, WalletSubscriptionService>();
        services.AddScoped<IWalletPermissionsService, WalletPermissionsService>();
        services.AddScoped<IWalletUserPermissionsService, WalletUserPermissionsService>();
        services.AddScoped<IWalletAuthorizationRequestsService, WalletAuthorizationRequestsService>();
        services.AddScoped<IProfileService, ProfileService>();
        services.AddScoped<ITransfersService, TransfersService>();
        services.AddScoped<IPaymentsService, PaymentsService>();
        services.AddScoped<IAuthContextService, AuthContextService>();
        services.AddScoped<IHostCommonConfigurationService, HostCommonConfigurationService>();
        services.AddScoped<IServiceAuditRepositoryService, ServiceAuditRepositoryService>();

        services.AddMemoryCache();
        services.AddScoped<IIbanAccountUtility, IbanAccountUtility>();

        services.AddAutoMapper(typeof(ImplementationMappingProfile).Assembly);
        // remove this forever.....
        //services.AddAutoMapper(typeof(RepoMappingProfile).Assembly);

        SqlMapperRegistrator.RegisterTypeHandlers();
    }

    private void RegisterControllers(IServiceCollection services)
    {
        services
            .AddControllers(SetMvcOptions)
            .AddJsonOptions(SetJsonOptions)
            //.AddNewtonsoftJson(SetMvcNewtonsoftOptions)
            .ConfigureApiBehaviorOptions(SetApiBehaviorOptions)
            .AddApplicationPart(typeof(AdminController).Assembly);

        services.Configure<ApiBehaviorOptions>(options =>
        {
            options.InvalidModelStateResponseFactory = actionContext =>
            {
                var errors = string.Join(", ", actionContext.ModelState.Values
                    .SelectMany(x => x.Errors)
                    .Select(e => e.ErrorMessage));

                return new BadRequestObjectResult(errors);
            };
        });
    }

    private void RegisterConfigurations(IServiceCollection services)
    {
        services.Configure<RepositorySettings>(_configuration.GetSection(nameof(RepositorySettings)));
        services.Configure<UrlSettings>(_configuration.GetSection(nameof(UrlSettings)));
        services.Configure<ValidationsControlPolicy>(_configuration.GetSection(nameof(ValidationsControlPolicy)));
        services.Configure<BigDataAzureClientSettings>(_configuration.GetSection("BigDataAzureClientSettings"));
        services.Configure<CorporateApiClientSettings>(_configuration.GetSection("CorporateApiClientSettings"));
        services.Configure<AccountsApiClientSettings>(_configuration.GetSection("AccountsApiClientSettings"));
        services.Configure<AccountsCoreClientSettings>(_configuration.GetSection("AccountsCoreClientSettings"));
        services.Configure<TPPApiClientSettings>(_configuration.GetSection("TPPApiClientSettings"));
        services.Configure<HostCommonConfigurationSettings>(_configuration.GetSection("HostCommonConfigurationSettings"));
        services.Configure<ServiceAuditSettings>(_configuration.GetSection("ServiceAuditSettings"));
        services.Configure<CreateAccountSettings>(_configuration.GetSection("CreateAccountSettings"));
        services.Configure<SubscriptionApiClientSettings>(_configuration.GetSection("SubscriptionApiClientSettings"));
    }

    private void SetMvcOptions(MvcOptions options)
    {
        options.ReturnHttpNotAcceptable = true;
    }

    private void SetJsonOptions(JsonOptions options)
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
        options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase, allowIntegerValues: false));
        options.JsonSerializerOptions.Converters.Add(new SystemTextJsonDateTimeConverter()); // Custom if needed
    }

    //private void SetMvcNewtonsoftOptions(MvcNewtonsoftJsonOptions options)
    //{
    //    options.SerializerSettings.Converters = [new StringEnumConverter { AllowIntegerValues = false }, new UtcDateTimeConverter()];
    //    options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
    //}

    private void SetApiBehaviorOptions(ApiBehaviorOptions options)
    {
        options.InvalidModelStateResponseFactory = context => throw new BadRequestException("Invalid Model State");
    }
}
