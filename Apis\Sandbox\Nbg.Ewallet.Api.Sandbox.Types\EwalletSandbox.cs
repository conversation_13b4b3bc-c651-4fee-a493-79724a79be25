﻿using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Sandbox.Types;

public sealed class EwalletSandbox
{
    public List<SandboxStatement> Statements { get; set; }
    public Dictionary<Guid, List<SandboxAvailableUser>> AvailableUsers { get; set; }
    public List<SandboxUserPermission> UserPermissions { get; set; }
    public List<SandboxWalletPermission> WalletsPermissions { get; set; } //waletid , Permissions granted to other wallets
    public List<SandboxSubscription> WalletsSubscriptions { get; set; } //waletid , subscription

    public List<SandboxWallet> Wallets { get; set; }

    //public List<SandboxWalletBalance> WalletsBalance { get; set; } // walletid, wallet balance

    public SandboxTerms Terms { get; set; }

    public List<SandboxTransaction> Transactions { get; set; }

    public List<WalletAuthorizationResponse> WalletAuthorizationRequests { get; set; }

    public List<SandboxLimit> Limits { get; set; }
    public List<SandboxAvailableAccount> AvailableAccounts { get; set; } // available accounts with balance and iban
}
