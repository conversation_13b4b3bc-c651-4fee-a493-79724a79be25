﻿using System;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Implementation.Extensions;
public static class EnumExtensions
{
    public static TransactionSubType ToTransactionSubType(this SubscriptionTransactionType transactionType)
    {
        return transactionType switch
        {
            SubscriptionTransactionType.Payment => TransactionSubType.GenericPayment,
            SubscriptionTransactionType.Transfer or _ => TransactionSubType.WalletToIBAN,
        };
    }

    public static SubscriptionTransactionType ToSubscriptionTransactionType(this TransactionType transactionType)
    {
        return transactionType switch
        {
            TransactionType.Payment => SubscriptionTransactionType.Payment,
            TransactionType.Transfer or _ => SubscriptionTransactionType.Transfer,
        };
    }

    public static TransactionType ToTransactionType(this TransactionSubType transactionSubType)
    {
        return transactionSubType switch
        {
            TransactionSubType.GenericPayment => TransactionType.Payment,
            TransactionSubType.WalletToNBG or TransactionSubType.WalletToWallet or TransactionSubType.WalletToIBAN or _ => TransactionType.Transfer,
        };
    }

    public static SubscriptionTransactionType ToSubscriptionTransactionType(this TransactionSubType transactionSubType)
    {
        return transactionSubType switch
        {
            TransactionSubType.GenericPayment => SubscriptionTransactionType.Payment,
            TransactionSubType.WalletToNBG or TransactionSubType.WalletToWallet or TransactionSubType.WalletToIBAN or _ => SubscriptionTransactionType.Transfer,
        };
    }

    public static LimitTransactionType ToLimitTransactionType(this TransactionSubType transactionSubType)
    {
        return transactionSubType switch
        {
            TransactionSubType.GenericPayment => LimitTransactionType.Payment,
            TransactionSubType.WalletToNBG => LimitTransactionType.WalletToNBG,
            TransactionSubType.WalletToWallet => LimitTransactionType.WalletToWallet,
            TransactionSubType.WalletToIBAN => LimitTransactionType.WalletToIBAN,
            _ => throw new NotImplementedException(),
        };
    }

    public static TransferType ToTransferType(this TransactionSubType transactionSubType)
    {
        return transactionSubType switch
        {
            TransactionSubType.GenericPayment => TransferType.UNKNOWN,
            TransactionSubType.WalletToNBG => TransferType.NBG,
            TransactionSubType.WalletToWallet => TransferType.NBG,
            TransactionSubType.WalletToIBAN => TransferType.OTHER,
            _ => TransferType.UNKNOWN,
        };
    }

    public static Indication ToIndication(this SubscriptionTier subscriptionPlanType)
    {
        return subscriptionPlanType switch
        {
            SubscriptionTier.Free => Indications.Trial,
            SubscriptionTier.Basic => Indications.Basic,
            SubscriptionTier.Premium => Indications.Premium,
            _ => null,
        };
    }
}
