using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// This Controller Provides Wallet Permissions functionality.
/// </summary>
[ApiController]
[ApiExplorerSettings(GroupName = "Wallet")]
[Route("wallet")]
[Produces("application/json")]
[Consumes("application/json")]
public class WalletPermissionsController : ControllerBase
{
    private readonly IWalletPermissionsService _walletPermissionsService;

    public WalletPermissionsController(IWalletPermissionsService walletPermissionsService)
    {
        _walletPermissionsService = walletPermissionsService;
    }

    /// <summary>
    /// Grants access to other wallets.
    /// </summary>
    /// <param name="request">The request containing permissions details for other wallets.</param>
    /// <returns>A response indicating the result of the operation.</returns>
    /// <response code="200">Permissions granted successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(WalletPermissionResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPost]
    [Route("{walletId}/wallet-permissions", Name = "SetWalletPermissionsForOtherWallets")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<WalletPermissionResponse>> SetWalletPermissionsForOtherWallets(WalletPermissionsRequest request)
    {
        return await _walletPermissionsService.SetWalletPermissionsForOtherWallets(request);
    }

    /// <summary>
    /// Retrieves permissions for other wallets.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="showAll">Whether to show all permissions.</param>
    /// <returns>A list of permissions for other wallets.</returns>
    /// <response code="200">Permissions retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<WalletPermission>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/wallet-permissions", Name = "GetWalletPermissionsForOtherWallets")]
    public async Task<ActionResult<List<WalletPermission>>> GetWalletPermissionsForOtherWallets(Guid walletId, [FromQuery] bool showAll)
    {
        return await _walletPermissionsService.GetWalletPermissionsForOtherWallets(walletId, showAll);
    }

    /// <summary>
    /// Retrieves permissions for a specific external wallet.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="externalWalletId">The external wallet identifier.</param>
    /// <param name="showAll">Whether to show all permissions.</param>
    /// <returns>A list of permissions for the specified external wallet.</returns>
    /// <response code="200">Permissions retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<WalletPermission>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/wallet-permissions/{externalWalletId}", Name = "GetWalletPermissionsForOtherWallet")]
    public async Task<ActionResult<List<WalletPermission>>> GetWalletPermissionsForOtherWallet(Guid walletId, Guid externalWalletId, [FromQuery] bool showAll)
    {
        return await _walletPermissionsService.GetWalletPermissionsForExternalWallet(walletId, externalWalletId, showAll);
    }

    /// <summary>
    /// Revokes permissions for a specific external wallet.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="targetWalletId">The target wallet identifier whose permissions are to be revoked.</param>
    /// <returns>A list of permissions after revocation.</returns>
    /// <response code="200">Permissions revoked successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<WalletPermission>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPut]
    [Route("{walletId}/wallet-permissions/{targetWalletId}/revoke", Name = "RevokeExternalWalletPermissions")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<WalletPermission>> RevokeExternalWalletPermissions(Guid walletId, Guid targetWalletId)
    {
        var response = await _walletPermissionsService.RevokeExternalWalletPermissions(walletId, targetWalletId);
        return response;
    }
}
