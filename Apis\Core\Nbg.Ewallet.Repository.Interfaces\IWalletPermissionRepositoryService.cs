﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface IWalletPermissionsRepositoryService
{
    Task<List<RepoWalletPermission>> FindAllByWalletIdAsync(Guid myWalletId);
    Task<List<RepoWalletPermission>> FindAllByWalletIdAndTargetWalletIdAsync(Guid walletId, Guid targetWalletId);
    Task ExpireExistingPermissionsyWalletIdAsync(Guid myWalletId, IEnumerable<RepoWalletPermission> permissions);
    Task SaveAllAsync(List<RepoWalletPermission> permission);
    Task<List<RepoWalletPermission>> FindAllActiveByWalletIdAndTargetWalletIdAsync(Guid walletId, Guid targetWalletId);
    Task<RepoWalletPermission> SaveAsync(RepoWalletPermission walletPermission);
    Task<List<RepoWalletPermission>> FindAllByExpiredAndWalletIdAndExternalWalletId(Guid walletId, Guid externalWalletId);
    Task<List<RepoWalletPermission>> FindAllActiveByWalletIdAndExternalWalletIdAsync(Guid walletId, Guid externalWalletId);
    //Task<List<RepoLimit>> GetLimitsForPermissionIdAsync(Guid permissionId);
}
