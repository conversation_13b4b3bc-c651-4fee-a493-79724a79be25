﻿using System;
using System.Linq;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Implementation.Extensions;

public static class AvailableAccountsResponseExtension
{
    /// <summary>
    /// Converts an <see cref="AccountsFullResponse"/> instance into an <see cref="AvailableAccountsResponse"/> instance.
    /// </summary>
    /// <param name="accountsFull">The source <see cref="AccountsFullResponse"/> containing account details to be converted. Cannot be <see
    /// langword="null"/>.</param>
    /// <returns>An <see cref="AvailableAccountsResponse"/> containing the converted account details. Each account in the source is
    /// mapped to an <see cref="AvailableAccount"/> with its IBAN, alias, available balance, and ledger balance.</returns>
    public static AvailableAccountsResponse ToAvailableAccountsResponse(this AccountsFullResponse accountsFull)
    {
        ArgumentNullException.ThrowIfNull(accountsFull, nameof(accountsFull));
        var result = new AvailableAccountsResponse
        {
            Accounts = [.. accountsFull.Accounts.Select(static account => new AvailableAccount
            {
                IBAN = account.IBAN,
                Alias = account.Alias,
                AvailableBalance = account.AvailableBalance,
                LedgerBalance = account.LedgerBalance,
                Currency = account.Currency
            })]
        };

        return result;
    }
}
