﻿using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;

namespace Nbg.Ewallet.Api.Implementation;
public class AuthContextService : IAuthContextService
{
    private AuthorizationContext _context;

    public AuthContextService()
    {
    }

    public void SetContext(AuthorizationContext authorizationContext)
    {
        _context = authorizationContext;
    }

    public AuthorizationContext GetContext()
    {
        return _context;
    }
}
