﻿using System.Net;

namespace IntegrationTests.Helpers;

public class CallbackListener : IDisposable
{
    private readonly HttpListener _listener;
    private readonly Action<HttpListenerContext> _handler;

    public static CallbackListener Create(string url, Action<HttpListenerContext> handler)
    {
        var server = new CallbackListener(new HttpListener { Prefixes = { url/*, "http://localhost:52695/"*/ } }, handler);
        server.Start();
        return server;
    }

    private CallbackListener(HttpListener listener, Action<HttpListenerContext> handler)
    {
        _listener = listener;
        _handler = handler;
    }

    private void Start()
    {
        if (_listener.IsListening) return;
        _listener.Start();
    }

    public void AddTask()
    {
        Task.Factory.StartNew(() =>
        {
            var context = _listener.GetContext();
            _handler(context);
        });
    }

    public void Dispose()
    {
        _listener.Stop();
        _listener.Close();
    }
}
