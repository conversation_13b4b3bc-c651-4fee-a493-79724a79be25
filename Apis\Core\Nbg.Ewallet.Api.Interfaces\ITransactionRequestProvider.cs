﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Records;

namespace Nbg.Ewallet.Api.Interfaces;

public interface ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse>
{
    Task<List<RepoTransaction>> InitiateRepoTransactions(TRequest request, RepoWallet wallet, string userId);
    Task<ValidateRequestResult> ValidateRequest(TRequest request, RepoWallet wallet);
    TResponse EnrichResponse(TRequest request, List<RepoTransaction> transactions);
    Task<TResponse> HandleTransactionExecutionsAsync(TRequest request, List<RepoTransaction> transactions, RepoWallet wallet);
    Task<TResponse> TransactionForecastExecutionsAsync(TRequest request, RepoWallet wallet);
    Task<TCommissionResponse> GetCommissionAsync(TRequest request, Guid walletId);
    TotalAmountWithCommission GetTotalAmountWithCommission(TRequest request, TCommissionResponse commissionResponse);
    void SetNewDebitAccount(TRequest request, string ibanAccount);
}
