﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.CorporateApi;

[DataContract]
public class CompanyUserGenericRequest
{
    public CompanyUserGenericRequest() { }

    public CompanyUserGenericRequest(string userId)
    {
        UserID = userId;
        SubjectUserId = userId;
    }
    /// <summary>
    /// Editor User Id
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserID { get; set; }
    /// <summary>
    /// Editor Auth Level
    /// </summary>
    [DataMember(Name = "authorizationLevel")]
    public string AuthorizationLevel { get; set; }
    /// <summary>
    /// Subject User Id
    /// </summary>
    [DataMember(Name = "subjectUserId")]
    public string SubjectUserId { get; set; }
    /// <summary>
    /// MyNbg Editor User Details
    /// Null for S user
    /// </summary>
    [DataMember(Name = "authDetails")]
    public NbgEmployeeAuthDetails AuthDetails { get; set; }
}

/// <summary>
/// Λεπτομέρειες για τη σύνδεση από κατάσταημα
/// </summary>
[DataContract]
public class NbgEmployeeAuthDetails
{
    /// <summary>
    /// true για σύνδεση από κατάστημα
    /// Το userId συμπληρώνεται στο request.userId
    /// </summary>
    [DataMember(Name = "nbgEmployee")]
    public bool? NbgEmployee { get; set; } = true;
    /// <summary>
    /// Workstation υπαλλήλου
    /// </summary>
    [DataMember(Name = "workstation")]
    public string Workstation { get; set; } = "7004";
    /// <summary>
    /// κατάστημα υπαλλήλου
    /// </summary>
    [DataMember(Name = "branch")]
    public string Branch { get; set; } = "040";
    /// <summary>
    /// Τερματικό υπαλλήλου
    /// </summary>
    [DataMember(Name = "aterm")]
    public string Aterm { get; set; } = "000";
    /// <summary>
    /// userId υπαλλήλου
    /// </summary>
    [DataMember(Name = "approver")]
    public string Approver { get; set; } = "E99999";

}
