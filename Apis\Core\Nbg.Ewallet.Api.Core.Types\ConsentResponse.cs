﻿using Nbg.Ewallet.Repository.Types;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Consent response
/// </summary>
[DataContract]
public class ConsentResponse
{
    /// <summary>
    /// Consent id
    /// </summary>
    [DataMember(Name = "consentId")]
    public string ConsentId { get; set; }

    /// <summary>
    /// Sandbox id
    /// </summary>
    [DataMember(Name = "sandboxId")]
    public string SandboxId { get; set; }

    /// <summary>
    /// Already selected account
    /// </summary>
    [DataMember(Name = "defaultIban")]
    public string DefaultIban { get; set; }

    /// <summary>
    /// Wallet Name
    /// </summary>
    [DataMember(Name = "walletName")]
    public string WalletName { get; set; }

    /// <summary>
    /// Id of the discount applied
    /// </summary>
    [DataMember(Name = "planid")]
    public SubscriptionTier PlanId { get; set; }
}
