﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Payments;

/// <summary>
///
/// </summary>
public class BatchCommissionResponse
{
    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "commissions")]
    public List<CommissionResponseBatch> Commissions { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "commissionSum")]
    public CommissionResponseBatch CommissionSum { get; set; }
}
