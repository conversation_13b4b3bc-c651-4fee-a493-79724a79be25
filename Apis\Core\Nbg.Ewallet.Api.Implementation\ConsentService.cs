﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Options;
using nbg.netcore.consent.repository.types;
using nbg.netcore.consent.types;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.OpenBanking.Utilities;
using CreateConsentRequest = Nbg.Ewallet.Api.Types.Wallet.CreateConsentRequest;


namespace Nbg.Ewallet.Api.Implementation;

public class ConsentService : IConsentService
{
    private readonly IHttpContextRepositoryService _httpContextService;
    private readonly IMapper _mapper;
    private readonly IConsentsRepositoryService _consentsRepositoryService;
    private readonly UrlSettings _urlSettings;

    public ConsentService(
        IHttpContextRepositoryService httpContextService,
        IMapper mapper,
        IConsentsRepositoryService consentsRepositoryService,
        IOptions<UrlSettings> urlOptions)
    {
        _httpContextService = httpContextService;
        _mapper = mapper;
        _consentsRepositoryService = consentsRepositoryService;
        _urlSettings = urlOptions.Value;
    }

    public async Task<CreateConsentResponse> CreateConsent(CreateConsentRequest request)
    {
        var extraData = new EWalletConsentExtraData
        {
            Type = nameof(EWalletConsent),
            SandboxId = _httpContextService.GetSandboxId()
        };

            var consenData = new EWalletConsentData
            {
                DefaultIban = string.Empty,
                WalletName = request.WalletName,
                PlanId = request.PlanId,
            };

        var consent = new EWalletConsent
        {
            ClientId = _httpContextService.GetClientId().ToString(),
            StartDate = DateTime.UtcNow,
            EndDate = null,
            Status = ConsentStatus.AwaitingAuthorization,
            ConsentData = consenData.GetSerializedObject(),
            ExtraData = extraData.GetSerializedObject()
        };

        var repoConsent = _mapper.Map<RepoConsent>(consent);
        var repoConsentFull = await _consentsRepositoryService.CreateConsentAsync(repoConsent);
        var generatedConsent = _mapper.Map<EWalletConsent>(repoConsentFull);

        var consentId = generatedConsent.Id.ToString();
        generatedConsent.Data = generatedConsent.ConsentData.GetDeserializedObject<EWalletConsentData>();

        return new CreateConsentResponse
        {
            ConsentId = consentId,
            Status = generatedConsent.Status.ToString(),
            AuthorizationUrl = GetAuthorizationUrl(consentId)
        };
    }

    public async Task<CreateConsentResponse> GetConsent(Guid ConsentId)
    {
        var repoConsentFull = await _consentsRepositoryService.GetConsentAsync(ConsentId.ToString());
        var consent = _mapper.Map<EWalletConsent>(repoConsentFull);
        consent.Data = consent.ConsentData.GetDeserializedObject<EWalletConsentData>();

        return new CreateConsentResponse
        {
            ConsentId = ConsentId.ToString(),
            Status = consent.Status.ToString()
        };
    }

    private string GetAuthorizationUrl(string consentId)
    {
        var callbackUrl = _httpContextService.GetCallbackUrl() ?? "{{redirect_uri}}";
        var clientId = _httpContextService.GetClientId().ToString();

        var scaRedirectUrl = _urlSettings.AuthorizationUrl
            .Replace("{{consent_id}}", consentId)
            .Replace("{{client_id}}", clientId)
            .Replace("{{redirect_uri}}", callbackUrl);

        return scaRedirectUrl;
    }
}
