using Microsoft.Extensions.DependencyInjection;

namespace Nbg.Ewallet.Api.Controllers;

public static class Extensions
{
    public static void AddAppControllers(this IMvcBuilder builder)
    {
        builder
            .AddApplicationPart(typeof(AdminController).Assembly)
            .AddApplicationPart(typeof(B2BController).Assembly)
            .AddApplicationPart(typeof(WalletServiceController).Assembly)
            .AddApplicationPart(typeof(PaymentsController).Assembly)
            .AddApplicationPart(typeof(ConsentsController).Assembly)
            .AddApplicationPart(typeof(TransfersController).Assembly)
            .AddApplicationPart(typeof(TermsController).Assembly)
            ;
    }
}
