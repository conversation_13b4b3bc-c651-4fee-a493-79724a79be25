﻿using System;

namespace Nbg.Ewallet.Repository.Types;

public class RepoWallet
{
    public Guid WalletId { get; set; }

    public string WalletAccount { get; set; }

    public DateTime? WalletAccountCreatedAt { get; set; }

    public string OrganizationName { get; set; }

    public DateTime RegistrationDate { get; set; }

    public string VatNumber { get; set; }

    public string OwnerUserId { get; set; }

    public string WalletName { get; set; }

    public string OwnerCustomerCode { get; set; }

    public bool IsCorporateUser { get; set; }

    public Guid TenantId { get; set; }
    public Guid ActiveSubscriptionId { get; set; }
}
