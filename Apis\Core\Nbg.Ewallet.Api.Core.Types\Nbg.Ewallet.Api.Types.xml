<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Nbg.Ewallet.Api.Types</name>
    </assembly>
    <members>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.Account">
            <summary>
            User account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Account.Iban">
            <summary>
            Account IBAN
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Account.Currency">
            <summary>
            Account currency ISO code
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Account.Description">
            <summary>
            Account description
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.AccountsRequest">
            <summary>
            Accounts request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AccountsRequest.UserID">
            <summary>
            User id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AccountsRequest.NetworkId">
            <summary>
            Network id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.AccountsResponse">
            <summary>
            Accounts response
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.ConsentResponse">
            <summary>
            Consent response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.ConsentResponse.ConsentId">
            <summary>
            Consent id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.ConsentResponse.SandboxId">
            <summary>
            Sandbox id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.ConsentResponse.DefaultIban">
            <summary>
            Already selected account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.ConsentResponse.WalletName">
            <summary>
            Wallet Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.ConsentResponse.PlanId">
            <summary>
            Id of the discount applied
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.CreateConsentRequest">
            <summary>
            Consent response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.CreateConsentRequest.WalletName">
            <summary>
            Wallet Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.CreateConsentRequest.PlanId">
            <summary>
            Id of the discount applied
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.CreateConsentResponse">
            <summary>
            Consent response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.CreateConsentResponse.ConsentId">
            <summary>
            Consent id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.CreateConsentResponse.Status">
            <summary>
            Consent status
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.CreateConsentResponse.AuthorizationUrl">
            <summary>
            Consent authorization url
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.EWalletConsent">
            <summary>
            Consent object
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.EWalletConsentData">
            <summary>
            Consent data object
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.EWalletConsentData.DefaultIban">
            <summary>
            Wallet Default IBAN
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.EWalletConsentData.WalletName">
            <summary>
            Wallet Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.EWalletConsentData.PlanId">
            <summary>
            Id of the discount applied
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.RetrieveConsentRequest">
            <summary>
            Consent information request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.RetrieveConsentRequest.UserID">
            <summary>
            User id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.RetrieveConsentRequest.ConsentId">
            <summary>
            Consent id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest">
            <summary>
            Authorize / Reject consent object
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest.UserID">
            <summary>
            User id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest.ConsentId">
            <summary>
            Consent id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest.Iban">
            <summary>
            IBAN
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest.WalletName">
            <summary>
            WalletName
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest.TanNumber">
            <summary>
            TAN number
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.AvailableAccountsResponse">
            <summary>
            Represents a response containing a list of available accounts.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableAccountsResponse.Accounts">
            <summary>
            Collection of available accounts.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.AvailableAccount">
            <summary>
            Represents an available account.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableAccount.IBAN">
            <summary>Account IBAN.</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableAccount.Alias">
            <summary>Friendly name</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableAccount.AvailableBalance">
            <summary>Available balance</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableAccount.LedgerBalance">
            <summary>Ledger balance</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableAccount.Currency">
            <summary>Currency code</summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.AvailableUserResponse">
            <summary>
            A list of the users connected to the IB corporate account.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableUserResponse.Users">
            <summary>
            List of ib users that belong to the specific company
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.AvailableUser">
            <summary>
            A connected corporate account user in IB.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableUser.Userid">
            <summary>
            User ID of the user.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.AvailableUser.Alias">
            <summary>
            Alias, set by the IB with the S user profile.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.EditWalletNameRequest.WalletName">
            <summary>
            The name of the wallet. It is set by the wallet creator during registration and it is not unqiue across wallets.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.GetWalletBalanceResponse">
            <summary>
            GetWalletBalanceResponse definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.GetWalletBalanceResponse.LedgerBalance">
            <summary>
            Wallet Account ledger balance
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.GetWalletBalanceResponse.AvailableBalance">
            <summary>
            Wallet Account available balance
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.Limit">
            <summary>
            Represents a limit set on wallet transactions.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Limit.Id">
            <summary>
            The unique identifier for the limit.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Limit.PermissionId">
            <summary>
            The identifier of the permission to which this limit is associated.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Limit.TransactionType">
            <summary>
            The type of transaction that the limit applies to.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Limit.Amount">
            <summary>
            The maximum amount allowed for the transaction type within the specified timeframe.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Limit.Timeframe">
            <summary>
            The period over which the limit applies.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.SetWalletUserPermissionsRequest">
            <summary>
            Represents a request to set user permissions for a specific wallet.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.SetWalletUserPermissionsRequest.WalletId">
            <summary>
            The wallet identifier for which permissions are being set.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.SetWalletUserPermissionsRequest.Permissions">
            <summary>
            The list of user permissions to set for the wallet.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.UserPermission">
            <summary>
            The User Permissions assigned to the User Id for the connected wallet.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.Id">
            <summary>
            The permission unique identifier.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.WalletId">
            <summary>
            The Wallet Id of the wallet that this permission applies to.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.UserId">
            <summary>
            The IB User ID of the user that the permission is assigned to.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.Submit">
            <summary>
            This permissions allows the user to submit a transaction for approval.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.Approve">
            <summary>
            This permission allows the user to execute a tranasction without approval,
            or approve a transaction that is pending approval.
            NO, LIMIT, NO_CONSTRAINT
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.BalanceView">
            <summary>
            This permission allows the user to view the remaining balance of the wallet account.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.TransactionView">
            <summary>
            This permission allows the user to view the transactions or statements of the wallet account.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.InheritsAuthorizations">
            <summary>
            This permission allows the corporate user to inherit the authorizations provided to the wallet they belong to.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.Admin">
            <summary>
            Administration permission assigned only to the wallet creator/owner.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.Limits">
            <summary>
            Limits per transaction type that apply to the Submit, or Approve permission.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.CreationDate">
            <summary>
            Creation datetime of the permission. It is also the assignment date.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermission.ExpirationDate">
            <summary>
            Datetime of the expiration of the permissions.
            It is also the revocation datetime, if permissions are explicitly revoked by the admin.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.UserPermissionResponse">
            <summary>
            User Permssions Request Wrapper
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.UserPermissionResponse.Permissions">
            <summary>
            List of User Permissions
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.Wallet">
            <summary>
            Represents a wallet in the eWallet system.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.WalletId">
            <summary>
            The unique identifier for the wallet.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.WalletAccount">
            <summary>
            The IBAN of the secial purpose wallet account.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.WalletAccountCreatedAt">
            <summary>
            The date and time when the wallet account was created.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.OrganizationName">
            <summary>
            The name of the company associated with the wallet as it appears in IB.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.ConnectedIban">
            <summary>
            The connected IBAN, reserved for future use and should be ignored for now.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.RegistrationDate">
            <summary>
            The date when the wallet was registered.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.VatNumber">
            <summary>
            The VAT number associated with the wallet.
            It can be and FP or NP VAT number.
            It is unique across all wallets, since only one wallet per business entity can exist.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.OwnerUserId">
            <summary>
            The user ID of the wallet owner.
            The owner of the wallet has always ADMIN rights.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.WalletName">
            <summary>
            The name of the wallet. It is set by the wallet creator during registration and it is not unqiue across wallets.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.OwnerCustomerCode">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.Wallet.IsCorporateUser">
             <summary>
            
             </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationRequest">
            <summary>
            Wallet Authorization Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationRequest.TargetWalletId">
            <summary>
            Id of the wallet requesting authorization for
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse">
            <summary>
            Wallet Statements Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse.CreatedAt">
            <summary>
            Datetime of request creation
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse.UpdatedAt">
            <summary>
            Datetime of request creation
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse.ExpiresAt">
            <summary>
            Expiration Datetime of the authorization
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse.Status">
            <summary>
            Status of the authorization request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse.RequestorWallet">
            <summary>
            Requestor Wallet
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationResponse.TargetWallet">
            <summary>
            Target Wallet
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationBase">
            <summary>
            Wallet Authorization
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationBase.WalletId">
            <summary>
            Wallet Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationBase.WalletAccount">
            <summary>
            Wallet account IBAN
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationBase.OrganizationName">
            <summary>
            Company’s Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationBase.VatNumber">
            <summary>
            vatNumber
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationBase.WalletName">
            <summary>
            Wallet Name - By default the company’s name
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.RequestorWalletAuthorization">
             <summary>
            
             </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.TargetWalletAuthorization">
             <summary>
            
             </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletLite">
            <summary>
            Represents a search wallet result. Includes basic information of the Wallet entity.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletLite.WalletId">
            <summary>
            The wallet Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletLite.WalletAccount">
            <summary>
            Wallet account IBAN
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletLite.OrganizationName">
            <summary>
            Company’s Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletLite.VatNumber">
            <summary>
            VAT number of company or individual connected with the wallet.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletLite.WalletName">
            <summary>
            Wallet Name - By default the company’s name
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletPermission">
            <summary>
            Represents the permissions granted to a wallet for operations by another wallet.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.Id">
            <summary>
            The unique identifier for the permission.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.WalletId">
            <summary>
            The identifier of the wallet to which these permissions apply.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.TargetWalletId">
            <summary>
            The identifier of the target wallet granted these permissions.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.Submit">
            <summary>
            Indicates if the permission to submit is granted.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.Approve">
            <summary>
            Type of approval granted.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.BalanceView">
            <summary>
            Indicates if the permission to view balances is granted.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.TransactionView">
            <summary>
            Indicates if the permission to view transactions is granted.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.Limits">
            <summary>
            A list of transaction limits associated with this permission.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.CreationDate">
            <summary>
            The date when the permissions were created.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermission.ExpirationDate">
            <summary>
            The date when the permissions expire.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletPermissionsRequest">
            <summary>
            Represents a request to grant permissions to other wallets.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermissionsRequest.Permissions">
            <summary>
            A list of permissions to be granted.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletPermissionResponse">
            <summary>
            Represents the response containing permissions of external wallets.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletPermissionResponse.Permissions">
            <summary>
            A list of permissions associated with the wallet.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Wallet.WalletRegister">
            <summary>
            Wallet Register Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Wallet.WalletRegister.WalletName">
            <summary>
            Wallet name to be assigned to the newly created wallet.
            Can be used to be searched by other wallet user.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ChannelId">
            <summary>
            Channel id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ChannelName">
            <summary>
            name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ChannelUrl">
            <summary>
            FirstName
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.B2BChannel.ProductId">
            <summary>
            LastName
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Admin.CommonSuccessResult`1">
            <summary>
            Status Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.CommonSuccessResult`1.Success">
            <summary>
            Request Status Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.CommonSuccessResult`1.Payload">
            <summary>
            Associated payload
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.Rule.RuleName">
            <summary>
            name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Admin.Rule.Owner">
            <summary>
            owner
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ConfigureB2bRulesRequest.Rules">
            <summary>
            name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ConfigureB2bRulesRequest.rulesType">
            <summary>
            owner
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.AuthorizationContext.ActivePermission">
            <summary>
            The Active permission depending on request path walletId.
            Can be <see langword="null"/>
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchRequest.vatNumber">
            <summary>
            Vat Number
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchRequest.name">
            <summary>
            Company Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchRequest.walletAccount">
            <summary>
            Wallet Account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchResponse.name">
            <summary>
            Company Name used inside wallet environment
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchResponse.vatNumber">
            <summary>
            Company vat number
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.B2BSearchResponse.walletAccount">
            <summary>
            Company’s wallet account
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.AuthorizeConsent.ConsentId">
            <summary>
            The Id of the consent
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.AuthorizeConsent.UserId">
            <summary>
            the Id of the User
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.AuthorizeConsent.Iban">
            <summary>
            The Iban specified by the user
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.AuthorizeConsent.TanNumber">
            <summary>
            The sms OTP number the user has keyed in
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Consents.Consent2">
            <summary>
            Consent response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.Consent2.consentId">
            <summary>
            Consent id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.Consent2.status">
            <summary>
            Consent Status
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Consents.Consent2.AuthorizationUrl">
            <summary>
            Consent authorization url
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.ProfileResponse">
            <summary>
            User Profile information.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ProfileResponse.UserId">
            <summary>
            The User Id used to log in to IB.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ProfileResponse.WalletId">
            <summary>
            The Wallet Id that this User Id is connected to.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ProfileResponse.Permissions">
            <summary>
            The User Permissions assigned to this User Id for the connected wallet.
            If no active permissions are found, null is returned.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.ProfileResponse.Authorizations">
            <summary>
            Access granted to other wallets along with the specific permissions and limits.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Payments.BatchCommissionResponse">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payments.BatchCommissionResponse.Commissions">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payments.BatchCommissionResponse.CommissionSum">
             <summary>
            
             </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Payments.ExtendedPaymentResponse">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payments.ExtendedPaymentResponse.RequiresApproval">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payments.ExtendedPaymentResponse.TransactionId">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payments.ExtendedPaymentResponse.ErrorReason">
            <summary>Error reason</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payments.CommissionResponseBatch.Error">
             <summary>
            
             </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.BatchPaymentsCommissionRequest">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.BatchPaymentsCommissionRequest.BatchId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.BatchPaymentsCommissionRequest.Batch">
            <summary>
            List of payment requests
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.BatchPaymentsRequest">
             <summary>
            
             </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.BatchPaymentsRequest.BatchId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.BatchPaymentsRequest.Batch">
            <summary>
            List of payment requests
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.ExtendedPaymentsRequest">
            <summary>
            Payments Pay Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.DebtorName">
            <summary>
            Debtor Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.DebtorIban">
            <summary>
            Debtor Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.DebtorTelephone">
            <summary>
            Debtor Telephone
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.Payments">
            <summary>
            List of Payments
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.GetMassPaymentResponse.MassTransactionId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.MassStatement.PaymentCode">
            <summary>
            Payment Code
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.MassStatement.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.MassStatement.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Payment">
            <summary>
            Debtor Telephone
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payment.PaymentCode">
            <summary>
            Payment Code
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payment.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Payment.Amount">
            <summary>
            Amount
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.PaymentsCreateRequest">
            <summary>
            Crrate Payment
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.DebtorName">
            <summary>
            Debtor Name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.DebtorIban">
            <summary>
            Debtor Iban
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.DebtorTelephone">
            <summary>
            Debtor Telephone
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsCreateRequest.Payments">
            <summary>
            List of Payments
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.PaymentsForecastResponse">
            <summary>
            Payments Forecast Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsForecastResponse.BatchId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.PaymentsResponse">
            <summary>
            Payments Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsResponse.BatchId">
            <summary>
            Mass Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsResponse.Payments">
            <summary>
            List of payments
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.PaymentsStatementsResponse.Statements">
            <summary>
            List of Statements
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.RequiresOtp">
            <summary>
            Sms otp filter required fields
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.RequiresOtp.TanNumber">
            <summary>
            The sms OTP number the user has keyed in
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.RequiresOtp.IsSmsOtp">
            <summary>
            The sms OTP flag
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.FreeTransaction">
            <summary>
            FreeTransaction definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.FreeTransaction.TransactionType">
            <summary>
            Type of transaction
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.FreeTransaction.Value">
            <summary>
            value
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.DiscountTransaction">
            <summary>
            DiscountTransaction definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.DiscountTransaction.TransactionType">
            <summary>
            Type of transaction
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.DiscountTransaction.Value">
            <summary>
            Value
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.DiscountType">
            <summary>
            Defined DiscountTypes
            </summary>
        </member>
        <member name="F:Nbg.Ewallet.Api.Types.Subscriptions.DiscountType.Percentage">
            <summary>
            Percentage
            </summary>
        </member>
        <member name="F:Nbg.Ewallet.Api.Types.Subscriptions.DiscountType.FixedPrice">
            <summary>
            FixedPrice
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionPlan">
            <summary>
            SubscriptionPlan definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionPlan.Id">
            <summary>
            Id of the SubscriptionPlan
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionPlan.discounts">
            <summary>
            Defined discounts per transaction type
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.CreateSubscriptionRequest">
            <summary>
            SubscriptionRequest definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.CreateSubscriptionRequest.Tier">
            <summary>
            Id of the discount applied
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.OptOutSubscriptionRequest">
            <summary>
            SubscriptionRequest definition
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse">
            <summary>
            CreateSubscriptionResponse definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.SubscriptionId">
            <summary>
            Subscription Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.StartDate">
            <summary>
            Start Date
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.EndDate">
            <summary>
            Expiration date of the subscription.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.Amount">
            <summary>
            Monetary amount paid by the End User to the partner for subscribing to the wallet service.
            This is only for consolidation reasons and it is not used in any other way.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.PaymentDue">
            <summary>
            Due Payment date of the subscription
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.DueAmount">
            <summary>
            Due Amount to be paid by the End User to the partner for subscribing to the wallet service.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.SubscriptionBundles">
            <summary>
            The transasction bundle with applied discounts for the transaction types included in the list.
            It includes pairs of transaction type and value, indicating the number of discounted transactions per transaction type.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.Tier">
            <summary>
            Id of the discount applied
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.OptOut">
            <summary>
            Opt Out
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionResponse.Status">
            <summary>
            Subscription Status
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionUsage">
            <summary>
            SubscriptionUsage definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionUsage.SubscriptionId">
            <summary>
            Subscription Plan Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Subscriptions.SubscriptionUsage.Usage">
            <summary>
            List of free transactions per transaction type with their remaining balance
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Terms">
            <summary>
            Terms and conditions
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Terms.title">
            <summary>
            Title
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Terms.subject">
            <summary>
            Subject
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Terms.version">
            <summary>
            Subject
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Transfers.Transaction">
            <summary>
            Transaction definition
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.TransactionId">
            <summary>
            The unique identifier of a transaction within the eWallet domain.
            This identifier is not shared with IB or any other system of the bank.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.WalletId">
            <summary>
            The Wallet related to the transaction, whose funds are used to execute the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Status">
            <summary>
            The status indicates if a transaction has been sent for processing to the core system or is still waiting for approval.
            Status options: PENDING_APPROVAL, EXECUTED.
            It is not related to the execution success.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.TransactionSubType">
            <summary>
            The transaction subtype provides further segregation on the transaction type.
            Supported subtypes: WalletToWallet, WalletToIBAN, WalletToNBG, GenericPayment.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.TransactionType">
            <summary>
            Supported transaction types: Transfer, Payment.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Currency">
            <summary>
            Transaction currency. Only EUR is supported currently.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Amount">
            <summary>
            The amount involved in the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.CreditIban">
            <summary>
            The IBAN to which the funds will be credited.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.CreditName">
            <summary>
            The name of the recipient for the credit transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.DebitIban">
            <summary>
            The IBAN from which the funds will be debited.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.DebitName">
            <summary>
            The name of the sender for the debit transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Timestamp">
            <summary>
            Timestamp of the creation of the transaction record.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Instant">
            <summary>
            Indicator for SEPA Instant transfer.
            Check getTransferOptions response for instant capability.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.TransactionDate">
            <summary>
            Date of the transaction execution. When the transaction status is Pending Approval, this property is expected to be empty.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Valeur">
            <summary>
            Date when transaction amount is debited or credited.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Description">
            <summary>
            Description of this transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Reference">
            <summary>
            Transaction reference number (with the "externalSystem" combination is unique).
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.BatchId">
            <summary>
            ID of the batch containing the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.BatchDescription">
            <summary>
            Description of the batch containing the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.SubmittedBy">
            <summary>
            The user who submitted the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.ApprovedBy">
            <summary>
            The user who approved the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.ExecutedBy">
            <summary>
            The user who executed the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.RejectedBy">
            <summary>
            The user who rejected the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Result">
            <summary>
            The result of the transaction. Options: Success, Failed.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.ResultReason">
            <summary>
            Message from CICS regarding the result of the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.BranchId">
            <summary>
            Creditor's bank branch identifier
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.DebtorName">
            <summary>
            Debtor's name
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.Transaction.Commission">
            <summary>
            Transaction's Commission
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Transfers.TransfersRequest">
            <summary>
            TransferRequest definition
            Add any wallet specific params here
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Transfers.TransfersRequest.TransactionId">
            <summary>
            TransactionId provided by client
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersForecatsResponse.TransactionId">
            <summary>
            Transaction Id
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.TransfersResponse">
            <summary>
            TransfersResponse definition
            Add any wallet specific params here
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.TransactionId">
            <summary>
            Transaction Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.RemType">
            <summary>Τύπος συναλλαγής ROT / RNB. Χρησιμοποιείται κατά την κλήση της remittancequery
            If equals to "RNB" then the remittance is to NBG.If equals to "ROT" then the remittance is to other bank</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.DebitAccount">
            <summary>The NBG account number (e.g. 11 digits) to debit (transfer from)</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.Currency">
            <summary>Currency, 3-letter code, e.g. <code>EUR</code>.</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.TransferAmount">
            <summary>Transfered amount.</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.AvailableBalance">
            <summary>Available balance</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.LedgerBalance">
            <summary>Ledger balance</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.ReferenceNumber">
            <summary>Reference number in *********** form</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.BankTitle">
            <summary>Full bank title </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.DebtorName">
            <summary>Νame of the debit's account beneficiary</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.RefNo">
            <summary>Reference number in IFTI***********1 form
            Επιστρέφεται μόνο κατά την εκτέλεση εμβάσματος (εκτός Εθνικής)</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.Valeur">
            <summary>Date when transaction amount debited or credited</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.NetAmountOut">
            <summary>Transaction amount</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.SumCommissionOut">
            <summary>Total comission amount</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.DebitAmountOut">
            <summary>Sum of transaction and total commision amounts</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.Beneficiaries">
            <summary>Credit account beneficiaries (as of now - 2014/03/24 - applies only to transfers inside NBG)</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.RequiresApproval">
            <summary>If <code>true</code> this transaction is deferred for approval.</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.TanCheck">
            <summary>The confirmation code of the tanNumber </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.TransactionDate">
            <summary>The exact timestamp of this transaction, as the server perceived it.</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.DebtorIBAN">
            <summary>Debit's account IBAN</summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.IsDuplicate">
            <summary> True if transaction is duplicate </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.TransfersResponse.ErrorReason">
            <summary>Error reason</summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.Statement">
            <summary>
            Represents a financial transaction statement.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.SerialNum">
            <summary>
            The serial number of this entry.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Date">
            <summary>
            The date of this transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Branch">
            <summary>
            The bank's branch.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Trans">
            <summary>
            Code for transaction type, e.g., "10" for "Κατάθεση".
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.TransDescription">
            <summary>
            Description of the transaction type.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Amount">
            <summary>
            The amount of this transaction in the currency of the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Currency">
            <summary>
            The currency of the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.AmountEquivalent">
            <summary>
            The amount of this transaction in the account's currency.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.CreditDebit">
            <summary>
            Indicates whether the transaction is a "Credit" or "Debit".
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Valeur">
            <summary>
            The date when the transaction amount was debited or credited.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Description">
            <summary>
            A description of this transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.AccountingBalance">
            <summary>
            The accounting balance after this transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Reference">
            <summary>
            The reference number of the transaction, which is unique when combined with "externalSystem".
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.ExternalSystem">
            <summary>
            Indicates the system where the remittance is sent: "RNB" for NBG, "ROT" for other banks.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.RelatedAccount">
            <summary>
            Related account number, which varies based on whether the transaction is a debit or credit.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.RelatedName">
            <summary>
            Name of the counterparty.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Timestamp">
            <summary>
            The full date and time of the transaction, precise to milliseconds.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.Account">
            <summary>
            The account of the user that initiated the transaction.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.CounterpartyBank">
            <summary>
            Name of the counterparty bank.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.Statement.AdditionalInfo">
            <summary>
            Additional information related to the transaction.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletBalanceResponse">
            <summary>
            Wallet Balance Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletBalanceResponse.LedgerBalance">
            <summary>
            Account Ledger Balance
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletBalanceResponse.AvailableBalance">
            <summary>
            Account Available Balance
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletLoadRequest">
            <summary>
            Wallet Load Request
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadRequest.WalletId">
            <summary>
            wallet Id
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadRequest.Amount">
            <summary>
            amount
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadRequest.Currency">
            <summary>
            currency
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadRequest.Iban">
            <summary>
            currency
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletLoadResponse">
            <summary>
            Wallet Load Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadResponse.Balance">
            <summary>
            Account Balance after loading
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletLoadResponse.Transaction">
            <summary>
            Transaction details after loading the wallet
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletStatementsRequest">
            <summary>
            Represents a request to retrieve wallet statements within a specified date range and with pagination options.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.DateFrom">
            <summary>
            The start date for filtering the statements.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.DateTo">
            <summary>
            The end date for filtering the statements.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.PaginationToken">
            <summary>
            A token used to paginate through the results.
            Used in the statements response.
            If it is assigned a value, it means there are more data to be fetched/
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsRequest.Limit">
            <summary>
            The maximum number of statement entries to return in the response.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Types.WalletStatementsResponse">
            <summary>
            Wallet Statements Response
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsResponse.Statements">
            <summary>
            List of Wallet Statements
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Types.WalletStatementsResponse.PaginationToken">
            <summary>
            Pagination Token
            </summary>
        </member>
    </members>
</doc>
