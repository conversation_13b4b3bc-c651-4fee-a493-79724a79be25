﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Implementation;

public class CustomerCommissionService : ICustomerCommissionService
{
    private readonly IMainFrameConnector _mainFrameConnector;
    private readonly ISubscriptionApiClientService _subscriptionApiClientService;
    private readonly ISubscriptionUsageCalculatorService _subscriptionUsageCalculatorService;

    private readonly List<TransactionSubType> _freeTransactionSubTypes = [TransactionSubType.WalletToWallet, TransactionSubType.WalletToNBG];

    public CustomerCommissionService(
        IMainFrameConnector mainFrameConnector,
        ISubscriptionApiClientService subscriptionApiClientService,
        ISubscriptionUsageCalculatorService subscriptionUsageCalculatorService)
    {
        _mainFrameConnector = mainFrameConnector;
        _subscriptionApiClientService = subscriptionApiClientService;
        _subscriptionUsageCalculatorService = subscriptionUsageCalculatorService;
    }

    public async Task UpdateCustomerCommissionAsync(Guid walletId, TransactionSubType transactionSubType, string userId, string customerCode)
    {
        await UpdateCustomerCommissionAsync(walletId, transactionSubType, userId, customerCode, commissionTransactionCount: 0);
    }

    public async Task UpdateCustomerCommissionAsync(Guid walletId, TransactionSubType transactionSubType, string userId, string customerCode, int commissionTransactionCount)
    {
        if (_freeTransactionSubTypes.Contains(transactionSubType)) return;

        var indication = await CalculateIndicationAsync(walletId, transactionSubType, commissionTransactionCount);

        //var existingIndications = await _cicsConnectorService._cicsConnectorServiceFindIndicationsAsync(userId);
        //if (existingIndications.Any(x => decimal.ToInt64(x.IndicationValue.GetValueOrDefault()) == indication.Value)) return;

        _ = await _mainFrameConnector.SubmitCustomerCommissionAsync(userId, customerCode, indication.Type, indication.Value);
    }

    private async Task<Indication> CalculateIndicationAsync(Guid walletId, TransactionSubType transactionSubType, int commissionTransactionCount)
    {
        //var subscription = await _subscriptionApiClientService.FindOneActiveByWalletIdAsync(walletId);
        //if (subscription == null) return Indications.Free;

        //var subscriptionUsage = await _subscriptionUsageCalculatorService.CalculateSubscriptionUsageAsync(walletId);

        //var freeTransactions = subscriptionUsage.Usage
        //    .Where(x => x.TransactionType == transactionSubType.ToSubscriptionTransactionType())
        //    .Select(x => Math.Max(x.Value - commissionTransactionCount, 0))
        //    .FirstOrDefault();

        //return freeTransactions == 0
        //    ? Indications.Free
        //    : subscription.SubscriptionPlanType.Value.ToIndication();

        return Indications.Free;
    }
}
