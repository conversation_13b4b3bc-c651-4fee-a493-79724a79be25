﻿using System;
using System.Text.Json.Serialization;

namespace Nbg.Ewallet.Repository.Types;

public readonly struct DapperableEnum<TEnum> where TEnum : struct
{
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public TEnum Value { get; }

    public DapperableEnum(TEnum value)
    {
        Value = value;
    }
    public DapperableEnum(string stringValue)
    {
        _ = Enum.TryParse<TEnum>(stringValue, out var value);
        Value = value;
    }

    public static implicit operator DapperableEnum<TEnum>(TEnum v) => new DapperableEnum<TEnum>(v);
    public static implicit operator TEnum(DapperableEnum<TEnum> v) => v.Value;
    public static implicit operator DapperableEnum<TEnum>(string s) => new DapperableEnum<TEnum>(s);
}
