﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// A connected corporate account user in IB.
/// </summary>
public class SandboxAvailableUser
{
    /// <summary>
    /// User ID of the user.
    /// </summary>
    [DataMember(Name = "userid")]
    public string Userid { get; set; }
    /// <summary>
    /// Alias, set by the IB with the S user profile.
    /// </summary>
    [DataMember(Name = "alias")]
    public string Alias { get; set; }
}
