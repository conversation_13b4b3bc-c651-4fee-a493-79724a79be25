using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;

namespace Nbg.Ewallet.Api.Implementation;

public class ProfileService : IProfileService
{
    private readonly IMapper _mapper;
    private readonly IWalletPermissionsRepositoryService _walletPermissionRepository;
    private readonly IUserPermissionsRepositoryService _userPermissionRepository;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly ILogger<ProfileService> _logger;
    private readonly ILimitRepositoryService _limitRepositoryService;

    public ProfileService(
        ILogger<ProfileService> logger,
        IMapper mapper,
        IWalletPermissionsRepositoryService walletPermissionRepository,
        IUserPermissionsRepositoryService userPermissionRepository,
        ILimitRepositoryService limitRepositoryService,
        IHttpContextRepositoryService httpContextRepositoryService)
    {
        _mapper = mapper;
        _walletPermissionRepository = walletPermissionRepository;
        _userPermissionRepository = userPermissionRepository;
        _httpContextRepositoryService = httpContextRepositoryService;
        _logger = logger;
        _limitRepositoryService = limitRepositoryService;
    }

    public async Task<ProfileResponse> GetProfile()
    {
        var userId = _httpContextRepositoryService.GetUserId();
        _logger.LogWarning($"userId: {userId}");
        var repoUserPermissions = await _userPermissionRepository.FindOneByActiveAndUserIdAsync(userId);
        if (repoUserPermissions == null)
        {
            return new ProfileResponse()
            {
                UserId = userId, WalletId = null, Permissions = null, Authorizations = [],
            };
        }

        var profileResponse = new ProfileResponse { UserId = userId, Authorizations = [] };

        var userPermissions = _mapper.Map<UserPermission>(repoUserPermissions);
        profileResponse.Permissions = userPermissions;
        profileResponse.WalletId = userPermissions.WalletId.ToString();

        var repoUserPermissionLimits = await _limitRepositoryService.GetByPermissionIdAsync(repoUserPermissions.Id.ToString());

        if (repoUserPermissionLimits != null && repoUserPermissionLimits.Count > 0)
        {
            userPermissions.Limits = _mapper.Map<List<Limit>>(repoUserPermissionLimits);
        }
        else
        {
            userPermissions.Limits = [];
        }

        var repoWalletPermissions = await _walletPermissionRepository.FindAllByWalletIdAsync(repoUserPermissions.WalletId);
        var walletPermissions = _mapper.Map<List<WalletPermission>>(repoWalletPermissions);

        foreach (var item in walletPermissions)
        {
            var limitsList = await _limitRepositoryService.GetByPermissionIdAsync(item.Id.ToString());
            item.Limits = _mapper.Map<List<Limit>>(limitsList);
            profileResponse.Authorizations.Add(item);
        }

        return profileResponse;
    }
}
