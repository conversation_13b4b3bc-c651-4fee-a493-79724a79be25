﻿using System.IO;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Nbg.AspNetCore.Http.Extensions;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.Test;

public static class TestConfigSetup
{
    public static ServiceProvider CreateServiceProvider()
    {
        var config = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory()) // or use path to your project root
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false)
            .Build();

        var services = new ServiceCollection();

        services.AddSingleton<IConfiguration>(config);
        services.Configure<AccountsApiClientSettings>(config.GetSection("AccountsApiClientSettings"));

        services.AddHttpClient("accountsApi", "HttpClient:accountsApi");

        return services.BuildServiceProvider();
    }
}
