openapi: 3.0.1
info:
  title: NBG E-Wallet API Specification
  description: "## Functionality at a glance\r\n\r\nWelcome to the NBG E-Wallet API. In this text, you will find guidelines for the interaction with the API. \r\nA registered company can register its users to be able to use the service. There are API endpoints for:\r\n1. Creating Wallets\r\n2. Adding And Configuring Users\r\n3. Adding Subscription Plans\r\n4. Configuring user rights and limits\r\n5. Loading/Unloading Wallet Account\r\n6. Wallet Statements\r\n7. Creating and Executing Payments\r\n8. Executing Transfers\r\n9. Payments Statements\r\n\r\n## Quick Getting Started\r\n\r\n\r\n1. **Login/Register** to the NBG Technology HUB\r\n\r\n2. Go to **\"APPS\"**\r\n\r\n3. Select your Organization and go to step 4. If you want to create a new Organization click **\\\"CREATE AN ORGANIZATION\\\"** and follow the steps below:\r\n\t1. Enter the title of your Organization\r\n\t2. Enter a short description of your Organization (optional)\r\n\t3. Click **\"SUBMIT\"**\r\n\r\n4. Select the Organization of choice and click **\"ADD AN APPLICATION\"** \r\n\t  1. Fill in the forms (title and short description)\r\n\t  2. Check **\\\"Authorization Code\\\" and \\\"Client Credentials\\\"** \r\n\t  3. Enter the **OAuth Redirect and Post Logout URIs** (these are the URIs that we will redirect the user upon logging in and logging out respectively)\r\n\t\t  \r\n\t\t  You can use the following redirect URL to easily test the API through the portal: *https://developer.nbg.gr/oauth2/redoc-callback*\r\n\t  4. Click **\"SUBMIT\"**\r\n\t  5. Store the APPs **\"Client ID\"** and **\"Client Secret\"**\r\n5. Go to **\"API PRODUCTS\"** and select the **NBG E-WALLET API**\r\n\r\n6. Click **\\\"START USING THIS API\\\"**, choose your app and click\r\n**\"SUBSCRIBE\"**\r\n\r\n7. Get an Access Token using the Access Token Flow and the API scopes provided in the Authentication and Authorization (OAuth2) section below\r\n\r\n8. Create a Sandbox\r\n\r\n9. Play with the API \r\n\r\n### Sandbox Flow\r\n\r\nThe Sandbox Flow matches the Production Flow. The difference lies into the Data used. Instead of live\r\ndata, the Sandbox flow uses mocked data. Furthermore, sandbox provides the additional functionality of simulating transactions\r\n\r\n### General Flow\r\n1. Create a consent via the appropriate endpoint (/consents), using a Client Credentials Flow.\r\n\r\n2. Once the Consent is obtained initiate an \"Authorization Code Flow\" in order to obtain the needed Access Token.\r\n    \r\n    At this point the User will need to **Log In** to the required **Consent UI Screen** using their **Internet Banking credentials**.\r\n\r\n    If the User **grants** their **Consent** a successful Access Token is returned.\r\n\r\n    The Authorization Endpoint is compiled by amending the generated \"Consent ID\", like this: https://my.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope={{scope}}&redirect_uri={{redirect_uri}}&response_type=code\r\n\r\n3. Once the Authorization Access Token is obtained, you may register a new company\r\n\r\n4. You can then create a wallet account\r\n\r\n5. After wallet is created, you can proceed with payments and transfers\r\n\r\n\r\n## Authentication and Authorization (OAuth2)\r\n\r\nThis API version uses the OAuth2 protocol for authentication and authorization, which means that a\r\nBearer (access token) should be acquired. An access token can be retrieved using the client_id and\r\nclient_secret of the APP that you created and subscribed in this API, and your own credentials\r\n(username, password) that you use to sign in the NBG Technology HUB. The scopes are defined below:\r\n\r\n**Authorization Endpoint:** \r\n\r\n\t  https://my.nbg.gr/identity/connect/authorize\r\n\r\n\r\n**Token Endpoint:** \r\n\r\n\t  https://my.nbg.gr/identity/connect/token\r\n\r\n### Authorization Code ###\r\n\r\n**Sandbox Scopes:** \r\n\r\n\t  sandbox-ewallet-api-v1 offline_access\r\n\r\n\r\n**Production Scopes:** \r\n\r\n\t  ewallet-api-v1 offline_access\r\n\r\n### Client Credentials ###\r\n\r\n**Sandbox Scopes:** \r\n\r\n\t  sandbox-ewallet-api-v1\r\n\r\n\r\n**Production Scopes:** \r\n\r\n\t  ewallet-api-v1\r\n\r\n\r\nSee more [here](https://developer.nbg.gr/oauth-document)\r\n\r\n## Create your Sandbox\r\n\r\nCreate a new Sandbox application by invoking the POST /sandbox. This call will generate a new Sandbox\r\nwith a unique sandbox-id.\r\n\r\n\r\n__Important!__ Before proceeding save the sandbox id you just created.\r\n\r\n\r\nWhen you create a sandbox, sandbox specific data are generated as sample data.\r\n\r\n\r\n## Start Testing\r\n\r\nOnce you have your sandbox-id, you can start invoking the rest of the operations by providing the\r\nmandatory http header **sandbox-id**  and the http headers described below.\r\n\r\n## Important notes\r\n\r\n\r\n**Request headers**\r\n\r\n\r\nThe following HTTP header parameters are required for every call:\r\n\r\n\r\n1. Authorization. The Auth2 Token\r\n\r\n2. sandbox-id. Your Sandbox ID\r\n\r\n# Feedback and Questions\r\n\r\nWe would love to hear your feedback and answer your questions. Send us at\r\n[<EMAIL>](<EMAIL>)\r\n\r\n\r\nCheck out our [Sandbox Postman Collection](https://developer.nbg.gr/partner/news/Access-to-Github-repositories-for-Partners)!\r\n\r\n\r\n________________________________________\r\n\r\nCreated by [**NBG**](https://www.nbg.gr/)."
  contact:
    name: NBG
    url: https://developer.nbg.gr/
    email: <EMAIL>
  version: v1.0
servers:
  - url: https://apis.nbg.gr/sandbox/ewallet/oauth2/v1
    description: Sandbox Server
  - url: https://services.nbg.gr/apis/ewallet/v1
    description: Production Server
paths:
  /profile:
    get:
      tags:
        - User Profile
      summary: Retrieves the current user profile.
      description: 'The user profile is consisted of user specific information, user permissions on own wallet and access to other wallets.'
      operationId: profile
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'
  '/Payments/{walletId}/batch/execute':
    post:
      tags:
        - Payments
      summary: Execute Massive Payment
      operationId: Execute
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchPaymentsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsResponse'
  '/Payments/{walletId}/pay':
    post:
      tags:
        - Payments
      summary: Bill Payment from a wallet to an organization
      operationId: Pay
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExtendedPaymentsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtendedPaymentResponse'
  '/Payments/{walletId}/commission':
    post:
      tags:
        - Payments
      summary: Bill Payment commissions
      operationId: Commission
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExtendedPaymentsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommissionResponse'
  '/Payments/{walletId}/batch/commission':
    post:
      tags:
        - Payments
      summary: Execute Massive Payment
      operationId: BatchCommission
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchPaymentsCommissionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchCommissionResponse'
  /sandbox:
    post:
      tags:
        - Sandbox
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SandboxRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/SandboxRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/SandboxRequest'
      responses:
        '500':
          description: Internal Server Error
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Response'
            application/json:
              schema:
                $ref: '#/components/schemas/Response'
            text/json:
              schema:
                $ref: '#/components/schemas/Response'
  '/sandbox/{sandbox_id}':
    get:
      tags:
        - Sandbox
      parameters:
        - name: sandbox_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '500':
          description: Internal Server Error
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Response'
            application/json:
              schema:
                $ref: '#/components/schemas/Response'
            text/json:
              schema:
                $ref: '#/components/schemas/Response'
    delete:
      tags:
        - Sandbox
      parameters:
        - name: sandbox_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/SandboxSuccessResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/SandboxSuccessResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/SandboxSuccessResponse'
        '500':
          description: Internal Server Error
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Response'
            application/json:
              schema:
                $ref: '#/components/schemas/Response'
            text/json:
              schema:
                $ref: '#/components/schemas/Response'
    put:
      tags:
        - Sandbox
      parameters:
        - name: sandbox_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EwalletDataModel'
          text/json:
            schema:
              $ref: '#/components/schemas/EwalletDataModel'
          application/*+json:
            schema:
              $ref: '#/components/schemas/EwalletDataModel'
      responses:
        '500':
          description: Internal Server Error
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Response'
            application/json:
              schema:
                $ref: '#/components/schemas/Response'
            text/json:
              schema:
                $ref: '#/components/schemas/Response'
  /Subscriptions/subscription-plans:
    get:
      tags:
        - Subscriptionplans
      summary: get subscription plans
      operationId: subscription-plans
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SubscriptionPlan'
  /getTerms:
    get:
      tags:
        - Terms
      summary: get terms and conditions
      operationId: getTerms
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Terms'
  '/Transfers/{walletId}/transfer':
    post:
      tags:
        - Transfers
      summary: 'Transfer from a wallet to a wallet, or from a wallet to an NBG account'
      operationId: Transfer
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransfersRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransfersResponse'
  '/Transfers/{walletId}/batch':
    post:
      tags:
        - Transfers
      summary: 'Transfer from a wallet to many, or from a wallet to many NBG accounts'
      operationId: Batch
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchTransfersRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchTransfersResponse'
  '/Transfers/{walletId}/calculateFees':
    post:
      tags:
        - Transfers
      summary: Calculates the fees applied to the transaction
      operationId: CalculateFees
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransfersRequestBase'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransferExpensesCommissionsResponse'
  '/Transfers/{walletId}/batch/calculateFees':
    post:
      tags:
        - Transfers
      summary: Calculates the fees applied to the transaction
      operationId: BatchCalculateFees
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchTransfersRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchTransferExpensesCommissionsResponse'
  '/Wallet/{walletId}/users':
    get:
      tags:
        - Wallet
      summary: Retrieves available user to assign user permissions to.
      description: "Retrieves all company users that are available to be connected to the wallet.\r\nThis will only return data for corporate IB accounts."
      operationId: GetOrganizationUsers
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Users retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableUserResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /Wallet/search-wallets:
    get:
      tags:
        - Wallet
      summary: Search for other wallets.
      description: "Search for other registered wallets within the eWallet community.\r\nAll filters are applied with a %like% operator.\r\nAt least one filter must be present, otherwise a 4XX error will be returned."
      operationId: SearchWallets
      parameters:
        - name: vatNumber
          in: query
          description: The VAT number to search for.
          schema:
            type: string
        - name: walletName
          in: query
          description: The wallet name to search for.
          schema:
            type: string
        - name: iban
          in: query
          description: The IBAN to search for.
          schema:
            type: string
        - name: organizationName
          in: query
          description: The OrganizationName to search for.
          schema:
            type: string
      responses:
        '200':
          description: Wallets retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WalletLite'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/statements':
    get:
      tags:
        - Wallet
      summary: Retrieves statements for a wallet account.
      description: "The data returned will be the same as returned from the corresponding functionality from IB.\r\nIt includes transcations that have happened outside of the eWallet ecosystem."
      operationId: statements
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: DateFrom
          in: query
          description: The start date for filtering the statements.
          schema:
            type: string
            format: date-time
        - name: DateTo
          in: query
          description: The end date for filtering the statements.
          schema:
            type: string
            format: date-time
        - name: PaginationToken
          in: query
          description: "A token used to paginate through the results.\r\nUsed in the statements response.\r\nIf it is assigned a value, it means there are more data to be fetched/"
          schema:
            type: string
        - name: Limit
          in: query
          description: The maximum number of statement entries to return in the response.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Statements retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletStatementsResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/transactions':
    get:
      tags:
        - Wallet
      summary: Retrieves transactions for a wallet account.
      operationId: getwallettransactions
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: BatchId
          in: query
          schema:
            type: string
            format: uuid
        - name: Status
          in: query
          schema:
            $ref: '#/components/schemas/TransactionStatus'
      responses:
        '200':
          description: Transactions retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Transaction'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/transactions/{transactionId}/approve':
    put:
      tags:
        - Wallet
      summary: Approves a transaction for a wallet account.
      operationId: approvetransaction
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: transactionId
          in: path
          description: The transaction identifier.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Transaction approved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/transactions/{transactionId}/reject':
    put:
      tags:
        - Wallet
      summary: Rejects a transaction for a wallet account.
      operationId: rejecttransaction
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: transactionId
          in: path
          description: The transaction identifier.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Transaction rejected successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/authorization-requests':
    post:
      tags:
        - Wallet
      summary: Requests authorization for a wallet account.
      operationId: requestauthorization
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        description: The request containing authorization details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WalletAuthorizationRequest'
      responses:
        '200':
          description: Authorization request created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletAuthorizationResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - Wallet
      summary: Retrieves authorization requests for a wallet account.
      operationId: getauthorizationrequests
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          description: The status of the authorization requests to filter by.
          schema:
            $ref: '#/components/schemas/RequestStatus'
      responses:
        '200':
          description: Authorization requests retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WalletAuthorizationResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/authorization-requests/{authRequestId}/reject':
    put:
      tags:
        - Wallet
      summary: Rejects an authorization request for a wallet account.
      operationId: rejectrequestauthorization
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: authRequestId
          in: path
          description: The authorization request identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Authorization request rejected successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletAuthorizationResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/statements-pdf-export':
    get:
      tags:
        - Wallet
      summary: Returns wallet account statements in a PDF format.
      operationId: statementspdfexport
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: DateFrom
          in: query
          required: true
          schema:
            type: string
            format: date-time
        - name: DateTo
          in: query
          required: true
          schema:
            type: string
            format: date-time
        - name: UserId
          in: query
          schema:
            type: string
        - name: Account
          in: query
          schema:
            type: string
        - name: Currency
          in: query
          schema:
            type: string
      responses:
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '200':
          description: Authorization request rejected successfully.
  '/Wallet/{walletId}/wallet-permissions':
    post:
      tags:
        - Wallet
      summary: Grants access to other wallets.
      operationId: SetWalletPermissionsForOtherWallets
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        description: The request containing permissions details for other wallets.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WalletPermissionsRequest'
      responses:
        '200':
          description: Permissions granted successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletPermissionResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - Wallet
      summary: Retrieves permissions for other wallets.
      operationId: GetWalletPermissionsForOtherWallets
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: showAll
          in: query
          description: Whether to show all permissions.
          schema:
            type: boolean
      responses:
        '200':
          description: Permissions retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WalletPermission'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/wallet-permissions/{externalWalletId}':
    get:
      tags:
        - Wallet
      summary: Retrieves permissions for a specific external wallet.
      operationId: GetWalletPermissionsForOtherWallet
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: externalWalletId
          in: path
          description: The external wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: showAll
          in: query
          description: Whether to show all permissions.
          schema:
            type: boolean
      responses:
        '200':
          description: Permissions retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WalletPermission'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/wallet-permissions/{targetWalletId}/revoke':
    put:
      tags:
        - Wallet
      summary: Revokes permissions for a specific external wallet.
      operationId: RevokeExternalWalletPermissions
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: targetWalletId
          in: path
          description: The target wallet identifier whose permissions are to be revoked.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Permissions revoked successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WalletPermission'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/info':
    get:
      tags:
        - Wallet
      summary: Retrieves information about a specific wallet.
      operationId: GetWalletInformation
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Returns the wallet information.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Wallet'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: If the wallet is not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/balance':
    get:
      tags:
        - Wallet
      summary: Retrieves the current balance of a subscription plan.
      operationId: balance
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Returns the current balance.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWalletBalanceResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: If the wallet or balance is not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/edit':
    put:
      tags:
        - Wallet
      summary: Retrieves the current balance of a subscription plan.
      operationId: Edit
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EditWalletNameRequest'
      responses:
        '200':
          description: Returns the current balance.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Wallet'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: If the wallet or balance is not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/my-transactions':
    get:
      tags:
        - Wallet
      summary: Retrieves transactions for a wallet account.
      operationId: getmytransactions
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Transactions retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Transaction'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/load':
    post:
      tags:
        - Wallet
      summary: Loads funds into the wallet account.
      operationId: load
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        description: The request containing load details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WalletLoadRequest'
      responses:
        '200':
          description: Funds loaded successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletLoadResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/unload':
    post:
      tags:
        - Wallet
      summary: Unloads funds from the wallet account.
      operationId: unload
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        description: The request containing unload details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WalletLoadRequest'
      responses:
        '200':
          description: Funds unloaded successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletLoadResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /Wallet/register:
    post:
      tags:
        - Wallet
      summary: Registers a new wallet.
      description: "Registers a new wallet by providing a name for the wallet. This name can be used when searching for other wallets.\r\nA GUID is generated and connected to the newly created wallet, uniquely identifying it.\r\nCan be called from any user, since there are is not wallet yet, and therefore no permissions assigned."
      operationId: Register
      requestBody:
        description: The wallet registration request.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WalletRegister'
      responses:
        '200':
          description: Returns the newly registered wallet.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Wallet'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/account':
    post:
      tags:
        - Wallet
      summary: Creates the special purpose account in IB and connects it to the wallet.
      description: "This action will only succeed if there is an active, non-expired subscription for the wallet.\r\nUse the corresponding enpoint to create a subscription.\r\nIf there i already a special purpose account connected with the wallet, this API call will fail."
      operationId: account
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Returns the created wallet account.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Wallet'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/subscription':
    post:
      tags:
        - Wallet
      summary: Creates a new subscription.
      description: "Creates a new subscription if it is called for the frist time, or extends an existing one.\r\nThe new subscription can be of a different Plan Id than the current one, in case of extension/renewal."
      operationId: subscription
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        description: The subscription creation request.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSubscriptionRequest'
      responses:
        '200':
          description: Returns the created subscription response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSubscriptionResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - Wallet
      summary: "Returns the active subscription for the wallet.\r\nWill return an error message in case that no active subscription is found."
      operationId: GetSubscription
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Returns the subscription details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSubscriptionResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: If the subscription is not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/subscription/usage':
    get:
      tags:
        - Wallet
      summary: 'Returns the current subscription usage, in terms of discount transactions consumption.'
      operationId: GetSubscriptionUsage
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Returns the subscription usage details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionUsage'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: If the user is unauthorized.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: If the user is forbidden from accessing this resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: If the subscription usage is not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/user-permissions':
    post:
      tags:
        - Wallet
      summary: Grants access to internal company users.
      operationId: SetWalletUserPermissions
      parameters:
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        description: The request containing user permissions details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetWalletUserPermissionsRequest'
      responses:
        '200':
          description: Permissions granted successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPermissionResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - Wallet
      summary: Retrieves all internal company users' permissions.
      operationId: GetWalletUserPermissions
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: showAll
          in: query
          description: Whether to show all permissions.
          schema:
            type: boolean
      responses:
        '200':
          description: Permissions retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPermissionResponse'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/user-permissions/{userId}':
    get:
      tags:
        - Wallet
      summary: Retrieves access rights for a specific internal wallet user.
      operationId: GetWalletPermissionForUser
      parameters:
        - name: walletId
          in: path
          description: The wallet identifier.
          required: true
          schema:
            type: string
            format: uuid
        - name: userId
          in: path
          description: The user identifier.
          required: true
          schema:
            type: string
        - name: showAll
          in: query
          description: 'Whether to show all permissions, even if the permission is expired.'
          schema:
            type: boolean
      responses:
        '200':
          description: Permissions retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserPermission'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/Wallet/{walletId}/user-permissions/{userId}/revoke':
    put:
      tags:
        - Wallet
      summary: Revokes access of a specific internal user.
      operationId: RevokeUserPermissionsForUser
      parameters:
        - name: userId
          in: path
          description: The user identifier.
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Permission revoked successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPermission'
        '400':
          description: If the request is invalid.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: If there is an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    AcceptDuplicateSpecification:
      type: object
      properties:
        includeUserId:
          type: boolean
          nullable: true
        includePaymentMethod:
          type: boolean
          nullable: true
        statuses:
          type: array
          items:
            type: string
          nullable: true
        hourInterval:
          type: integer
          format: int32
          nullable: true
        includeCcy:
          type: boolean
          nullable: true
      additionalProperties: false
    Additional:
      type: object
      properties:
        ledgerBalance:
          type: number
          format: double
          nullable: true
        availableBalance:
          type: number
          format: double
          nullable: true
        sendCutOffTime:
          type: string
          nullable: true
      additionalProperties: false
    AvailableUser:
      type: object
      properties:
        userid:
          type: string
          description: User ID of the user.
          nullable: true
        alias:
          type: string
          description: 'Alias, set by the IB with the S user profile.'
          nullable: true
      additionalProperties: false
      description: A connected corporate account user in IB.
    AvailableUserResponse:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/AvailableUser'
          description: List of ib users that belong to the specific company
          nullable: true
      additionalProperties: false
      description: A list of the users connected to the IB corporate account.
    BatchCalculateFeesResponse:
      type: object
      properties:
        sumCommissionCurrency:
          type: number
          format: double
          nullable: true
        creditAmountCurrency:
          type: number
          format: double
          nullable: true
        bankTitle:
          type: string
          nullable: true
        exchangeRate:
          type: number
          format: double
        netAmount:
          type: number
          format: double
        debitNetAmount:
          type: number
          format: double
        sumCommission:
          type: number
          format: double
        debitAmount:
          type: number
          format: double
        eteCommission:
          type: number
          format: double
        deptExpenses:
          type: number
          format: double
        nonStpExpenses:
          type: number
          format: double
        urgentExpenses:
          type: number
          format: double
        onlineExpenses:
          type: number
          format: double
        exchangeProfit:
          type: number
          format: double
        transactionType:
          $ref: '#/components/schemas/TransactionSubType'
        error:
          type: string
          nullable: true
      additionalProperties: false
    BatchCommissionResponse:
      type: object
      properties:
        commissions:
          type: array
          items:
            $ref: '#/components/schemas/CommissionResponseBatch'
          description: ''
          nullable: true
      additionalProperties: false
      description: ''
    BatchPaymentsCommissionRequest:
      type: object
      properties:
        batchId:
          type: string
          description: Mass Transaction Id
          format: uuid
        batch:
          type: array
          items:
            $ref: '#/components/schemas/ExtendedPaymentsRequest'
          description: List of payment requests
          nullable: true
      additionalProperties: false
      description: ''
    BatchPaymentsRequest:
      type: object
      properties:
        tanNumber:
          type: string
          description: The sms OTP number the user has keyed in
          nullable: true
        isSmsOtp:
          type: boolean
          description: The sms OTP flag
          nullable: true
        batchId:
          type: string
          description: Mass Transaction Id
          format: uuid
        batch:
          type: array
          items:
            $ref: '#/components/schemas/ExtendedPaymentsRequest'
          description: List of payment requests
          nullable: true
      additionalProperties: false
      description: ''
    BatchTransferExpensesCommissionsResponse:
      type: object
      properties:
        commissions:
          type: array
          items:
            $ref: '#/components/schemas/BatchCalculateFeesResponse'
          nullable: true
      additionalProperties: false
    BatchTransfersRequest:
      type: object
      properties:
        batch:
          type: array
          items:
            $ref: '#/components/schemas/TransfersRequest'
          nullable: true
        batchId:
          type: string
          format: uuid
      additionalProperties: false
    BatchTransfersResponse:
      type: object
      properties:
        batchId:
          type: string
          format: uuid
        transfers:
          type: array
          items:
            $ref: '#/components/schemas/TransfersResponse'
          nullable: true
      additionalProperties: false
    CdtNoteAmt:
      type: object
      properties:
        note:
          type: string
          nullable: true
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    ChargesInfo:
      type: object
      properties:
        chrgbr:
          $ref: '#/components/schemas/ChrgbrEnum'
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    ChrgbrEnum:
      enum:
        - DEBT
        - CRED
        - SHAR
        - SLEV
        - COMM
      type: string
    CodeDescription:
      type: object
      properties:
        code:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    CommissionInfo:
      type: object
      properties:
        total:
          type: number
          format: double
        nbg:
          type: number
          format: double
        merchant:
          type: number
          format: double
        thirdParty:
          type: number
          format: double
        subAgent:
          type: number
          format: double
        nbgOrg:
          type: number
          format: double
      additionalProperties: false
    CommissionResponse:
      type: object
      properties:
        commissionInfo:
          $ref: '#/components/schemas/CommissionInfo'
      additionalProperties: false
    CommissionResponseBatch:
      type: object
      properties:
        commissionInfo:
          $ref: '#/components/schemas/CommissionInfo'
        error:
          type: string
          description: ''
          nullable: true
      additionalProperties: false
    CreateSubscriptionRequest:
      type: object
      properties:
        amount:
          type: number
          description: Subscription cost
          format: double
        months:
          type: integer
          description: duration of subscription in months
          format: int32
        subscriptionBundles:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionBundle'
          description: Commission-free transactions per type
          nullable: true
        planId:
          $ref: '#/components/schemas/SubscriptionPlanType'
      additionalProperties: false
      description: SubscriptionRequest definition
    CreateSubscriptionResponse:
      type: object
      properties:
        subscriptionId:
          type: string
          description: Subscription Id
          format: uuid
        startDate:
          type: string
          description: Start Date
          format: date-time
        endDate:
          type: string
          description: Expiration date of the subscription.
          format: date-time
        amount:
          type: number
          description: "Monetary amount paid by the End User to the partner for subscribing to the wallet service.\r\nThis is only for consolidation reasons and it is not used in any other way."
          format: double
        subscriptionBundles:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionBundle'
          description: "The transasction bundle with applied discounts for the transaction types included in the list.\r\nIt includes pairs of transaction type and value, indicating the number of discounted transactions per transaction type."
          nullable: true
        planId:
          $ref: '#/components/schemas/SubscriptionPlanType'
      additionalProperties: false
      description: CreateSubscriptionResponse definition
    Creditor:
      type: object
      properties:
        name:
          type: string
          nullable: true
        creditorAccount:
          $ref: '#/components/schemas/CreditorAccount'
        creditorBank:
          $ref: '#/components/schemas/CreditorBank'
        addressLine:
          type: string
          nullable: true
      additionalProperties: false
    CreditorAccount:
      type: object
      properties:
        iban:
          type: string
          nullable: true
        pan:
          type: string
          nullable: true
      additionalProperties: false
    CreditorBank:
      type: object
      properties:
        bic:
          type: string
          nullable: true
        branchId:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
      additionalProperties: false
    Debtor:
      type: object
      properties:
        name:
          type: string
          nullable: true
        debtorAccount:
          $ref: '#/components/schemas/DebtorAccount'
        debtorBank:
          $ref: '#/components/schemas/DebtorBank'
        telephone:
          type: string
          nullable: true
        addressLine:
          type: string
          nullable: true
        extraIdentities:
          type: array
          items:
            $ref: '#/components/schemas/EntityIdentity'
          nullable: true
      additionalProperties: false
    DebtorAccount:
      type: object
      properties:
        iban:
          type: string
          nullable: true
        pan:
          type: string
          nullable: true
      additionalProperties: false
    DebtorBank:
      type: object
      properties:
        bic:
          type: string
          nullable: true
        branchId:
          type: string
          nullable: true
      additionalProperties: false
    DeferredRequest:
      type: object
      properties:
        isDeferred:
          type: boolean
          nullable: true
        deferredDate:
          type: string
          format: date-time
          nullable: true
        deferredName:
          type: string
          nullable: true
        deferredFrequency:
          type: integer
          format: int32
          nullable: true
        deferredTotalPayments:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    DiscountTransaction:
      type: object
      properties:
        transactionType:
          $ref: '#/components/schemas/SubscriptionTransactionType'
        value:
          type: number
          description: Value
          format: double
      additionalProperties: false
      description: DiscountTransaction definition
    DscntApldAmt:
      type: object
      properties:
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    DuePyblAmt:
      type: object
      properties:
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    EditWalletNameRequest:
      type: object
      properties:
        walletName:
          type: string
          description: The name of the wallet. It is set by the wallet creator during registration and it is not unqiue across wallets.
          nullable: true
      additionalProperties: false
    EntityIdentity:
      type: object
      properties:
        entityId:
          type: string
          nullable: true
        idType:
          type: string
          nullable: true
        idScope:
          type: string
          nullable: true
      additionalProperties: false
    ErrorCategory:
      enum:
        - Business
        - Communication
        - Technical
        - Security
      type: string
    ErrorDetails:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: Gets or sets the list of error messages.
          nullable: true
      additionalProperties: false
      description: Represents the details of an error.
    ErrorResponse:
      type: object
      properties:
        code:
          type: string
          description: Gets or sets the error code or title of the error.
          nullable: true
        status:
          type: integer
          description: Gets or sets the HTTP status code of the response.
          format: int32
        traceId:
          type: string
          description: 'Gets or sets the trace identifier for the request, useful for debugging.'
          nullable: true
        errors:
          $ref: '#/components/schemas/ErrorDetails'
      additionalProperties: false
      description: Represents an error response returned by the API.
    ErrorSeverity:
      enum:
        - Warning
        - Error
        - Info
      type: string
    EwalletDataModel:
      type: object
      properties:
        sandboxId:
          type: string
          nullable: true
        version:
          type: integer
          format: int32
        ewalletSandbox:
          $ref: '#/components/schemas/EwalletSandbox'
      additionalProperties: false
    EwalletSandbox:
      type: object
      properties:
        statements:
          type: array
          items:
            $ref: '#/components/schemas/SandboxStatement'
          nullable: true
        availableUsers:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/SandboxAvailableUser'
            nullable: true
          nullable: true
        userPermissions:
          type: array
          items:
            $ref: '#/components/schemas/SandboxUserPermission'
          nullable: true
        walletsPermissions:
          type: array
          items:
            $ref: '#/components/schemas/SandboxWalletPermission'
          nullable: true
        walletsSubscription:
          type: array
          items:
            $ref: '#/components/schemas/SandboxSubscription'
          nullable: true
        wallets:
          type: array
          items:
            $ref: '#/components/schemas/SandboxWallet'
          nullable: true
        walletsBalance:
          type: array
          items:
            $ref: '#/components/schemas/SandboxWalletBalance'
          nullable: true
        terms:
          $ref: '#/components/schemas/SandboxTerms'
        subscriptionPlans:
          type: array
          items:
            $ref: '#/components/schemas/SandboxSubscriptionPlan'
          nullable: true
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/SandboxTransaction'
          nullable: true
        walletAuthorizationRequests:
          type: array
          items:
            $ref: '#/components/schemas/WalletAuthorizationResponse'
          nullable: true
        subscriptionBundles:
          type: array
          items:
            $ref: '#/components/schemas/SandboxSubscriptionBundle'
          nullable: true
        limits:
          type: array
          items:
            $ref: '#/components/schemas/SandboxLimit'
          nullable: true
      additionalProperties: false
    ExpensesType:
      enum:
        - SHA
        - BEN
        - OUR
      type: string
    ExtendedPaymentResponse:
      type: object
      properties:
        paymentIdentification:
          $ref: '#/components/schemas/PaymentIdentification'
        creditor:
          $ref: '#/components/schemas/Creditor'
        debtor:
          $ref: '#/components/schemas/Debtor'
        settlementInfo:
          $ref: '#/components/schemas/SettlementInfo'
        commissionInfo:
          $ref: '#/components/schemas/CommissionInfo'
        additional:
          $ref: '#/components/schemas/Additional'
        additionalInfo:
          type: object
          additionalProperties:
            type: string
            nullable: true
          nullable: true
        requiresApproval:
          type: boolean
          description: ''
        transactionId:
          type: string
          description: ''
          format: uuid
        errorReason:
          type: string
          description: Error reason
          nullable: true
      additionalProperties: false
      description: ''
    ExtendedPaymentsRequest:
      required:
        - transactionId
      type: object
      properties:
        paymentIdentification:
          $ref: '#/components/schemas/PaymentIdentification'
        paymentOrgIdentification:
          $ref: '#/components/schemas/PaymentOrgIdentification'
        chargesInfo:
          $ref: '#/components/schemas/ChargesInfo'
        settlementInfo:
          $ref: '#/components/schemas/SettlementInfo'
        debtor:
          $ref: '#/components/schemas/Debtor'
        remittanceInfo:
          $ref: '#/components/schemas/RemittanceInfo'
        acceptDuplicate:
          type: boolean
        acceptDuplicateSpecification:
          $ref: '#/components/schemas/AcceptDuplicateSpecification'
        origin:
          $ref: '#/components/schemas/SpotMachineOrigin'
        userID:
          type: string
          nullable: true
        requestMachineId:
          type: string
          nullable: true
        machineId:
          type: string
          nullable: true
        ultimateDebtor:
          $ref: '#/components/schemas/UltimateDebtor'
        deferred:
          $ref: '#/components/schemas/DeferredRequest'
        originalHeaderId:
          type: string
          format: uuid
        requestMetadata:
          $ref: '#/components/schemas/RequestMetadata'
        transactionId:
          type: string
          format: uuid
      additionalProperties: false
      description: Payments Pay Request
    FreeTransaction:
      type: object
      properties:
        transactionType:
          $ref: '#/components/schemas/SubscriptionTransactionType'
        value:
          type: integer
          description: value
          format: int32
      additionalProperties: false
      description: FreeTransaction definition
    GetSubscriptionResponse:
      type: object
      properties:
        subscriptionId:
          type: string
          description: Subscription Plan Id
          format: uuid
        startDate:
          type: string
          description: Start Date
          format: date-time
        expirationDate:
          type: string
          description: Expiration  date of the subscription.
          format: date-time
        amount:
          type: number
          description: "Monetary amount paid by the End User to the partner for subscribing to the wallet service.\r\nThis is only for consolidation reasons and it is not used in any other way."
          format: double
        planId:
          $ref: '#/components/schemas/SubscriptionPlanType'
        subscriptionBundles:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionBundle'
          description: "The transaction bundle with applied discounts for the transaction types included in the list.\r\nIt includes pairs of transaction type and value, indicating the number of discounted transactions per transaction type."
          nullable: true
      additionalProperties: false
      description: GetSubscriptionResponse definition
    GetWalletBalanceResponse:
      type: object
      properties:
        ledgerBalance:
          type: number
          description: Wallet Account ledger balance
          format: double
        availableBalance:
          type: number
          description: Wallet Account available balance
          format: double
      additionalProperties: false
      description: GetWalletBalanceResponse definition
    InvoiceCommissionAmt:
      type: object
      properties:
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    IssuerEnum:
      enum:
        - NBG
        - NONNBG
        - IDEAFO
        - TUP
        - LABOUR
        - B2BPAY
      type: string
    Limit:
      type: object
      properties:
        permissionId:
          type: string
          description: The identifier of the permission to which this limit is associated.
          format: uuid
        transactionType:
          $ref: '#/components/schemas/LimitTransactionType'
        amount:
          type: number
          description: The maximum amount allowed for the transaction type within the specified timeframe.
          format: double
        timeframe:
          $ref: '#/components/schemas/TimeFrameType'
      additionalProperties: false
      description: Represents a limit set on wallet transactions.
    LimitTransactionType:
      enum:
        - WalletToWallet
        - WalletToNBG
        - WalletToIBAN
        - Payment
      type: string
    PaymentIdentification:
      type: object
      properties:
        instrId:
          type: string
          nullable: true
        endToEndId:
          type: string
          nullable: true
        txId:
          type: string
          format: uuid
        clrSysRef:
          type: string
          nullable: true
        paymentRef:
          type: string
          nullable: true
      additionalProperties: false
    PaymentOrgIdentification:
      type: object
      properties:
        id:
          type: string
          format: uuid
        paymentCode:
          type: string
          nullable: true
        paymentNum:
          type: string
          nullable: true
        paymentAcc:
          type: string
          nullable: true
        paymentType:
          $ref: '#/components/schemas/IssuerEnum'
        isOnline:
          type: boolean
          nullable: true
        validationAlgorithm:
          type: string
          nullable: true
        barCode:
          type: array
          items:
            type: string
          nullable: true
        fields:
          type: object
          additionalProperties:
            type: string
            nullable: true
          nullable: true
        extraData:
          type: object
          additionalProperties:
            type: string
            nullable: true
          nullable: true
      additionalProperties: false
    PaymentsResponse:
      type: object
      properties:
        batchId:
          type: string
          description: Mass Transaction Id
          format: uuid
        payments:
          type: array
          items:
            $ref: '#/components/schemas/ExtendedPaymentResponse'
          description: List of payments
          nullable: true
      additionalProperties: false
      description: Payments Response
    ProfileResponse:
      type: object
      properties:
        userId:
          type: string
          description: The User Id used to log in to IB.
          nullable: true
        walletId:
          type: string
          description: The Wallet Id that this User Id is connected to.
          nullable: true
        permissions:
          $ref: '#/components/schemas/UserPermission'
        authorizations:
          type: array
          items:
            $ref: '#/components/schemas/WalletPermission'
          description: Access granted to other wallets along with the specific permissions and limits.
          nullable: true
      additionalProperties: false
      description: User Profile information.
    ReceiverAccType:
      enum:
        - IBAN
        - TEXT
      type: string
    RemittanceInfo:
      type: object
      properties:
        unstructured:
          $ref: '#/components/schemas/UnStructured'
        structured:
          $ref: '#/components/schemas/Structured'
      additionalProperties: false
    RequestMetadata:
      type: object
      properties:
        referenceID:
          type: string
          nullable: true
        sydipel:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        userAgent:
          type: string
          nullable: true
        ipAddress:
          type: string
          nullable: true
        sessionID:
          type: string
          nullable: true
        approvedWithSCA:
          type: string
          nullable: true
        deviceID:
          type: string
          nullable: true
      additionalProperties: false
    RequestStatus:
      enum:
        - Pending
        - Completed
        - Rejected
      type: string
    RequestorWalletAuthorization:
      type: object
      properties:
        walletId:
          type: string
          description: Wallet Id
          format: uuid
        walletAccount:
          type: string
          description: Wallet account IBAN
          nullable: true
        organizationName:
          type: string
          description: Company’s Name
          nullable: true
        vatNumber:
          type: string
          description: vatNumber
          nullable: true
        walletName:
          type: string
          description: Wallet Name - By default the company’s name
          nullable: true
      additionalProperties: false
      description: ''
    Response:
      type: object
      properties:
        exception:
          $ref: '#/components/schemas/ResponseMessage'
        messages:
          type: array
          items:
            $ref: '#/components/schemas/ResponseMessage'
          nullable: true
        executionTime:
          type: number
          format: double
      additionalProperties: false
    ResponseMessage:
      type: object
      properties:
        id:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        severity:
          $ref: '#/components/schemas/ErrorSeverity'
        category:
          $ref: '#/components/schemas/ErrorCategory'
      additionalProperties: false
    RmtdAmt:
      type: object
      properties:
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    SandboxAvailableUser:
      type: object
      properties:
        userid:
          type: string
          nullable: true
        alias:
          type: string
          nullable: true
      additionalProperties: false
    SandboxLimit:
      type: object
      properties:
        id:
          type: string
          format: uuid
        permissionId:
          type: string
          format: uuid
        transactionType:
          $ref: '#/components/schemas/LimitTransactionType'
        amount:
          type: number
          format: double
        timeframe:
          $ref: '#/components/schemas/TimeFrameType'
      additionalProperties: false
    SandboxRequest:
      type: object
      properties:
        sandboxId:
          type: string
          nullable: true
      additionalProperties: false
    SandboxStatement:
      type: object
      properties:
        serialNum:
          type: string
          nullable: true
        date:
          type: string
          nullable: true
        branch:
          type: string
          nullable: true
        trans:
          type: string
          nullable: true
        transDescription:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        currency:
          type: string
          nullable: true
        amountEquivalent:
          type: number
          format: double
        creditDebit:
          type: string
          nullable: true
        valeur:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        accountingBalance:
          type: number
          format: double
        reference:
          type: string
          nullable: true
        externalSystem:
          type: string
          nullable: true
        relatedAccount:
          type: string
          nullable: true
        relatedName:
          type: string
          nullable: true
        timestamp:
          type: string
          nullable: true
        account:
          type: string
          nullable: true
        counterpartyBank:
          type: string
          nullable: true
        additionalInfo:
          type: string
          nullable: true
      additionalProperties: false
    SandboxSubscription:
      type: object
      properties:
        subscriptionId:
          type: string
          format: uuid
        walletId:
          type: string
          format: uuid
        amount:
          type: number
          format: double
        months:
          type: integer
          format: int32
        subscriptionBundles:
          type: array
          items:
            $ref: '#/components/schemas/SandboxSubscriptionBundle'
          nullable: true
        planId:
          $ref: '#/components/schemas/SubscriptionPlanType'
        allowedUsers:
          type: integer
          format: int32
        startDate:
          type: string
          format: date-time
        expirationDate:
          type: string
          format: date-time
      additionalProperties: false
    SandboxSubscriptionBundle:
      type: object
      properties:
        walletId:
          type: string
          format: uuid
        subscriptionId:
          type: string
          format: uuid
        transactionType:
          $ref: '#/components/schemas/SubscriptionTransactionType'
        value:
          type: number
          format: double
      additionalProperties: false
    SandboxSubscriptionPlan:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/SubscriptionPlanType'
        discounts:
          type: array
          items:
            $ref: '#/components/schemas/DiscountTransaction'
          nullable: true
      additionalProperties: false
    SandboxSuccess:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    SandboxSuccessResponse:
      type: object
      properties:
        exception:
          $ref: '#/components/schemas/ResponseMessage'
        messages:
          type: array
          items:
            $ref: '#/components/schemas/ResponseMessage'
          nullable: true
        executionTime:
          type: number
          format: double
        payload:
          $ref: '#/components/schemas/SandboxSuccess'
        hasData:
          type: boolean
          readOnly: true
      additionalProperties: false
    SandboxTerms:
      type: object
      properties:
        title:
          type: string
          nullable: true
        subject:
          type: string
          nullable: true
        version:
          type: string
          nullable: true
      additionalProperties: false
    SandboxTransaction:
      type: object
      properties:
        transactionId:
          type: string
          format: uuid
        walletId:
          type: string
          format: uuid
        status:
          $ref: '#/components/schemas/TransactionStatus'
        transactionSubType:
          $ref: '#/components/schemas/TransactionSubType'
        transactionType:
          $ref: '#/components/schemas/TransactionType'
        userId:
          type: string
          nullable: true
        paymentCode:
          type: string
          nullable: true
        currency:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        creditIban:
          type: string
          nullable: true
        creditName:
          type: string
          nullable: true
        creditBankBic:
          type: string
          nullable: true
        debitIban:
          type: string
          nullable: true
        debitName:
          type: string
          nullable: true
        timestamp:
          type: string
          format: date-time
        instant:
          type: boolean
          nullable: true
        priority:
          type: integer
          format: int32
        serialNum:
          type: string
          nullable: true
        transactionDate:
          type: string
          format: date-time
          nullable: true
        branch:
          type: string
          nullable: true
        trans:
          type: string
          nullable: true
        transDescription:
          type: string
          nullable: true
        amountEquivalent:
          type: number
          format: double
        creditDebit:
          type: string
          nullable: true
        valeur:
          type: string
          format: date-time
          nullable: true
        description:
          type: string
          nullable: true
        accountingBalance:
          type: number
          format: double
        reference:
          type: string
          nullable: true
        externalSystem:
          type: string
          nullable: true
        relatedAccount:
          type: string
          nullable: true
        relatedName:
          type: string
          nullable: true
        account:
          type: string
          nullable: true
        counterpartyName:
          type: string
          nullable: true
        customerId:
          type: string
          nullable: true
        customerInfo:
          type: string
          nullable: true
        operation:
          type: string
          nullable: true
        operationDesc:
          type: string
          nullable: true
        reasonInfo:
          type: string
          nullable: true
        counterpartyBank:
          type: string
          nullable: true
        additionalInfo:
          type: string
          nullable: true
        batchId:
          type: string
          format: uuid
          nullable: true
        batchDescription:
          type: string
          nullable: true
        submittedBy:
          type: string
          nullable: true
        approvedBy:
          type: string
          nullable: true
        executedBy:
          type: string
          nullable: true
        rejectedBy:
          type: string
          nullable: true
        result:
          type: string
          nullable: true
        resultReason:
          type: string
          nullable: true
        commission:
          type: number
          format: double
          nullable: true
        requestJson:
          type: string
          nullable: true
      additionalProperties: false
    SandboxUserPermission:
      required:
        - userId
      type: object
      properties:
        id:
          type: string
          format: uuid
        walletId:
          type: string
          format: uuid
        userId:
          minLength: 1
          type: string
        submit:
          type: boolean
        approve:
          type: boolean
        balanceView:
          type: boolean
        transactionView:
          type: boolean
        inheritsAuthorizations:
          type: boolean
        admin:
          type: boolean
        limits:
          type: array
          items:
            $ref: '#/components/schemas/SandboxLimit'
          nullable: true
        creationDate:
          type: string
          format: date-time
        expirationDate:
          type: string
          format: date-time
      additionalProperties: false
    SandboxWallet:
      type: object
      properties:
        walletId:
          type: string
          format: uuid
        walletAccount:
          type: string
          nullable: true
        walletAccountCreatedAt:
          type: string
          format: date-time
          nullable: true
        organizationName:
          type: string
          nullable: true
        connectedIban:
          type: string
          nullable: true
        registrationDate:
          type: string
          format: date-time
        vatNumber:
          type: string
          nullable: true
        ownerUserId:
          type: string
          nullable: true
        walletName:
          type: string
          nullable: true
        ownerCustomerCode:
          type: string
          nullable: true
        isCorporateUser:
          type: boolean
      additionalProperties: false
    SandboxWalletBalance:
      type: object
      properties:
        walletId:
          type: string
          format: uuid
        amount:
          type: number
          format: double
        currency:
          type: string
          nullable: true
        iban:
          type: string
          nullable: true
      additionalProperties: false
    SandboxWalletPermission:
      type: object
      properties:
        id:
          type: string
          format: uuid
        walletId:
          type: string
          format: uuid
        targetWalletId:
          type: string
          format: uuid
        submit:
          type: boolean
        approve:
          type: boolean
        balanceView:
          type: boolean
        transactionView:
          type: boolean
        limits:
          type: array
          items:
            $ref: '#/components/schemas/SandboxLimit'
          nullable: true
        creationDate:
          type: string
          format: date-time
        expirationDate:
          type: string
          format: date-time
      additionalProperties: false
    SetWalletUserPermissionsRequest:
      type: object
      properties:
        tanNumber:
          type: string
          description: The sms OTP number the user has keyed in
          nullable: true
        isSmsOtp:
          type: boolean
          description: The sms OTP flag
          nullable: true
        walletId:
          type: string
          description: The wallet identifier for which permissions are being set.
          format: uuid
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/UserPermission'
          description: The list of user permissions to set for the wallet.
          nullable: true
      additionalProperties: false
      description: Represents a request to set user permissions for a specific wallet.
    SettlementInfo:
      type: object
      properties:
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        instalments:
          type: number
          format: double
        instructedDate:
          type: string
          format: date-time
          nullable: true
        method:
          $ref: '#/components/schemas/SettlementMethodEnum'
        settleType:
          $ref: '#/components/schemas/SettlementSettleTypeEnum'
        interAccount:
          type: string
          nullable: true
        srcReason:
          type: string
          nullable: true
        trgReason:
          type: string
          nullable: true
        branch:
          type: string
          nullable: true
        subBranch:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        rejectAccount:
          type: string
          nullable: true
        operationId:
          type: string
          nullable: true
        depositFlag:
          type: string
          nullable: true
        workStationId:
          type: string
          nullable: true
        commissionOperationId:
          type: string
          nullable: true
        creditCommissionAccount:
          type: string
          nullable: true
      additionalProperties: false
    SettlementMethodEnum:
      enum:
        - CASH
        - CARD
        - ACCT
        - CARDAUTH
        - VPOSCARD
        - NBGCARD
        - DBCACCT
        - LNEXACCT
        - CARDACCT
      type: string
    SettlementSettleTypeEnum:
      enum:
        - B
        - O
      type: string
    SpotMachineOrigin:
      type: object
      properties:
        networkId:
          type: string
          format: uuid
        spotId:
          type: string
          format: uuid
          nullable: true
        spotMachineId:
          type: string
          format: uuid
          nullable: true
        machineType:
          type: string
          nullable: true
        machineId:
          type: string
          nullable: true
        impersonatorId:
          type: string
          format: uuid
          nullable: true
        impersonatorMachineType:
          type: string
          nullable: true
        networkMode:
          type: string
          nullable: true
        userId:
          type: string
          nullable: true
        userType:
          type: string
          nullable: true
      additionalProperties: false
    Statement:
      type: object
      properties:
        serialNum:
          type: string
          description: The serial number of this entry.
          nullable: true
        date:
          type: string
          description: The date of this transaction.
          nullable: true
        branch:
          type: string
          description: The bank's branch.
          nullable: true
        trans:
          type: string
          description: 'Code for transaction type, e.g., "10" for "Κατάθεση".'
          nullable: true
        transDescription:
          type: string
          description: Description of the transaction type.
          nullable: true
        amount:
          type: number
          description: The amount of this transaction in the currency of the transaction.
          format: double
        currency:
          type: string
          description: The currency of the transaction.
          nullable: true
        amountEquivalent:
          type: number
          description: The amount of this transaction in the account's currency.
          format: double
        creditDebit:
          type: string
          description: Indicates whether the transaction is a "Credit" or "Debit".
          nullable: true
        valeur:
          type: string
          description: The date when the transaction amount was debited or credited.
          nullable: true
        description:
          type: string
          description: A description of this transaction.
          nullable: true
        accountingBalance:
          type: number
          description: The accounting balance after this transaction.
          format: double
        reference:
          type: string
          description: 'The reference number of the transaction, which is unique when combined with "externalSystem".'
          nullable: true
        externalSystem:
          type: string
          description: 'Indicates the system where the remittance is sent: "RNB" for NBG, "ROT" for other banks.'
          nullable: true
        relatedAccount:
          type: string
          description: 'Related account number, which varies based on whether the transaction is a debit or credit.'
          nullable: true
        relatedName:
          type: string
          description: Name of the counterparty.
          nullable: true
        timestamp:
          type: string
          description: 'The full date and time of the transaction, precise to milliseconds.'
          nullable: true
        account:
          type: string
          description: The account of the user that initiated the transaction.
          nullable: true
        counterpartyBank:
          type: string
          description: Name of the counterparty bank.
          nullable: true
        additionalInfo:
          type: string
          description: Additional information related to the transaction.
          nullable: true
      additionalProperties: false
      description: Represents a financial transaction statement.
    Structured:
      type: object
      properties:
        dueAmount:
          $ref: '#/components/schemas/DuePyblAmt'
        discountAmount:
          $ref: '#/components/schemas/DscntApldAmt'
        remittedAmount:
          $ref: '#/components/schemas/RmtdAmt'
        creditorNoteAmount:
          $ref: '#/components/schemas/CdtNoteAmt'
        taxAmount:
          $ref: '#/components/schemas/TaxAmt'
        invoiceCommissionAmount:
          $ref: '#/components/schemas/InvoiceCommissionAmt'
      additionalProperties: false
    SubscriptionBundle:
      type: object
      properties:
        transactionType:
          $ref: '#/components/schemas/SubscriptionTransactionType'
        value:
          type: number
          description: "The number of discounted transaction based on the agreed PlanId (Basic, Premium, etc.).\r\nWhen this number is consumed, the tranasction commission will default to the IB commissions."
          format: double
      additionalProperties: false
      description: SubscriptionPlan definition
    SubscriptionPlan:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/SubscriptionPlanType'
        discounts:
          type: array
          items:
            $ref: '#/components/schemas/DiscountTransaction'
          description: Defined discounts per transaction type
          nullable: true
      additionalProperties: false
      description: SubscriptionPlan definition
    SubscriptionPlanType:
      enum:
        - Trial
        - Basic
        - Premium
      type: string
    SubscriptionTransactionType:
      enum:
        - Transfer
        - Payment
      type: string
    SubscriptionUsage:
      type: object
      properties:
        subscriptionId:
          type: string
          description: Subscription Plan Id
          format: uuid
        usage:
          type: array
          items:
            $ref: '#/components/schemas/FreeTransaction'
          description: List of free transactions per transaction type with their remaining balance
          nullable: true
      additionalProperties: false
      description: SubscriptionUsage definition
    TargetWalletAuthorization:
      type: object
      properties:
        walletId:
          type: string
          description: Wallet Id
          format: uuid
        walletAccount:
          type: string
          description: Wallet account IBAN
          nullable: true
        organizationName:
          type: string
          description: Company’s Name
          nullable: true
        vatNumber:
          type: string
          description: vatNumber
          nullable: true
        walletName:
          type: string
          description: Wallet Name - By default the company’s name
          nullable: true
      additionalProperties: false
      description: ''
    TaxAmt:
      type: object
      properties:
        ccy:
          type: string
          nullable: true
        amount:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    Terms:
      type: object
      properties:
        title:
          type: string
          description: Title
          nullable: true
        subject:
          type: string
          description: Subject
          nullable: true
        version:
          type: string
          description: Subject
          nullable: true
      additionalProperties: false
      description: Terms and conditions
    TimeFrameType:
      enum:
        - DAILY
        - MONTHLY
        - YEARLY
      type: string
    Transaction:
      type: object
      properties:
        transactionId:
          type: string
          description: "The unique identifier of a transaction within the eWallet domain.\r\nThis identifier is not shared with IB or any other system of the bank."
          format: uuid
        walletId:
          type: string
          description: 'The Wallet related to the transaction, whose funds are used to execute the transaction.'
          format: uuid
        status:
          $ref: '#/components/schemas/TransactionStatus'
        transactionSubType:
          $ref: '#/components/schemas/TransactionSubType'
        transactionType:
          $ref: '#/components/schemas/TransactionType'
        currency:
          type: string
          description: Transaction currency. Only EUR is supported currently.
          nullable: true
        amount:
          type: number
          description: The amount involved in the transaction.
          format: double
        creditIban:
          type: string
          description: The IBAN to which the funds will be credited.
          nullable: true
        creditName:
          type: string
          description: The name of the recipient for the credit transaction.
          nullable: true
        debitIban:
          type: string
          description: The IBAN from which the funds will be debited.
          nullable: true
        debitName:
          type: string
          description: The name of the sender for the debit transaction.
          nullable: true
        timestamp:
          type: string
          description: Timestamp of the creation of the transaction record.
          format: date-time
        instant:
          type: boolean
          description: "Indicator for SEPA Instant transfer.\r\nCheck getTransferOptions response for instant capability."
          nullable: true
        transactionDate:
          type: string
          description: 'Date of the transaction execution. When the transaction status is Pending Approval, this property is expected to be empty.'
          format: date-time
          nullable: true
        valeur:
          type: string
          description: Date when transaction amount is debited or credited.
          format: date-time
          nullable: true
        description:
          type: string
          description: Description of this transaction.
          nullable: true
        reference:
          type: string
          description: Transaction reference number (with the "externalSystem" combination is unique).
          nullable: true
        batchId:
          type: string
          description: ID of the batch containing the transaction.
          format: uuid
          nullable: true
        batchDescription:
          type: string
          description: Description of the batch containing the transaction.
          nullable: true
        submittedBy:
          type: string
          description: The user who submitted the transaction.
          nullable: true
        approvedBy:
          type: string
          description: The user who approved the transaction.
          nullable: true
        executedBy:
          type: string
          description: The user who executed the transaction.
          nullable: true
        rejectedBy:
          type: string
          description: The user who rejected the transaction.
          nullable: true
        result:
          type: string
          description: 'The result of the transaction. Options: Success, Failed.'
          nullable: true
        resultReason:
          type: string
          description: Message from CICS regarding the result of the transaction.
          nullable: true
        branchId:
          type: string
          description: Creditor's bank branch identifier
          nullable: true
        debtorName:
          type: string
          description: Debtor's name
          nullable: true
        commission:
          type: number
          description: Transaction's Commission
          format: double
          nullable: true
      additionalProperties: false
      description: Transaction definition
    TransactionStatus:
      enum:
        - PENDING_APPROVAL
        - EXECUTED
        - REJECTED
      type: string
    TransactionSubType:
      enum:
        - WalletToWallet
        - WalletToIBAN
        - WalletToNBG
        - WalletLoad
        - WalletWithdraw
        - GenericPayment
      type: string
    TransactionType:
      enum:
        - Transfer
        - Payment
      type: string
    TransferExpensesCommissionsResponse:
      type: object
      properties:
        sumCommissionCurrency:
          type: number
          format: double
          nullable: true
        creditAmountCurrency:
          type: number
          format: double
          nullable: true
        bankTitle:
          type: string
          nullable: true
        exchangeRate:
          type: number
          format: double
        netAmount:
          type: number
          format: double
        debitNetAmount:
          type: number
          format: double
        sumCommission:
          type: number
          format: double
        debitAmount:
          type: number
          format: double
        eteCommission:
          type: number
          format: double
        deptExpenses:
          type: number
          format: double
        nonStpExpenses:
          type: number
          format: double
        urgentExpenses:
          type: number
          format: double
        onlineExpenses:
          type: number
          format: double
        exchangeProfit:
          type: number
          format: double
      additionalProperties: false
    TransferType:
      enum:
        - OWN
        - NBG
        - OTHER
        - UNKNOWN
      type: string
    TransfersRequest:
      required:
        - amount
        - beneficiaryBic
        - currency
        - debitAccount
        - reason
        - receiverAcc
        - transactionId
        - userID
      type: object
      properties:
        userID:
          minLength: 1
          type: string
        debitAccount:
          minLength: 1
          type: string
        debtorName:
          type: string
          nullable: true
        debitAccountCurrency:
          type: string
          nullable: true
        transferType:
          $ref: '#/components/schemas/TransferType'
        receiverAcc:
          minLength: 1
          type: string
        receiverAccType:
          $ref: '#/components/schemas/ReceiverAccType'
        beneficiaryBic:
          minLength: 1
          type: string
        receiverGR:
          type: string
          nullable: true
        receiverAddrGR:
          type: string
          nullable: true
        receiverCityGR:
          type: string
          nullable: true
        systType:
          type: string
          nullable: true
        systCode:
          type: string
          nullable: true
        systBic:
          type: string
          nullable: true
        systCountry:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        currency:
          minLength: 1
          type: string
        expenses:
          $ref: '#/components/schemas/ExpensesType'
        emergency:
          type: boolean
        onlineSend:
          type: boolean
          nullable: true
        instantSend:
          type: boolean
          nullable: true
        reason:
          minLength: 1
          type: string
        tanNumber:
          type: string
          nullable: true
        isSmsOtp:
          type: boolean
          nullable: true
        remType:
          type: string
          nullable: true
        remCategory:
          type: string
          nullable: true
        remImportedGood:
          $ref: '#/components/schemas/CodeDescription'
        approvalId:
          type: string
          nullable: true
        foreignExecType:
          type: string
          nullable: true
        senderRefNum:
          type: string
          nullable: true
        sellerID:
          type: string
          nullable: true
        sellerTradingName:
          type: string
          nullable: true
        sellerSubID:
          type: string
          nullable: true
        auxInfo:
          type: string
          nullable: true
        massFlag:
          type: string
          nullable: true
        destCountry:
          type: string
          nullable: true
        isSmsOtpFree:
          type: boolean
        allowDuplicate:
          type: boolean
          nullable: true
        sydipel:
          type: string
          nullable: true
        isCorporateUser:
          type: boolean
          nullable: true
        transactionId:
          type: string
          description: TransactionId provided by client
          format: uuid
      additionalProperties: false
      description: "TransferRequest definition\r\nAdd any wallet specific params here"
    TransfersRequestBase:
      required:
        - amount
        - beneficiaryBic
        - currency
        - debitAccount
        - reason
        - receiverAcc
        - userID
      type: object
      properties:
        userID:
          minLength: 1
          type: string
        debitAccount:
          minLength: 1
          type: string
        debtorName:
          type: string
          nullable: true
        debitAccountCurrency:
          type: string
          nullable: true
        transferType:
          $ref: '#/components/schemas/TransferType'
        receiverAcc:
          minLength: 1
          type: string
        receiverAccType:
          $ref: '#/components/schemas/ReceiverAccType'
        beneficiaryBic:
          minLength: 1
          type: string
        receiverGR:
          type: string
          nullable: true
        receiverAddrGR:
          type: string
          nullable: true
        receiverCityGR:
          type: string
          nullable: true
        systType:
          type: string
          nullable: true
        systCode:
          type: string
          nullable: true
        systBic:
          type: string
          nullable: true
        systCountry:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        currency:
          minLength: 1
          type: string
        expenses:
          $ref: '#/components/schemas/ExpensesType'
        emergency:
          type: boolean
        onlineSend:
          type: boolean
          nullable: true
        instantSend:
          type: boolean
          nullable: true
        reason:
          minLength: 1
          type: string
        tanNumber:
          type: string
          nullable: true
        isSmsOtp:
          type: boolean
          nullable: true
        remType:
          type: string
          nullable: true
        remCategory:
          type: string
          nullable: true
        remImportedGood:
          $ref: '#/components/schemas/CodeDescription'
        approvalId:
          type: string
          nullable: true
        foreignExecType:
          type: string
          nullable: true
        senderRefNum:
          type: string
          nullable: true
        sellerID:
          type: string
          nullable: true
        sellerTradingName:
          type: string
          nullable: true
        sellerSubID:
          type: string
          nullable: true
        auxInfo:
          type: string
          nullable: true
        massFlag:
          type: string
          nullable: true
        destCountry:
          type: string
          nullable: true
        isSmsOtpFree:
          type: boolean
        allowDuplicate:
          type: boolean
          nullable: true
        sydipel:
          type: string
          nullable: true
        isCorporateUser:
          type: boolean
          nullable: true
      additionalProperties: false
    TransfersResponse:
      type: object
      properties:
        transactionId:
          type: string
          description: Transaction Id
          format: uuid
        remType:
          type: string
          description: "Τύπος συναλλαγής ROT / RNB. Χρησιμοποιείται κατά την κλήση της remittancequery\r\n            If equals to \"RNB\" then the remittance is to NBG.If equals to \"ROT\" then the remittance is to other bank"
          nullable: true
        debitAccount:
          type: string
          description: The NBG account number (e.g. 11 digits) to debit (transfer from)
          nullable: true
        currency:
          type: string
          description: 'Currency, 3-letter code, e.g. ```EUR```.'
          nullable: true
        transferAmount:
          type: number
          description: Transfered amount.
          format: double
        availableBalance:
          type: number
          description: Available balance
          format: double
        ledgerBalance:
          type: number
          description: Ledger balance
          format: double
        referenceNumber:
          type: string
          description: Reference number in *********** form
          nullable: true
        bankTitle:
          type: string
          description: Full bank title
          nullable: true
        debtorName:
          type: string
          description: Νame of the debit's account beneficiary
          nullable: true
        refNo:
          type: string
          description: "Reference number in IFTI***********1 form\r\n            Επιστρέφεται μόνο κατά την εκτέλεση εμβάσματος (εκτός Εθνικής)"
          nullable: true
        valeur:
          type: string
          description: Date when transaction amount debited or credited
          format: date-time
          nullable: true
        exchangeRateOut:
          type: number
          format: double
        netAmountOut:
          type: number
          description: Transaction amount
          format: double
        sumCommissionOut:
          type: number
          description: Total comission amount
          format: double
        debitAmountOut:
          type: number
          description: Sum of transaction and total commision amounts
          format: double
        eteCommissionOut:
          type: number
          format: double
        deptExpensesOut:
          type: number
          format: double
        nonStpExpensesOut:
          type: number
          format: double
        urgentExpensesOut:
          type: number
          format: double
        onlineExpensesOut:
          type: number
          format: double
        exchangeProfitOut:
          type: number
          format: double
        beneficiaries:
          type: array
          items:
            type: string
          description: Credit account beneficiaries (as of now - 2014/03/24 - applies only to transfers inside NBG)
          nullable: true
        requiresApproval:
          type: boolean
          description: If ```true``` this transaction is deferred for approval.
        tanCheck:
          type: string
          description: The confirmation code of the tanNumber
          nullable: true
        transactionDate:
          type: string
          description: 'The exact timestamp of this transaction, as the server perceived it.'
          format: date-time
          nullable: true
        debtorIBAN:
          type: string
          description: Debit's account IBAN
          nullable: true
        isDuplicate:
          type: boolean
          description: True if transaction is duplicate
          nullable: true
        errorReason:
          type: string
          description: Error reason
          nullable: true
      additionalProperties: false
      description: "TransfersResponse definition\r\nAdd any wallet specific params here"
    UltimateDebtor:
      type: object
      properties:
        name:
          type: string
          nullable: true
        customerNumber:
          type: string
          nullable: true
        telephone:
          type: string
          nullable: true
        addressLine:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        account:
          $ref: '#/components/schemas/UltimateDebtorAccount'
        extraIdentities:
          type: array
          items:
            $ref: '#/components/schemas/EntityIdentity'
          nullable: true
      additionalProperties: false
    UltimateDebtorAccount:
      type: object
      properties:
        iban:
          type: string
          nullable: true
        pan:
          type: string
          nullable: true
        interAccount:
          type: string
          nullable: true
        instaAccount:
          type: string
          nullable: true
        agentMedAccountCom:
          type: string
          nullable: true
        agentMedAccount:
          type: string
          nullable: true
        subagentMedAccountCom:
          type: string
          nullable: true
        subagentMedAccount:
          type: string
          nullable: true
        rejectionAccount:
          type: string
          nullable: true
      additionalProperties: false
    UnStructured:
      type: object
      properties:
        line:
          type: string
          nullable: true
      additionalProperties: false
    UserPermission:
      required:
        - userId
      type: object
      properties:
        id:
          type: string
          description: The permission unique identifier.
          format: uuid
        walletId:
          type: string
          description: The Wallet Id of the wallet that this permission applies to.
          format: uuid
        userId:
          minLength: 1
          type: string
          description: The IB User ID of the user that the permission is assigned to.
        submit:
          type: boolean
          description: This permissions allows the user to submit a transaction for approval.
        approve:
          type: boolean
          description: "This permission allows the user to execute a tranasction without approval,\r\nor approve a transaction that is pending approval.\r\nNO, LIMIT, NO_CONSTRAINT"
        balanceView:
          type: boolean
          description: This permission allows the user to view the remaining balance of the wallet account.
        transactionView:
          type: boolean
          description: This permission allows the user to view the transactions or statements of the wallet account.
        inheritsAuthorizations:
          type: boolean
          description: This permission allows the corporate user to inherit the authorizations provided to the wallet they belong to.
        admin:
          type: boolean
          description: Administration permission assigned only to the wallet creator/owner.
        limits:
          type: array
          items:
            $ref: '#/components/schemas/Limit'
          description: 'Limits per transaction type that apply to the Submit, or Approve permission.'
          nullable: true
        creationDate:
          type: string
          description: Creation datetime of the permission. It is also the assignment date.
          format: date-time
        expirationDate:
          type: string
          description: "Datetime of the expiration of the permissions.\r\nIt is also the revocation datetime, if permissions are explicitly revoked by the admin."
          format: date-time
      additionalProperties: false
      description: The User Permissions assigned to the User Id for the connected wallet.
    UserPermissionResponse:
      type: object
      properties:
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/UserPermission'
          description: List of User Permissions
          nullable: true
      additionalProperties: false
      description: User Permssions Request Wrapper
    Wallet:
      type: object
      properties:
        walletId:
          type: string
          description: The unique identifier for the wallet.
          format: uuid
        walletAccount:
          type: string
          description: The IBAN of the secial purpose wallet account.
          nullable: true
        walletAccountCreatedAt:
          type: string
          description: The date and time when the wallet account was created.
          format: date-time
          nullable: true
        organizationName:
          type: string
          description: The name of the company associated with the wallet as it appears in IB.
          nullable: true
        connectedIban:
          type: string
          description: 'The connected IBAN, reserved for future use and should be ignored for now.'
          nullable: true
        registrationDate:
          type: string
          description: The date when the wallet was registered.
          format: date-time
        vatNumber:
          type: string
          description: "The VAT number associated with the wallet.\r\nIt can be and FP or NP VAT number.\r\nIt is unique across all wallets, since only one wallet per business entity can exist."
          nullable: true
        ownerUserId:
          type: string
          description: "The user ID of the wallet owner.\r\nThe owner of the wallet has always ADMIN rights."
          nullable: true
        walletName:
          type: string
          description: The name of the wallet. It is set by the wallet creator during registration and it is not unqiue across wallets.
          nullable: true
        ownerCustomerCode:
          type: string
          description: ''
          nullable: true
        isCorporateUser:
          type: boolean
          description: ''
      additionalProperties: false
      description: Represents a wallet in the eWallet system.
    WalletAuthorizationRequest:
      type: object
      properties:
        tanNumber:
          type: string
          description: The sms OTP number the user has keyed in
          nullable: true
        isSmsOtp:
          type: boolean
          description: The sms OTP flag
          nullable: true
        targetWalletId:
          type: string
          description: Id of the wallet requesting authorization for
          format: uuid
      additionalProperties: false
      description: Wallet Authorization Request
    WalletAuthorizationResponse:
      type: object
      properties:
        id:
          type: string
          description: Id
          format: uuid
        createdAt:
          type: string
          description: Datetime of request creation
          format: date-time
        updatedAt:
          type: string
          description: Datetime of request creation
          format: date-time
          nullable: true
        expiresAt:
          type: string
          description: Expiration Datetime of the authorization
          format: date-time
        status:
          $ref: '#/components/schemas/RequestStatus'
        requestorWallet:
          $ref: '#/components/schemas/RequestorWalletAuthorization'
        targetWallet:
          $ref: '#/components/schemas/TargetWalletAuthorization'
      additionalProperties: false
      description: Wallet Statements Response
    WalletLite:
      type: object
      properties:
        walletId:
          type: string
          description: The wallet Id
          format: uuid
        walletAccount:
          type: string
          description: Wallet account IBAN
          nullable: true
        organizationName:
          type: string
          description: Company’s Name
          nullable: true
        vatNumber:
          type: string
          description: VAT number of company or individual connected with the wallet.
          nullable: true
        walletName:
          type: string
          description: Wallet Name - By default the company’s name
          nullable: true
      additionalProperties: false
      description: Represents a search wallet result. Includes basic information of the Wallet entity.
    WalletLoadRequest:
      type: object
      properties:
        tanNumber:
          type: string
          description: The sms OTP number the user has keyed in
          nullable: true
        isSmsOtp:
          type: boolean
          description: The sms OTP flag
          nullable: true
        walletId:
          type: string
          description: wallet Id
          format: uuid
        amount:
          type: number
          description: amount
          format: double
        currency:
          type: string
          description: currency
          nullable: true
        iban:
          type: string
          description: currency
          nullable: true
      additionalProperties: false
      description: Wallet Load Request
    WalletLoadResponse:
      type: object
      properties:
        balance:
          type: number
          description: Account Balance after loading
          format: double
          nullable: true
        transaction:
          $ref: '#/components/schemas/Transaction'
      additionalProperties: false
      description: Wallet Load Response
    WalletPermission:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier for the permission.
          format: uuid
        walletId:
          type: string
          description: The identifier of the wallet to which these permissions apply.
          format: uuid
        targetWalletId:
          type: string
          description: The identifier of the target wallet granted these permissions.
          format: uuid
        submit:
          type: boolean
          description: Indicates if the permission to submit is granted.
        approve:
          type: boolean
          description: Type of approval granted.
        balanceView:
          type: boolean
          description: Indicates if the permission to view balances is granted.
        transactionView:
          type: boolean
          description: Indicates if the permission to view transactions is granted.
        limits:
          type: array
          items:
            $ref: '#/components/schemas/Limit'
          description: A list of transaction limits associated with this permission.
          nullable: true
        creationDate:
          type: string
          description: The date when the permissions were created.
          format: date-time
        expirationDate:
          type: string
          description: The date when the permissions expire.
          format: date-time
      additionalProperties: false
      description: Represents the permissions granted to a wallet for operations by another wallet.
    WalletPermissionResponse:
      type: object
      properties:
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/WalletPermission'
          description: A list of permissions associated with the wallet.
          nullable: true
      additionalProperties: false
      description: Represents the response containing permissions of external wallets.
    WalletPermissionsRequest:
      type: object
      properties:
        tanNumber:
          type: string
          description: The sms OTP number the user has keyed in
          nullable: true
        isSmsOtp:
          type: boolean
          description: The sms OTP flag
          nullable: true
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/WalletPermission'
          description: A list of permissions to be granted.
          nullable: true
      additionalProperties: false
      description: Represents a request to grant permissions to other wallets.
    WalletRegister:
      type: object
      properties:
        walletName:
          type: string
          description: "Wallet name to be assigned to the newly created wallet.\r\nCan be used to be searched by other wallet user."
          nullable: true
      additionalProperties: false
      description: Wallet Register Request
    WalletStatementsResponse:
      type: object
      properties:
        statements:
          type: array
          items:
            $ref: '#/components/schemas/Statement'
          description: List of Wallet Statements
          nullable: true
        paginationToken:
          type: string
          description: Pagination Token
          nullable: true
      additionalProperties: false
      description: Wallet Statements Response
  securitySchemes:
    Client-Id:
      type: apiKey
      description: Application's Id
      name: Client-Id
      in: header
    Authorization-Code-Token:
      type: oauth2
      description: OAuth2 Authorization Code Flow
      flows:
        authorizationCode:
          authorizationUrl: https://my.nbg.gr/identity/connect/authorize
          tokenUrl: https://my.nbg.gr/identity/connect/token
          scopes:
            sandbox-ewallet-api-v1 offline_access: Sandbox Scope
            e-wallet-api-v1 offline_access: Production Scope
    Client-Credentials-Token:
      type: oauth2
      description: OAuth2 Client Credentials Flow
      flows:
        clientCredentials:
          tokenUrl: https://my.nbg.gr/identity/connect/token
          scopes:
            sandbox-ewallet-api-v1: Sandbox Scope
            e-wallet-api-v1: Production Scope