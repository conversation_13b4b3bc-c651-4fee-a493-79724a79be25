﻿using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Types.Subscriptions;

/// <summary>
/// FreeTransaction definition
/// </summary>
public class FreeTransaction
{
    /// <summary>
    /// Type of transaction
    /// </summary>
    [DataMember(Name = "transactionType")]
    public SubscriptionTransactionType TransactionType { get; set; }

    /// <summary>
    /// value
    /// </summary>
    [DataMember(Name = "value")]
    public int Value { get; set; }
}

/// <summary>
/// DiscountTransaction definition
/// </summary>
public class DiscountTransaction
{
    /// <summary>
    /// Type of transaction
    /// </summary>
    [DataMember(Name = "transactionType")]
    public SubscriptionTransactionType TransactionType { get; set; }

    /// <summary>
    /// Value
    /// </summary>
    [DataMember(Name = "value")]
    public decimal Value { get; set; }

}

/// <summary>
/// Defined DiscountTypes
/// </summary>
public enum DiscountType
{
    /// <summary>
    /// Percentage
    /// </summary>
    Percentage,
    /// <summary>
    /// FixedPrice
    /// </summary>
    FixedPrice
}
