﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Accounts response
/// </summary>
[DataContract]
public class AccountsResponse
{
    [DataMember(Name = "isActive")]
    public bool? IsActive { get; set; }

    [DataMember(Name = "hasMissingInformation")]
    public bool? HasMissingInformation { get; set; }

    [DataMember(Name = "isFPbutNotBusiness")]
    public bool? IsFPbutNotBusiness { get; set; }

    [DataMember(Name = "accounts")]
    public List<Account> Accounts { get; set; }

    [DataMember(Name = "validationControls")]
    public List<string> ValidationControls { get; set; }

    [DataMember(Name = "existingIssuer")]
    public bool ExistingIssuer { get; set; }

    [DataMember(Name = "activatedIssuer")]
    public bool ActivatedIssuer { get; set; }

    [DataMember(Name = "isCorporate")]
    public bool? IsCorporate { get; set; }
}
