﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Transfers;

public class BatchTransfersForecastResponse
{
    [DataMember(Name = "batchId")]
    public Guid BatchId { get; set; }

    public static BatchTransfersForecastResponse ForecatsResponse(BatchTransfersResponse response)
    {
        var forecastResponse = new BatchTransfersForecastResponse
        {
            BatchId = response.BatchId
        };

        return forecastResponse;
    }
}
