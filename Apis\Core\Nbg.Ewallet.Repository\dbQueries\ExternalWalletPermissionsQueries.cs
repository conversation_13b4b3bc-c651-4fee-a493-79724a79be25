﻿namespace Nbg.Ewallet.Repository.dbQueries;

internal class ExternalWalletPermissionsQueries
{
    internal const string InsertExternalWalletPermission =
        "INSERT INTO [EWallet].[dbo].[WalletPermissions] " +
        "(Id, WalletId,ExternalWalletId,Submit,Approve,BalanceView,TransactionView,ExpirationDate,CreationDate,AssignedBy) " +
        "VALUES (@Id, @WalletId,@TargetWalletId,@submit,@approve,@balanceView,@transactionView,@expirationDate,@CreatedAt,@assignedBy)";

    internal const string ExpireExternalWalletPermission =
        "UPDATE [EWallet].[dbo].[WalletPermissions] " +
        "SET ExpirationDate = @ExpirationDate " +
        "WHERE Id = @ID";

    internal const string GetExistingActiveExternalWalletPermission =
        "SELECT [Id],[WalletId],[ExternalWalletId]AS TargetWalletId,[Submit],[BalanceView],[TransactionView],[ExpirationDate]," +
        "[CreationDate] AS CreatedAt,[AssignedBy],[RevokedBy],[Approve] " +
        "FROM [EWallet].[dbo].[WalletPermissions] " +
        "WHERE ExternalWalletId = @ExternalWalletId " +
        "AND WalletId = @walletid " +
        "AND ExpirationDate > @CurrentDate";

    internal const string UpdateExternalWalletPermission =
        "UPDATE [EWallet].[dbo].[WalletPermissions] " +
        "SET Approve = @Approve, BalanceView = @BalanceView, TransactionView = @TransactionView, ExpirationDate = @ExpirationDate, " +
        "RevokedBy = @RevokedBy " +
        "WHERE Id = @Id";

    internal const string GetActiveWalletPermissions =
        "SELECT [Id],[WalletId],[ExternalWalletId] AS TargetWalletId,[Submit],[BalanceView],[TransactionView],[ExpirationDate]," +
        "[CreationDate] AS CreatedAt,[AssignedBy],[RevokedBy],[Approve] " +
        "FROM [EWallet].[dbo].[WalletPermissions] " +
        "WHERE WalletId = @walletId " +
        "AND ExpirationDate > GETDATE() " +
        "AND RevokedBy IS NULL";
    //    @"SELECT * FROM [EWallet].[dbo].[WalletPermissions] WHERE WalletId = @walletId AND ExpirationDate > GETDATE() AND RevokedBy IS NULL";

    internal const string GetActiveWalletPermissionsForSpecificWallet =
        GetActiveWalletPermissions +
        " AND ExternalWalletId = @externalWalletId";

    internal const string GetAllExpiredWalletPermissions =
        "SELECT [Id],[WalletId],[ExternalWalletId] AS TargetWalletId,[Submit],[BalanceView],[TransactionView],[ExpirationDate]," +
        "[CreationDate] AS CreatedAt,[AssignedBy],[RevokedBy],[Approve] " +
        "FROM [EWallet].[dbo].[WalletPermissions] " +
        "WHERE WalletId = @walletId " +
        "AND ExpirationDate < GETDATE()";

    internal const string GetAllExpiredWalletPermissionsForSpecificWallet =
        GetAllExpiredWalletPermissions +
        " AND ExternalWalletId = @externalWalletId";

    internal const string GetCurrentActiveExternalWalletPermission =
        "SELECT [Id],[WalletId],[ExternalWalletId] AS TargetWalletId,[Submit],[BalanceView],[TransactionView],[ExpirationDate]," +
        "[CreationDate] AS CreatedAt,[AssignedBy],[RevokedBy],[Approve] " +
        "FROM [EWallet].[dbo].[WalletPermissions] " +
        "WHERE ExternalWalletId = @ExternalWalletId " +
        "AND WalletId = @walletid " +
        "AND RevokedBy IS NULL " +
        "AND ExpirationDate > @CurrentDate " +
        "ORDER BY CreationDate DESC";

    internal const string GetLimitsForWalletIdAsync =
        "SELECT [Id],[WalletId],[ExternalWalletId] AS TargetWalletId,[Submit],[BalanceView],[TransactionView],[ExpirationDate]," +
        "[CreationDate] AS CreatedAt,[AssignedBy],[RevokedBy],[Approve] " +
        "FROM [EWallet].[dbo].[WalletPermissions] " +
        "WHERE ExternalWalletId = @ExternalWalletId " +
        "AND WalletId = @walletid " +
        "AND RevokedBy IS NULL " +
        "AND ExpirationDate > @CurrentDate " +
        "ORDER BY CreationDate DESC";
}
