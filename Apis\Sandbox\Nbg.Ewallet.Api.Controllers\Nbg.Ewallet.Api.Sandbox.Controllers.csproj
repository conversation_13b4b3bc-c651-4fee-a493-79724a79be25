﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>true</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DocumentationFile>Nbg.Ewallet.Api.Sandbox.Controllers.xml</DocumentationFile>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
        <!--<PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />-->
    </ItemGroup>
    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\Core\Nbg.Ewallet.Api.Core.Types\Nbg.Ewallet.Api.Types.csproj" />
        <ProjectReference Include="..\Nbg.Ewallet.Api.Sandbox.Implementation\Nbg.Ewallet.Api.Sandbox.Implementation.csproj" />
    </ItemGroup>

</Project>