﻿using System;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface IHttpContextRepositoryService
{
    string GetCustomerCode();

    string GetUserId();

    int GetNumberOfApprovals();

    string GetAuthorizationLevel();

    string GetCompanyName();

    Guid GetClientId();

    string? GetConsentId();

    string? GetSub();

    public string? GetCallbackUrl();

    public string? GetSandboxId();
}
