using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// This Controller Provides Wallet Transaction functionality.
/// </summary>
[ApiController]
[ApiExplorerSettings(GroupName = "Wallet")]
[Route("wallet")]
[Produces("application/json")]
[Consumes("application/json")]
public class WalletTransactionController : ControllerBase
{
    private readonly ITransactionService _transactionService;

    public WalletTransactionController(ITransactionService transactionService)
    {
        _transactionService = transactionService;
    }

    /// <summary>
    /// Approves a transaction for a wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="transactionId">The transaction identifier.</param>
    /// <returns>The approved transaction.</returns>
    /// <response code="200">Transaction approved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(Transaction), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPut]
    [Route("{walletId}/transactions/{transactionId}/approve", Name = "approvetransaction")]
    [EwalletAuthorize(AuthorizationTypes.ApproveTransaction)]
    public async Task<ActionResult<Transaction>> ApproveTransaction(Guid walletId, string transactionId)
    {
        return await _transactionService.ApproveAsync(walletId, transactionId);
    }

    /// <summary>
    /// Rejects a transaction for a wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="transactionId">The transaction identifier.</param>
    /// <returns>The rejected transaction.</returns>
    /// <response code="200">Transaction rejected successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(Transaction), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPut]
    [Route("{walletId}/transactions/{transactionId}/reject", Name = "rejecttransaction")]
    [EwalletAuthorize(AuthorizationTypes.ApproveTransaction)]
    public async Task<ActionResult<Transaction>> RejectTransaction(Guid walletId, string transactionId)
    {
        return await _transactionService.RejectAsync(walletId, transactionId);
    }
}
