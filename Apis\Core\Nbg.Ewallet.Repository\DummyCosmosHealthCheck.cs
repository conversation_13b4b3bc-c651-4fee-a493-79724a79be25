﻿using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Nbg.Ewallet.Repository;

/// <summary>
/// Dummy health check for Cosmos connector.
/// Always returns Healthy status.
/// </summary>
public class DummyCosmosHealthCheck : IHealthCheck
{
    /// <inheritdoc />
    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(HealthCheckResult.Healthy("Just shows that Cosmos connection is selected"));
    }
}
