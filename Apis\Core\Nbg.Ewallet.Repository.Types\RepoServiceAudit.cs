﻿using System;

namespace Nbg.Ewallet.Repository.Types;

public class ServiceAudit
{
    public Guid Id { get; set; }

    public DateTime Timestamp { get; set; }

    public string Host { get; set; }

    public string ServiceName { get; set; }

    public Guid Application { get; set; }

    public string ClientRequestPath { get; set; }

    public string ClientSession { get; set; }

    public string RequestTextData { get; set; }

    public string ResponseTextData { get; set; }

    public string ErrorData { get; set; }

    public TimeSpan ExecutionTime { get; set; }

    public DateTime? EndTime { get; set; }

    public string ExtraTextData { get; set; }
}
