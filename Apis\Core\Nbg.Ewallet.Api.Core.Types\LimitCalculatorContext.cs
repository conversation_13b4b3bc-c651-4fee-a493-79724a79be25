﻿using System;
using System.Collections.Generic;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Implementation;

public class LimitCalculatorContext
{
    public LimitCalculatorContext()
    {
    }

    public LimitCalculatorContext(Guid walletId, TransactionSubType transactionSubType, decimal amount)
    {
        WalletId = walletId;
        TransactionSubTypeGroups = [
            new TransactionSubTypeGroup {
                TransactionSubType = transactionSubType,
                Amount = amount
            }
        ];
    }

    public Guid WalletId { get; set; }
    public List<TransactionSubTypeGroup> TransactionSubTypeGroups { get; set; }
}

public class TransactionSubTypeGroup
{
    public TransactionSubType TransactionSubType { get; set; }
    public decimal Amount { get; set; }
}
