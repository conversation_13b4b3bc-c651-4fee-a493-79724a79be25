﻿using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Types.Configuration;

[AttributeUsage(AttributeTargets.Method)]
public sealed class EwalletAuthorizeAttribute : TypeFilterAttribute
{
    public EwalletAuthorizeAttribute(params AuthorizationTypes[] roles)
        : base(typeof(EwalletAuthorizeFilter))
    {
        Arguments = new object[] { roles };
    }
}

public class EwalletAuthorizeFilter : IAuthorizationFilter
{
    private readonly AuthorizationTypes[] _requiredPermissions;
    private readonly IOptions<RepositorySettings> _options;

    public EwalletAuthorizeFilter(
        IOptions<RepositorySettings> options,
        AuthorizationTypes[] roles)
    {
        _requiredPermissions = roles;
        _options = options;
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        //Check if in appSettings permission check is disabled
        if (_options.Value.DisablePermissions) return;

        var authContext = (AuthorizationContext)context.HttpContext.Items["AuthContext"];
        foreach (var permission in _requiredPermissions)
        {
            var rule = AuthorizationRuleCache.GetRule(permission);
            rule.EnsureAuthorized(authContext, context);
        }
    }
}
