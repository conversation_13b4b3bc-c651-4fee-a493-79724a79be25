﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <SignAssembly>True</SignAssembly>
    <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
    <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile>Nbg.Ewallet.Api.Controllers.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Sandbox|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CBSQA|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Nbg.AspNetCore.Common.Security" Version="8.0.0" />
    <PackageReference Include="Nbg.AspNetCore.ExceptionHandling" Version="8.0.0" />
    <!--<PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />-->
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Nbg.Ewallet.Api.Interfaces\Nbg.Ewallet.Api.Interfaces.csproj" />
    <ProjectReference Include="..\Nbg.Ewallet.Repository.Interfaces\Nbg.Ewallet.Repository.Interfaces.csproj" />
    <!--<ProjectReference Include="..\Nbg.Ewallet.Repository\Nbg.Ewallet.Repository.csproj" />-->
  </ItemGroup>
</Project>