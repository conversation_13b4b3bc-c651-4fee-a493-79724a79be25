﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IWalletService
{
    Task<GetWalletBalanceResponse> GetWalletBalance(Guid walletΙd);
    Task<List<WalletLite>> SearchWallets(string vatNumber, string walletName, string iban, string organizationName);
    Task<Wallet> GetWalletInformation(Guid walletId);
    Task<Wallet> EditWalletName(Guid walletId, EditWalletNameRequest request);
    Task<WalletLoadResponse> Load(Guid walletId, WalletLoadRequest request);
    Task<WalletLoadResponse> Unload(Guid walletId, WalletLoadRequest request);
    Task<WalletStatementsResponse> GetWalletStatements(Guid walletId, WalletStatementsRequest request);
    Task<List<Transaction>> GetWalletTransactions(Guid walletId, TransactionRequestQueryParams request);
    //Task<Transaction> ApproveTransaction(Guid walletID, string transactionId);
    //Task<Transaction> RejectTransaction(Guid walletID, string transactionId);
    Task<List<Transaction>> GetMyTransactionsAsync(Guid walletId);
    Task<byte[]> GetWalletStatementPdfExport(Guid walletId, StatementRequestQueryParams request);
    Task<AvailableAccountsResponse> GetUserAccountsAsync(Guid walletId);
}
