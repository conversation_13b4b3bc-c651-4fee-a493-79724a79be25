﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

public class ConfigureB2bRulesRequest
{

    /// <summary>
    /// name
    /// </summary>
    [DataMember(Name = "Rules")]
    public IList<Rule> Rules { get; set; }

    /// <summary>
    /// owner
    /// </summary>
    [DataMember(Name = "rulesType")]
    public Guid rulesType { get; set; } = Guid.Parse("B7EF609D-C059-48C2-AFC6-96CBBB07F930");
}
