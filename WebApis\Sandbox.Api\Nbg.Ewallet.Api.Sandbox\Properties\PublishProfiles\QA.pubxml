﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <DeleteExistingFiles>true</DeleteExistingFiles>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <LastUsedBuildConfiguration>QA</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <EnvironmentName>QA</EnvironmentName>
    <PublishProvider>FileSystem</PublishProvider>
    <PublishUrl>C:\work\Publish\qa\Nbg.Ewallet.Api.Sandbox</PublishUrl>
    <WebPublishMethod>FileSystem</WebPublishMethod>
    <_TargetId>Folder</_TargetId>
    <SiteUrlToLaunchAfterPublish />
    <TargetFramework>net8.0</TargetFramework>
    <ProjectGuid>426732f0-a529-4ceb-aadd-002f5b9d5098</ProjectGuid>
    <SelfContained>false</SelfContained>
  </PropertyGroup>
  <ItemGroup>
    <Content Update="appsettings.*.json" CopyToPublishDirectory="Never" />
    <ResolvedFileToPublish Include="appsettings.QA.json">
      <RelativePath>appsettings.QA.json</RelativePath>
    </ResolvedFileToPublish>
  </ItemGroup>
</Project>
