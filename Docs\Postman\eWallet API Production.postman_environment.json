{"id": "4a1fa218-66c2-4a3f-947b-cc88da1c39be", "name": "eWallet API Production", "values": [{"key": "access-token-url", "value": "https://my.nbg.gr/identity/connect/token", "type": "default", "enabled": true}, {"key": "auth-url", "value": "https://my.nbg.gr/identity/connect/authorize", "type": "default", "enabled": true}, {"key": "callback-url", "value": "https://www.getpostman.com/oauth2/callback/", "type": "default", "enabled": true}, {"key": "client-id", "value": "8482BBE9-5FFF-48AF-92B4-A378B1F45C5F", "type": "default", "enabled": true}, {"key": "client-secret", "value": "CD868C76-A4C6-48E3-8073-35A1B05B38D7", "type": "default", "enabled": true}, {"key": "scope", "value": "sandbox-ewallet-api-v1 sandbox-i-bank-erp-api-v2 sandbox-i-bank-erp-api-v1-2 sandbox-i-bank-erp-account-api-v2 sandbox-i-bank-erp-transfer-api-v2 sandbox-i-bank-erp-card-api-v2", "type": "default", "enabled": true}, {"key": "url_https", "value": "https://apis.nbg.gr/sandbox/ewallet/oauth2/v1", "type": "default", "enabled": true}, {"key": "sandboxId", "value": "9784b955-95b8-4239-bf6b-065735bbf855", "type": "any", "enabled": true}, {"key": "sandboxwalletId", "value": "", "type": "any", "enabled": true}, {"key": "walletId", "value": "", "type": "any", "enabled": true}, {"key": "userId", "value": "", "type": "any", "enabled": true}, {"key": "subscriptionplanId", "value": "", "type": "any", "enabled": true}, {"key": "walletAccount", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid0", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid1", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid2", "value": "", "type": "any", "enabled": true}, {"key": "userPermission0", "value": "", "type": "any", "enabled": true}, {"key": "userPermission1", "value": "", "type": "any", "enabled": true}, {"key": "userPermission2", "value": "", "type": "any", "enabled": true}, {"key": "authorizationRequestId", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId0", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId1", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId2", "value": "", "type": "any", "enabled": true}, {"key": "paginationToken", "value": "", "type": "any", "enabled": true}, {"key": "batchId", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId3", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId4", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId5", "value": "", "type": "any", "enabled": true}, {"key": "wallet0", "value": "", "type": "any", "enabled": true}, {"key": "wallet1", "value": "", "type": "any", "enabled": true}, {"key": "wallet2", "value": "", "type": "any", "enabled": true}, {"key": "wallet3", "value": "", "type": "any", "enabled": true}, {"key": "wallet4", "value": "", "type": "any", "enabled": true}, {"key": "wallet5", "value": "", "type": "any", "enabled": true}, {"key": "wallet6", "value": "", "type": "any", "enabled": true}, {"key": "walletIdIban", "value": "", "type": "any", "enabled": true}, {"key": "wallet7", "value": "", "type": "any", "enabled": true}, {"key": "consentid", "value": "", "type": "any", "enabled": true}, {"key": "wallet8", "value": "", "type": "any", "enabled": true}, {"key": "wallet9", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-07-10T10:59:37.553Z", "_postman_exported_using": "Postman/11.2.27"}