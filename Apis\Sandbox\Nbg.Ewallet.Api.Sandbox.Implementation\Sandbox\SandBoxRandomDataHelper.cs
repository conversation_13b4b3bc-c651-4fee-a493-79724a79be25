﻿#pragma warning disable CA5394 // Do not use insecure randomness
using System;
using System.Text;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public static class SandBoxRandomDataHelper
{
    private const string _alphabet = "AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz";
    private static readonly int _alphabetLength = _alphabet.Length;
    private static readonly Random _random = new((int)DateTime.UtcNow.Ticks);

    public static decimal GenerateRandomDecimal(decimal min = 0, decimal max = 1000)
    {
        var range = max - min;
        var randomValue = (decimal)_random.NextDouble() * range + min;
        return Math.Round(randomValue, 2);
    }

    public static string GenerateRandomNumber(int length = 100)
    {
        StringBuilder result = new();
        for (var i = 0; i < length; i++)
        {
            result.Append(_random.Next(0, 9));
        }
        return result.ToString();
    }

    public static string GenerateVatNumber()
    {
        var vatNumber = GenerateRandomNumber(9);
        return $"EL{vatNumber}";
    }

    public static string GenerateIBAN()
    {
        var IBAN = GenerateRandomNumber(20);
        var firstTwoDigits = GenerateRandomNumber(2);
        return $"GR{firstTwoDigits}011{IBAN}";
    }

    public static string GenerateAvailableUserName()
    {
        var availableuser = "USER" + GenerateRandomNumber(4); // defaults to 3 characters
        return availableuser;
    }

    public static string GenerateRandomText(int length = 3)
    {
        var result = new StringBuilder(length);

        for (var n = 0; n < length; n++)
        {
            result.Append(_alphabet[_random.Next(0, _alphabetLength)]);
        }
        return result.ToString();
    }

    internal static int GenerateRandomNumber(int from, int to)
    {
        // Ensure the 'from' value is less than the 'to' value
        ArgumentOutOfRangeException.ThrowIfGreaterThanOrEqual(from, to);

        // Generate a random number between 'from' and 'to'

        return _random.Next(from, to);
    }
}
