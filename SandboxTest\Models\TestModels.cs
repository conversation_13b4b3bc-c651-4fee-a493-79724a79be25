using System.Text.Json.Serialization;

namespace SandboxTest.Models
{
    // Response models for deserialization
    public class WalletResponse
    {
        public Guid WalletId { get; set; }
        public string WalletName { get; set; } = string.Empty;
        public string WalletAccount { get; set; } = string.Empty;
        public string OrganizationName { get; set; } = string.Empty;
        public string RegistrationDate { get; set; } = string.Empty;
        public string VatNumber { get; set; } = string.Empty;
        public string OwnerUserId { get; set; } = string.Empty;
    }

    public class WalletBalanceResponse
    {
        public decimal Balance { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; }
    }

    public class WalletSearchResponse
    {
        public List<WalletLiteResponse> Wallets { get; set; } = new();
    }

    public class WalletLiteResponse
    {
        public Guid WalletId { get; set; }
        public string WalletName { get; set; } = string.Empty;
        public string OrganizationName { get; set; } = string.Empty;
        public string VatNumber { get; set; } = string.Empty;
    }

    public class UserPermissionResponse
    {
        public List<UserPermission> UserPermissions { get; set; } = new();
    }

    public class UserPermission
    {
        public Guid Id { get; set; }
        public bool Admin { get; set; }
        public bool Approve { get; set; }
        public bool BalanceView { get; set; }
        public bool Submit { get; set; }
        public bool TransactionView { get; set; }
        public bool InheritsAuthorizations { get; set; }
        public Guid WalletId { get; set; }
        public string UserId { get; set; } = string.Empty;
        public List<LimitResponse> Limits { get; set; } = new();
        public DateTime CreationDate { get; set; }
        public DateTime ExpirationDate { get; set; }
    }

    public class LimitResponse
    {
        public Guid Id { get; set; }
        public decimal Amount { get; set; }
        public string Timeframe { get; set; } = string.Empty;
        public string TransactionType { get; set; } = string.Empty;
        public Guid PermissionId { get; set; }
    }

    public class SubscriptionResponse
    {
        public Guid SubscriptionId { get; set; }
        public string Tier { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal DueAmount { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime PaymentDue { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool OptOut { get; set; }
        public List<SubscriptionBundleResponse> SubscriptionBundles { get; set; } = new();
    }

    public class SubscriptionBundleResponse
    {
        public string TransactionType { get; set; } = string.Empty;
        public int Value { get; set; }
    }

    public class TransferResponse
    {
        public string TransactionId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime TransactionDate { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class PaymentResponse
    {
        public string PaymentId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime PaymentDate { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public List<string> Details { get; set; } = new();
    }

    public class AvailableUserResponse
    {
        public List<AvailableUser> Users { get; set; } = new();
    }

    public class AvailableUser
    {
        public string UserId { get; set; } = string.Empty;
        public string Alias { get; set; } = string.Empty;
    }

    public class WalletPermissionResponse
    {
        public List<WalletPermission> WalletPermissions { get; set; } = new();
    }

    public class WalletPermission
    {
        public Guid Id { get; set; }
        public Guid WalletId { get; set; }
        public Guid ExternalWalletId { get; set; }
        public bool Admin { get; set; }
        public bool Approve { get; set; }
        public bool BalanceView { get; set; }
        public bool Submit { get; set; }
        public bool TransactionView { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime ExpirationDate { get; set; }
    }
}
