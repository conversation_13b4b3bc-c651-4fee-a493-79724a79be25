﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      throwExceptions="false"
      throwConfigExceptions="true"
      internalLogLevel="Off"
      internalLogFile="c:\logs\nlog-internal.log"
      globalThreshold="Trace" >
    <!-- USE globalThreshold="Off" to turn off ALL tracing -->

    <extensions>
        <add assembly="Nlog.WindowsEventLog" />
    </extensions>

    <variable name="logRootFolder" value="c:\logs\ewallet"/>
    <variable name="applicationName" value="ewallet"/>

    <targets>

        <target xsi:type="EventLog"
                name="eventLog"
                log="${applicationName}"
                source="${applicationName}-core"
                layout="${callsite:className=true:includeNamespace=true:includeSourcePath=true:methodName=true} ${message}${newline}${exception:format=ToString}" />

        <target xsi:type="File"
                 name="filelogger"
                 fileName="${logRootFolder}\logs-${shortdate}.log"
                 layout="${longdate}|${aspnet-traceidentifier}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}" />

        <target xsi:type="EventLog"
                name="eventLogBigData"
                log="BD-BigData"
                source="${applicationName}-BD-BigData"
                layout="${applicationName}|${threadid:padding=3}|${logger}|${message}${newline}${exception:format=ToString}" />

        <target xsi:type="EventLog"
                name="eventLogBigDataClientTime"
                log="BD-Time"
                source="${applicationName}-BD-Time"
                layout="${threadid:padding=3}|${message}" />

        <target xsi:type="EventLog"
                name="eventLogBigDataClientFailedCalls"
                log="BD-FailedCalls"
                source="${applicationName}-BD-FailedCalls"
                layout="${threadid:padding=3}|${message}" />
    </targets>

    <rules>

        <logger
          name="BigDataClientFailedCalls"
          minlevel="Debug"
          writeTo="eventLogBigDataClientFailedCalls"
          final="true">
        </logger>

        <!-- WARNING: FOR NORMAL PRODUCTION USE, SET minlevel="Off" -->
        <logger
          name="BigDataClientCalls"
          minlevel="Off"
          writeTo="eventLogBigData"
          final="true">
        </logger>

        <logger
          name="BigDataClientTime"
          minlevel="Off"
          writeTo="eventLogBigDataClientTime"
          final="true">
        </logger>

        <!-- WARNING: FOR NORMAL PRODUCTION USE, SET minlevel="Info" -->
        <logger
          name="NBG.BigData.*"
          minlevel="Info"
          writeTo="eventLogBigData"
          final="true">
        </logger>

        <logger
         name="*"
         minlevel="Warn"
         writeTo="eventLog" />

        <logger
          name="Filelogger"
          minlevel="Info"
          writeTo="filelogger" />

    </rules>
</nlog>
