﻿using System;
using Microsoft.AspNetCore.Mvc.Filters;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

public class ActiveAuthorizationRule : BaseAuthorizationRule
{
    public override void EnsureAuthorized(AuthorizationContext authContext, AuthorizationFilterContext context)
    {
        CheckAuthorizationContext(authContext);
        if (authContext.ActivePermission.ExpirationDate < DateTime.Now) throw new PermissionNotFoundException();
        CheckWalletId(authContext, context);
    }
}
