﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IWalletAuthorizationRequestsService
{
    Task<WalletAuthorizationResponse> RequestAuthorization(Guid walletId, WalletAuthorizationRequest request);
    Task<List<WalletAuthorizationResponse>> GetAuthorizationRequests(Guid walletId, RequestStatus? status);
    Task<WalletAuthorizationResponse> RejectAuthorizationRequest(Guid walletId, Guid authRequestId);
}
