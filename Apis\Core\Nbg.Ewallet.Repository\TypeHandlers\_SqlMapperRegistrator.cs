﻿using Dapper;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.TypeHandlers;

public static class SqlMapperRegistrator
{
    public static void RegisterTypeHandlers()
    {
        #region [ Enum Type Handlers ]
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<LimitTransactionType>), new EnumTypeHandler<LimitTransactionType>());
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<RequestStatus>), new EnumTypeHandler<RequestStatus>());
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<SubscriptionTier>), new EnumTypeHandler<SubscriptionTier>());
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<SubscriptionTransactionType>), new EnumTypeHandler<SubscriptionTransactionType>());
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<TimeFrameType>), new EnumTypeHandler<TimeFrameType>());
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<TransactionStatus>), new EnumTypeHandler<TransactionStatus>());
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<TransactionSubType>), new EnumTypeHandler<TransactionSubType>());
        SqlMapper.AddTypeHandler(typeof(DapperableEnum<TransactionType>), new EnumTypeHandler<TransactionType>());
        #endregion
    }
}
