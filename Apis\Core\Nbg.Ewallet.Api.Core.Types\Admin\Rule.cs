﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Admin;

public class Rule
{
    [DataMember(Name = "id")]
    public Guid id { get; set; }

    /// <summary>
    /// name
    /// </summary>
    [DataMember(Name = "name")]
    public string RuleName { get; set; }

    /// <summary>
    /// owner
    /// </summary>
    [DataMember(Name = "owner")]
    public string Owner { get; set; }
}

