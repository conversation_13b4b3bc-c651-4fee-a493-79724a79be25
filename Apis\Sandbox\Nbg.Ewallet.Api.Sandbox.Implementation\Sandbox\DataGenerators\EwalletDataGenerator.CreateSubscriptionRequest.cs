﻿using System;
using System.Collections.Generic;
using System.Linq;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static List<SandboxSubscription> GenerateCreateSubscriptionRequest(List<SandboxWallet> wallets)
    {
        return wallets.Select(w =>
        {
            return new SandboxSubscription
            {
                
                Amount = 2000,
                Tier = SubscriptionTier.Premium,
                EndDate = DateTime.Now.AddMonths(6),
                DueAmount = 2000,
                OptOut = false,
                PaymentDue = DateTime.Now.AddMonths(1),
                StartDate = DateTime.Now,
                Status = SubscriptionStatus.PendingPayment,
                SubscriptionId = Guid.NewGuid(),
                SubscriptionBundles = new List<SandboxSubscriptionBundle> {
                    new SandboxSubscriptionBundle
                    {
                        TransactionType = SubscriptionTransactionType.Transfer,
                        Value = 100
                    }
                }
            };
        }).ToList();
    }
}
