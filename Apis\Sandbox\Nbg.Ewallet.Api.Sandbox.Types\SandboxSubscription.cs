using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// SubscriptionRequest definition
/// </summary>
public class SandboxSubscription
{
    [DataMember(Name = "subscriptionId")]
    public Guid SubscriptionId { get; set; }

    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    [DataMember(Name = "startDate")]
    public DateTime StartDate { get; set; }

    [DataMember(Name = "endDate")]
    public DateTime EndDate { get; set; }

    [DataMember(Name = "status")]
    public SubscriptionStatus Status { get; set; }

    [DataMember(Name = "paymentDue")]
    public DateTime PaymentDue { get; set; }

    [DataMember(Name = "trialEndDate")]
    public DateTime TrialEndDate { get; set; }

    [DataMember(Name = "dueAmount")]
    public decimal? DueAmount { get; set; }

    [DataMember(Name = "tier")]
    public SubscriptionTier Tier { get; set; }

    [DataMember(Name = "optOut")]
    public bool OptOut { get; set; }

    [DataMember(Name = "subscriptionBundles")]
    public List<SandboxSubscriptionBundle> SubscriptionBundles { get; set; }
}
