﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Interfaces;

public interface ITransfersService
{
    Task<TransfersResponse> TransferAsync(TransfersRequest request, string walletId, Guid? batchId = null);
    Task<TransfersForecatsResponse> TransferForecastAsync(TransfersRequest request, string walletId, Guid? batchId = null);
    Task<BatchTransfersResponse> BatchAsync(BatchTransfersRequest request, string walletId);
    Task<BatchTransfersForecastResponse> BatchForecastAsync(BatchTransfersRequest request, string walletId);
    Task<CalculateFeesResponse> CalculateFeesAsync(TransfersRequestBase request, Guid walletId);
    Task<BatchTransferExpensesCommissionsResponse> BatchCalculateFeesAsync(BatchTransfersRequest request, Guid walletId);
}
