﻿using Nbg.Ewallet.Repository.Types.Configuration;

namespace Nbg.Ewallet.Repository.Types;

public class SubscriptionApiClientSettings : ClientSettingsBase
{
    public string CreateSubscription { get; set; }
    public string GetSubscription { get; set; }
    public string UpdateSubscription { get; set; }
    public string OptOutSubscription { get; set; }
    public string AvailableSubscriptionTiers { get; set; }
}
