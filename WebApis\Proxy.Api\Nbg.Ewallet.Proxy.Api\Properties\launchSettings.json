﻿{
    "$schema": "http://json.schemastore.org/launchsettings.json",
    "iisSettings": {
        "windowsAuthentication": false,
        "anonymousAuthentication": true,
        "iis": {
            "applicationUrl": "https://localhost/Nbg.Ewallet.proxy.api",
            "sslPort": 0
        },
        "iisExpress": {
            "applicationUrl": "http://localhost:18961",
            "sslPort": 44326
        }
    },
    "profiles": {
        "IIS": {
            "commandName": "IIS",
            "launchBrowser": true,
            "launchUrl": "health",
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "APP_ENVIRONMENT": "Live"
            }
        },
        "nbg.ewallet.proxy.api": {
            "commandName": "Project",
            "launchBrowser": true,
            "launchUrl": "",
            "applicationUrl": "https://localhost:5001;http://localhost:5000",
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "APP_ENVIRONMENT": "Live"
            }
        },
        "Sandbox IIS": {
            "commandName": "IIS",
            "launchBrowser": true,
            "launchUrl": "health",
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "APP_ENVIRONMENT": "Sandbox"
            },
            "ancmHostingModel": "InProcess"
        }
    }
}
