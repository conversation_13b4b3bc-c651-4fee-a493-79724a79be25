﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

[DataContract]
public class CalculateFeesResponse : TransferExpensesCommissionsResponse
{
    /// <summary>
    /// TransactionType
    /// </summary>
    [DataMember(Name = "transactionType")]
    public TransactionSubType? TransactionType { get; set; }
}

[DataContract]
public class TransferExpensesCommissionsResponse
{
    #region FX

    /// <summary>Σύνολο προμηθειών προς είσπραξη στο νόμισμα του λογαριασμού</summary>
    [DataMember(Name = "sumCommissionCurrency")]
    public decimal? SumCommissionCurrency { get; set; }

    /// <summary>Ποσό Πίστωσης στο νόμισμα του λογαριασμού</summary>
    [DataMember(Name = "creditAmountCurrency")]
    public decimal? CreditAmountCurrency { get; set; }

    #endregion FX

    /// <summary>
    /// Full Bank Title=>used in UI
    /// </summary>
    [DataMember(Name = "bankTitle")]
    public string BankTitle { get; set; }

    [DataMember(Name = "exchangeRate")]
    public decimal ExchangeRate { get; set; }

    /// <summary>Καθαρό Ποσό Εμβάσματος</summary>
    [DataMember(Name = "netAmount")]
    public decimal NetAmount { get; set; }

    /// <summary>Καθαρό Ποσό Εμβάσματος στο λογαριασμό Χρέωσης</summary>
    [DataMember(Name = "debitNetAmount")]
    public decimal DebitNetAmount { get; set; }

    /// <summary>Σύνολο Προμηθειών προς Είσπραξη</summary>
    [DataMember(Name = "sumComission")]
    public decimal SumCommission { get; set; }

    /// <summary>Σύνολο προς Χρέωση Λογαριασμού</summary>
    [DataMember(Name = "debitAmount")]
    public decimal DebitAmount { get; set; }

    /// <summary>Προμήθεια ΕΤΕ</summary>
    [DataMember(Name = "eteComission")]
    public decimal EteCommission { get; set; }

    /// <summary>Έξοδα Εντολέα (DEBT/OUR)</summary>
    [DataMember(Name = "deptExpences")]
    public decimal DeptExpenses { get; set; }

    [DataMember(Name = "nonStpExpences")]
    public decimal NonStpExpenses { get; set; }

    [DataMember(Name = "urgentExpences")]
    public decimal UrgentExpenses { get; set; }

    [DataMember(Name = "onlineExpences")]
    public decimal OnlineExpenses { get; set; }

    [DataMember(Name = "exchangeProfit")]
    public decimal ExchangeProfit { get; set; }
}

[DataContract]
public class BatchCalculateFeesResponse : CalculateFeesResponse
{
    /// <summary>
    /// Error
    /// </summary>
    [DataMember(Name = "error")]
    public string Error { get; set; }

    [DataMember(Name = "transactionId")]
    public Guid? TransactionId { get; set; }

    public static BatchCalculateFeesResponse ErrorFail(string message)
    {
        return new BatchCalculateFeesResponse() { Error = message };
    }

    public static BatchCalculateFeesResponse CommissionSum()
    {
        return new BatchCalculateFeesResponse()
        {
            TransactionId = null,
            TransactionType = null,
        };
    }
}

/// <summary>
///
/// </summary>
public class BatchTransferExpensesCommissionsResponse
{
    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "commissions")]
    public List<BatchCalculateFeesResponse> Commissions { get; set; }

    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "commissionSum")]
    public BatchCalculateFeesResponse CommissionSum { get; set; }
}
