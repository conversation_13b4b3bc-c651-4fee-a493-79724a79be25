﻿USE [EWallet]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[DiscountTransactions](
	[DiscountTransactionId] [uniqueidentifier] NOT NULL,
	[TransactionType] [smallint] NOT NULL,
	[Value] [decimal](10, 2) NOT NULL,
	[SubscriptionPlanType] [smallint] NOT NULL
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[DiscountTransactions] ADD  CONSTRAINT [DF_DiscountTransactions_DiscountTransactionId]  DEFAULT (newid()) FOR [DiscountTransactionId]
GO

CREATE TABLE [dbo].[SubscriptionPlans](
	[SubscriptionPlanid] [uniqueidentifier] NOT NULL,
	[SubscriptionPlanType] [smallint] NOT NULL,
 CONSTRAINT [PK_SubscriptionPlans] PRIMARY KEY CLUSTERED 
(
	[SubscriptionPlanType] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SubscriptionPlans] ADD  CONSTRAINT [DF_SubscriptionPlans_SubscriptionPlanid]  DEFAULT (newid()) FOR [SubscriptionPlanid]
GO

