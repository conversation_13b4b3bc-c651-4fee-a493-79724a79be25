﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.EWallet.Repository.Types.coreApis;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxCreateAccountService : ICreateAccountService
{
    public async Task<OpenAccountResponse> OpenAccountAsync(OpenAccountRequest request)
    {
        var response = new OpenAccountResponse
        {
            CustomerFullName = "", Iban = SandBoxRandomDataHelper.GenerateIBAN(), NewAccountNumber = SandBoxRandomDataHelper.GenerateIBAN(), OpenDate = DateTime.UtcNow
        };
        return response;
    }
}
