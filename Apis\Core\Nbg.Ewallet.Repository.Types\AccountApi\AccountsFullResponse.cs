﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

/// <summary>
///
/// </summary>
[DataContract]
public class AccountsFullResponse
{
    /// <summary>
    ///
    /// </summary>
    [DataMember(Name = "accounts")]
    public List<AccountFull> Accounts { get; set; }
}

/// <summary>
///
/// </summary>
[DataContract]
public class AccountFull : Balance
{
    /// <summary>Code for account type, e.g. <code>10</code> for "Ταμιευτήριο"</summary>
    [DataMember(Name = "accountType")]
    public string AccountType { get; set; }

    /// <summary>Overdraft limit</summary>
    [DataMember(Name = "overdraftLimit")]
    public decimal? OverdraftLimit { get; set; }

    /// <summary>Number of mandates</summary>
    [DataMember(Name = "numberOfMandates")]
    public int? NumberOfMandates { get; set; }
}

///
/// </summary>
[DataContract]
public class Balance
{
    /// <summary>
    ///
    /// </summary>
    public Balance()
    {
        AccountFeature = new Feature()
        {
            EnableCheque = true,
            EnableMandate = true,
            EnableOtherInfo = true,
            EnableOwners = true,
            EnableRates = true,
            EnableTransfer = true,
            EnableTrns = true
        };
    }

    /// <summary>Just the serial number of this entry</summary>
    [DataMember(Name = "serialNo")]
    public string SerialNum { get; set; }

    /// <summary>The NBG account number (e.g. 11 digits)</summary>
    [DataMember(Name = "account")]
    public string Account { get; set; }

    /// <summary>Account IBAN.</summary>
    [DataMember(Name = "iban")]
    public string IBAN { get; set; }

    /// <summary>Currency, 3-letter code , e.g. <code>EUR</code></summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>Friendly name</summary>
    [DataMember(Name = "alias")]
    public string Alias { get; set; }

    /// <summary>Account product code</summary>
    [DataMember(Name = "product")]
    public string ProductCode { get; set; }

    /// <summary>Ledger balance</summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }

    /// <summary>Available balance</summary>
    [DataMember(Name = "availableBalance")]
    public decimal AvailableBalance { get; set; }

    /// <summary>Available balance for transfer abroad</summary>
    [DataMember(Name = "newMoneyBalance")]
    public decimal NewmoneyBalance { get; set; }

    /// <summary>TBD</summary>
    [DataMember(Name = "flag")]
    public string Flag { get; set; }

    /// <summary>Connection indicator</summary>
    [DataMember(Name = "allowConnect")]
    public string ConnectInd { get; set; }

    /// <summary>Account debit indicator</summary>
    [DataMember(Name = "allowDebit")]
    public string DebitInd { get; set; }

    /// <summary>Account credit indicator</summary>
    [DataMember(Name = "allowCredit")]
    public string CreditInd { get; set; }

    /// <summary>Account other indicators</summary>
    [DataMember(Name = "indicators")]
    public string RestInd { get; set; }

    /// <summary>Special account</summary>
    [DataMember(Name = "ledgerAccount")]
    public string GenLedger { get; set; }

    ///<summary>Whether the account belongs to the user</summary>
    ///(connectInd='1' and (debitInd='1' or (creditInd='10' or creditInd='01' ))
    [DataMember(Name = "isOwnAccount")]
    public bool isOwnAccount { get; set; }

    /// <summary>Account features </summary>
    [DataMember(Name = "features")]
    public Feature AccountFeature { get; set; }
}
/// <summary>
///
/// </summary>
[DataContract]
public class Feature
{
    /// <summary>
    /// Statements
    /// </summary>
    [DataMember(Name = "statement")]
    public bool EnableTrns { get; set; }

    /// <summary>
    /// Other information
    /// </summary>
    [DataMember(Name = "otherInfo")]
    public bool EnableOtherInfo { get; set; }

    /// <summary>
    /// Mandates
    /// </summary>
    [DataMember(Name = "mandates")]
    public bool EnableMandate { get; set; }

    /// <summary>
    /// Cheques
    /// </summary>
    [DataMember(Name = "cheques")]
    public bool EnableCheque { get; set; }

    /// <summary>
    /// Transfer
    /// </summary>
    [DataMember(Name = "transfer")]
    public bool EnableTransfer { get; set; }

    /// <summary>
    /// Rates
    /// </summary>
    [DataMember(Name = "rates")]
    public bool EnableRates { get; set; }

    /// <summary>
    /// Cobeneficiaries
    /// </summary>
    [DataMember(Name = "beneficiaries")]
    public bool EnableOwners { get; set; }
}

