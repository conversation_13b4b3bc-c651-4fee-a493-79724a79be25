﻿using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.NetCore.Services.Cics.Http;

namespace Nbg.Ewallet.Repository;

public class AuditableCicsConnector : IAuditableCicsConnector
{
    private readonly ICicsHttpConnector _cicsHttpConnector;
    private readonly IServiceAuditRepositoryService _serviceAuditRepository;

    public AuditableCicsConnector(
        ICicsHttpConnector cicsHttpConnector,
        IServiceAuditRepositoryService serviceAuditRepository)
    {
        _cicsHttpConnector = cicsHttpConnector;
        _serviceAuditRepository = serviceAuditRepository;
    }

    public async Task<CicsJsonResponse<TResult>> ExecuteAsync<TRequest, TResult>(CicsJsonRequest<TRequest> request, string resource)
    {
        var auditEntry = await _serviceAuditRepository.AuditCicsBeforeExecAsync(request, resource);
        var result = await _cicsHttpConnector.ExecuteAsync<TRequest, TResult>(request, resource);
        await _serviceAuditRepository.AuditCicsAfterExecAsync(auditEntry, result);
        return result;
    }

    public async Task<TResponse> ExecuteAsync<TRequest, TResponse, TResult>(CicsJsonRequest<TRequest> request, string resource) where TResponse : CicsJsonResponse<TResult>
    {
        var auditEntry = await _serviceAuditRepository.AuditCicsBeforeExecAsync(request, resource);
        var result = await ExecuteAsync<TRequest, TResponse, TResult>(request, resource);
        await _serviceAuditRepository.AuditCicsAfterExecAsync(auditEntry, result);
        return result;
    }
}
