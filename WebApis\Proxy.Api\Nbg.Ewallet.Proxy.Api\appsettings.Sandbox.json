{
    "ProxyMiddleware": {
        "Modules": {
            "Authorization": "default",
            "Audit": "rest",
            "Transform": [ "holistic-transform" ]
        },
        "ReverseProxy": {
            "HttpClientName": "default",
            "AllowNonOKResponse": true,
            "Headers": [ "Callback-Url", "sandboxid" ]
        },
        "Routes": [
            {
                "Local": "/sandbox",
                "Remote": "{CoreServiceUrl}/sandbox",
                "RouteTemplate": "/sandbox",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Local": "/subscriptions",
                "Remote": "{CoreServiceUrl}/subscriptions",
                "RouteTemplate": "/subscriptions/subscription-plans",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Local": "/wallet/subscription",
                "Remote": "{CoreServiceUrl}/wallet/subscription",
                "RouteTemplate": "/wallet/subscription",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            //{
            //    "Name": "walletRegister",
            //    "Local": "/wallet/register",
            //    "Remote": "{CoreServiceUrl}/wallet/register",
            //    "RouteTemplate": "/wallet/register",
            //    "RequestTransformRef": [ "ClientIdRequestTransformer", "ConsentIdRequestTransformer", "SubjectRequestTransformer", "CustomerCodeRequestTransformer", "UserIdRequestTransformer", "AuthorizationLevelRequestTransformer", "NumberOfApprovalsRequestTransformer" ],
            //    "Claims": [
            //        {
            //            "Claim": "sub",
            //            "Values": [ "*" ]
            //        }
            //    ]
            //},
            {
                "Local": "/consents",
                "Remote": "{CoreServiceUrl}/consents",
                "RouteTemplate": "/consents",
                "RequestTransformRef": [ "ClientIdRequestTransformer" ]
            },
            {
                "Local": "/profile",
                "Remote": "{CoreServiceUrl}/profile",
                "RouteTemplate": "/profile",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/subscription",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/subscription/opt-out",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            //{
            //    "Local": "/wallet",
            //    "Remote": "{CoreServiceUrl}/wallet",
            //    "RouteTemplate": "/wallet/{walletId}/account",
            //    "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
            //    "Claims": [
            //        {
            //            "Claim": "sub",
            //            "Values": [ "*" ]
            //        }
            //    ]
            //},
            {
                "Name": "SetWalletPermissionsForOtherWallets",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/wallet-permissions",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "RevokeExternalWalletPermissions",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/wallet-permissions/{targetWalletId}/revoke",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "SetWalletUserPermissions",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/user-permissions",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "RevokeUserPermissionsForUser",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/user-permissions/{userId}/revoke",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "Edit",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/edit",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "Load",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/load",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "Unload",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/unload",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "Create",
                "Local": "/payments",
                "Remote": "{CoreServiceUrl}/payments",
                "RouteTemplate": "/payments/create",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "Execute",
                "Local": "/payments",
                "Remote": "{CoreServiceUrl}/payments",
                "RouteTemplate": "/payments/{walletId}/batch/execute",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "ExecuteForecast",
                "Local": "/payments",
                "Remote": "{CoreServiceUrl}/payments",
                "RouteTemplate": "/payments/{walletId}/batch/execute-forecast",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "BatchCommission",
                "Local": "/payments",
                "Remote": "{CoreServiceUrl}/payments",
                "RouteTemplate": "/payments/{walletId}/batch/commission",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "Pay",
                "Local": "/payments",
                "Remote": "{CoreServiceUrl}/payments",
                "RouteTemplate": "/payments/{walletId}/pay",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "PayForecast",
                "Local": "/payments",
                "Remote": "{CoreServiceUrl}/payments",
                "RouteTemplate": "/payments/{walletId}/pay-forecast",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "Commission",
                "Local": "/payments",
                "Remote": "{CoreServiceUrl}/payments",
                "RouteTemplate": "/payments/{walletId}/commission",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "CalculateFees",
                "Local": "/transfers",
                "Remote": "{CoreServiceUrl}/transfers",
                "RouteTemplate": "/transfers/{walletId}/calculateFees",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "BatchCalculateFees",
                "Local": "/transfers",
                "Remote": "{CoreServiceUrl}/transfers",
                "RouteTemplate": "/transfers/{walletId}/batch/calculateFees",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "Transfer",
                "Local": "/transfers",
                "Remote": "{CoreServiceUrl}/transfers",
                "RouteTemplate": "/transfers/{walletId}/transfer",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "TransferForecast",
                "Local": "/transfers",
                "Remote": "{CoreServiceUrl}/transfers",
                "RouteTemplate": "/transfers/{walletId}/transfer-forecast",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "Batch",
                "Local": "/transfers",
                "Remote": "{CoreServiceUrl}/transfers",
                "RouteTemplate": "/transfers/{walletId}/batch",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "BatchForecast",
                "Local": "/transfers",
                "Remote": "{CoreServiceUrl}/transfers",
                "RouteTemplate": "/transfers/{walletId}/batch-forecast",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ]
            },
            {
                "Name": "approvetransaction",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/transactions/{transactionId}/approve",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "rejecttransaction",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/transactions/{transactionId}/reject",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "requestauthorization",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/authorization-requests",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Name": "rejectrequestauthorization",
                "Local": "/wallet",
                "Remote": "{CoreServiceUrl}/wallet",
                "RouteTemplate": "/wallet/{walletId}/authorization-requests/{authRequestId}/reject",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "UserIdRequestTransformer", "CustomerCodeRequestTransformer" ],
                "Claims": [
                    {
                        "Claim": "sub",
                        "Values": [ "*" ]
                    }
                ],
                "CustomRestScaFilterConfig": {
                    "GeneratorProvider": "scaOtpProvider",
                    "NotificationSubTypeId": "F31D45CC-734C-4814-9220-8BCF5D831FA2"
                }
            },
            {
                "Local": "/consents",
                "Remote": "{CoreServiceUrl}/consents",
                "RouteTemplate": "/consents/create",
                "RequestTransformRef": [ "ClientIdRequestTransformer" ]
            },
            {
                "Local": "/consents",
                "Remote": "{CoreServiceUrl}/consents",
                "RouteTemplate": "/consents/{consentId}"
            },
            {
                "Local": "/management",
                "Remote": "{CoreServiceUrl}/management",
                "RouteTemplate": "/management/accounts",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "CustomerCodeRequestTransformer", "UserIdRequestTransformer", "AuthorizationLevelRequestTransformer", "NumberOfApprovalsRequestTransformer" ],
                "RequiredScopes": [ "ewallet-management" ]
            },
            {
                "Local": "/management",
                "Remote": "{CoreServiceUrl}/management",
                "RouteTemplate": "/management/getprofile",
                "RequestTransformRef": [ "ClientIdRequestTransformer", "CustomerCodeRequestTransformer", "UserIdRequestTransformer", "AuthorizationLevelRequestTransformer", "NumberOfApprovalsRequestTransformer" ],
                "RequiredScopes": [ "ewallet-management" ]
            },
            {
                "Local": "/management",
                "Remote": "{CoreServiceUrl}/management",
                "RouteTemplate": "/management/consent",
                "RequiredScopes": [ "ewallet-management" ]
            },
            {
                "Name": "authconsent",
                "Local": "/management",
                "Remote": "{CoreServiceUrl}/management",
                "RouteTemplate": "/management/consent-authorization",
                "RequiredScopes": [ "ewallet-management" ],
                "RequestTransformRef": [ "CustomerCodeRequestTransformer", "UserIdRequestTransformer", "AuthorizationLevelRequestTransformer", "NumberOfApprovalsRequestTransformer" ],
                "Sca": {
                    "GeneratorProvider": "default",
                    "NotificationSubTypeId": "12943AE3-E3D8-448C-BBFB-AA3BBD8E9488"
                }
            },
            {
                "Local": "/management",
                "Remote": "{CoreServiceUrl}/management",
                "RouteTemplate": "/management/consent-rejection",
                "RequiredScopes": [ "ewallet-management" ]

            },
            {
                "Local": "/health",
                "Audit": false,
                "Validate": false
            }
        ]
    }
}
