<Project>
    <PropertyGroup>
        <!-- Enable .NET analyzers -->
        <EnableNETAnalyzers>true</EnableNETAnalyzers>
        <AnalysisMode>AllEnabledByDefault</AnalysisMode>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
        <Version>3.0.7.0</Version>
        <!--<Nullable>enable</Nullable>-->
    </PropertyGroup>
    <ItemGroup>
        <!-- Add the analyzers package to all projects -->
        <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="9.0.0" PrivateAssets="all" />
    </ItemGroup>
</Project>
