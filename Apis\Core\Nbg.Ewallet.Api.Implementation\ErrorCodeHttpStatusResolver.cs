﻿//using System;
//using System.Collections.Generic;
//using System.Diagnostics;
//using System.Linq;
//using System.Net;
//using System.Reflection;
//using Nbg.Ewallet.Api.Interfaces;
//using Nbg.Ewallet.Api.Types;

//namespace Nbg.Ewallet.Api.Implementation;

//public class ErrorCodeHttpStatusResolver : IErrorCodeHttpStatusResolver
//{
//    private readonly Dictionary<string, HttpStatusCode> _errorToStatusCodes = [];
//    public ErrorCodeHttpStatusResolver()
//    {
//        // Get the all the assemblies loaded in the AppDomain
//        var assemblies = AppDomain.CurrentDomain.GetAssemblies();

//        var derivedTypes = GetAllDerivedTypes(assemblies, typeof(ClientErrorException));
//        foreach (var type in derivedTypes)
//        {
//            Debug.WriteLine(type.Name);
//            if (Activator.CreateInstance(type) is ClientErrorException instance)
//            {
//                var errorCode = instance.GetErrorCode();
//                var httpStatus = instance.GetHttpStatus();
//                _errorToStatusCodes.Add(errorCode, httpStatus);
//            }

//            //since there are no more constants in the ClientErrorException, we can remove this part
//            //FieldInfo[] fields = type.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.NonPublic);
//            //string ErrorCode = (string)fields.First(x => x.Name == "ErrorCode").GetRawConstantValue();
//            //int HttpStatus = (int)fields.First(x => x.Name == "HttpStatus").GetRawConstantValue();
//            //_errorToStatusCodes.Add(ErrorCode, HttpStatus);
//        }
//    }

//    public HttpStatusCode Resolve(string errorCode)
//    {
//        if (_errorToStatusCodes.TryGetValue(errorCode, out var retHttpStatus))
//        {
//            return retHttpStatus;
//        }
//        return HttpStatusCode.InternalServerError;
//    }

//    private static IEnumerable<Type> GetAllDerivedTypes(Assembly[] assemblies, Type baseType)
//    {
//        // get the Api.Types assembly where our ClientErrorExceptions are defined
//        var assembly = assemblies.FirstOrDefault(x => x.GetName().Name.Contains("Nbg.Ewallet.Api.Types"));
//        // probably make it more robust in case the Exceptions.cs has been moved out of Api.Types assembly
//        // Loop through all the types in the assembly
//        foreach (var type in assembly.GetTypes())
//        {
//            // Check if the type is a subclass of the baseType and it is not the baseType itself
//            if (baseType.IsAssignableFrom(type) && type != baseType)
//            {
//                // use yield with IEnumerable, so we do not create a new List
//                // maybe not in much use here since the list will be relatively short
//                yield return type;
//            }
//        }
//    }
//}
