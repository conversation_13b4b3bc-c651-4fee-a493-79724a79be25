﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.dbQueries;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.NetCore.Data;
using Nbg.NetCore.Services.Cics.Http;

namespace Nbg.Ewallet.Repository;

public class ServiceAuditRepositoryService : IServiceAuditRepositoryService
{
    private readonly ILogger _logger;
    private readonly CicsHttpConnectorOptions _cicsConfig;
    private readonly IDbConnector? _dbConnector;
    private readonly ServiceAuditSettings _configuration;

    private readonly HashSet<string> _propsToMask = new HashSet<string>([ /*"number", "card", "pan", "cardNumber",*/ "Authorization", "client_secret", "access_token"],
        StringComparer.OrdinalIgnoreCase);

    public ServiceAuditRepositoryService(
        ILogger<ServiceAuditRepositoryService> logger,
        IOptions<ServiceAuditSettings> configuration,
        IDbConnectorFactory dbConnectorFactory,
        IOptions<CicsHttpConnectorOptions> cicsConfig)
    {
        _logger = logger;
        _cicsConfig = cicsConfig?.Value;
        _configuration = configuration.Value;

        if (_configuration.EnableAudit)
        {
            _dbConnector = dbConnectorFactory.Get(_configuration.Database);
        }
    }

    public async Task InsertAuditEntryAsync(ServiceAudit auditEntry)
    {
        if (_dbConnector == null) return;
        try
        {
            using var connection = _dbConnector.Open();
            var result = await connection.ExecuteAsync(ServiceAuditQueries.Insert, auditEntry);
            if (result != 0) return;

            _logger.LogError("Failed to insert auditEntry {AuditEntryId}", auditEntry.Id);
        }
        catch (AggregateException aex)
        {
            _logger.LogError(aex, "AggregateExceptionMessages: {AllInnerExceptionMessages}", GetAllInnerExceptionMessages(aex));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AuditToSQLDb Request Failed: {Message}", ex.Message);
        }
    }

    public async Task UpdateAuditEntryAsync(ServiceAudit auditEntry)
    {
        if (_dbConnector == null) return;
        try
        {
            using (var connection = _dbConnector.Open())
            {
                var result = await connection.ExecuteAsync(ServiceAuditQueries.UpdateById, auditEntry);
                if (result != 0) return;

                _logger.LogError("Failed to update auditEntry {AuditEntryId}", auditEntry.Id);
            }
        }
        catch (AggregateException aex)
        {
            _logger.LogError(aex, "AggregateExceptionMessages: {AllInnerExceptionMessages}", GetAllInnerExceptionMessages(aex));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AuditToDatabase Failed: {Message}", ex.Message);
        }
    }

    public async Task<ServiceAudit> AuditBeforeExecAsync(HttpRequestMessage requestMsg)
    {
        var sessionId = GetCustomHash(DateTime.UtcNow.ToString("O"));
        var auditEntry = await GetAuditEntry(requestMsg);
        auditEntry.ClientSession = sessionId;
        auditEntry.ServiceName = $"{_configuration.PrefixServiceName}..{auditEntry.ServiceName}";
        auditEntry.Application = _configuration.Application;
        await InsertAuditEntryAsync(auditEntry);

        return auditEntry;
    }

    public async Task AuditAfterExecAsync(ServiceAudit auditEntry, HttpResponseMessage responseMsg)
    {
        _ = responseMsg.IsSuccessStatusCode
            ? auditEntry.ResponseTextData = HideSensitiveProperties(await responseMsg.Content.ReadAsStringAsync())
            : auditEntry.ErrorData = await responseMsg.Content.ReadAsStringAsync();
        auditEntry.ExtraTextData = HideSensitiveProperties(GetHttpHeaders(responseMsg));
        var dtEnd = DateTime.Now;
        auditEntry.EndTime = dtEnd;
        auditEntry.ExecutionTime = dtEnd - auditEntry.Timestamp;
        await UpdateAuditEntryAsync(auditEntry);
    }

    public async Task<ServiceAudit> AuditCicsBeforeExecAsync<TRequest>(CicsJsonRequest<TRequest> request, string action)
    {
        var sessionId = GetCustomHash(DateTime.UtcNow.ToString("O"));
        var auditEntry = await GetAuditEntry(JsonSerializer.Serialize(request), action);
        auditEntry.ClientSession = sessionId;
        auditEntry.ServiceName = $"{_cicsConfig.Host}:{_cicsConfig.Port}/..{auditEntry.ServiceName}";
        auditEntry.Application = _configuration.Application;
        await InsertAuditEntryAsync(auditEntry);

        return auditEntry;
    }

    public async Task AuditCicsAfterExecAsync<TResponse>(ServiceAudit auditEntry, CicsJsonResponse<TResponse> response)
    {
        if (response.Fault != null) auditEntry.ErrorData = JsonSerializer.Serialize(response.Fault);
        if (response.Exception != null) auditEntry.ErrorData = JsonSerializer.Serialize(response.Exception);
        if (response.Payload != null) auditEntry.ResponseTextData = JsonSerializer.Serialize(response.Payload);
        var dtEnd = DateTime.Now;
        auditEntry.EndTime = dtEnd;
        auditEntry.ExecutionTime = dtEnd - auditEntry.Timestamp;
        await UpdateAuditEntryAsync(auditEntry);
    }

    #region [ Helpers ]

    private static readonly SHA1 _sha1Instance = SHA1.Create();

    public static string GetCustomHash(string value)
    {
        if (value is null)
            return null;

        var textData = Encoding.UTF8.GetBytes(value);
        byte[] hash;
        lock (_sha1Instance)
        {
            hash = _sha1Instance.ComputeHash(textData);
        }

        return BitConverter.ToString(hash).Replace("-", string.Empty, StringComparison.Ordinal);
    }

    //private static string GetCustomHash(string value)
    //{
    //    string result;
    //    if (value == null)
    //    {
    //        result = null;
    //    }
    //    else
    //    {
    //        var sha = SHA1.Create();
    //        var textData = Encoding.UTF8.GetBytes(value);
    //        var hash = sha.ComputeHash(textData);
    //        result = BitConverter.ToString(hash).Replace("-", string.Empty);
    //    }

    //    return result;
    //}

    public ServiceAudit GetNewServiceAudit(string url, string action, string requestString)
    {
        var auditEntry = new ServiceAudit
        {
            Id = Guid.NewGuid(),
            Timestamp = DateTime.Now,
            Host = Environment.MachineName,
            ServiceName = url,
            ClientRequestPath = action,
            RequestTextData = HideSensitiveProperties(requestString),
            Application = _configuration.Application
        };

        return auditEntry;
    }

    private async Task<ServiceAudit> GetAuditEntry(HttpRequestMessage requestMsg)
    {
        var auditEntry = new ServiceAudit
        {
            Id = Guid.NewGuid(),
            Timestamp = DateTime.Now,
            Host = Environment.MachineName,
            ServiceName = requestMsg.RequestUri?.OriginalString?.Split('?')[0],
            RequestTextData = requestMsg.Content != null
                ? HideSensitiveProperties(await requestMsg.Content?.ReadAsStringAsync())
                : null,
            ClientRequestPath = requestMsg.RequestUri?.OriginalString
        };

        return auditEntry;
    }

    private async Task<ServiceAudit> GetAuditEntry(string requestMsg, string action)
    {
        var auditEntry = new ServiceAudit
        {
            Id = Guid.NewGuid(),
            Timestamp = DateTime.Now,
            Host = Environment.MachineName,
            ServiceName = action,
            RequestTextData = requestMsg,
            ClientRequestPath = action
        };

        return auditEntry;
    }

    private static string GetAllInnerExceptionMessages(Exception ex)
    {
        var sb = new StringBuilder();
        sb.Append(ex.Message);

        var exInnerException = ex.InnerException;
        while (exInnerException != null)
        {
            if (exInnerException.Message != null)
                sb.Append(" - ").Append(exInnerException.Message);

            exInnerException = exInnerException.InnerException;
        }

        return sb.ToString();
    }


    public string HideSensitiveProperties(string jsonText)
    {
        if (string.IsNullOrWhiteSpace(jsonText))
            return "";

        var node = TryParseJson(jsonText);
        if (node == null)
            return "";

        if (node is JsonObject jObject)
        {
            MaskJsonObject(jObject, _propsToMask);
            return jObject.ToJsonString(new JsonSerializerOptions { WriteIndented = true });
        }

        if (node is JsonArray jArray)
        {
            foreach (var item in jArray.OfType<JsonObject>())
                MaskJsonObject(item, _propsToMask);

            return jArray.ToJsonString(new JsonSerializerOptions { WriteIndented = true });
        }

        return "";
    }


    private JsonNode? TryParseJson(string json)
    {
        try
        {
            return JsonNode.Parse(json);
        }
        catch
        {
            return null;
        }
    }

    private void MaskJsonObject(JsonObject obj, IEnumerable<string> propsToMask)
    {
        foreach (var prop in propsToMask)
        {
            if (obj.ContainsKey(prop))
                obj[prop] = "***";
        }

        foreach (var kvp in obj)
        {
            if (kvp.Value is JsonObject childObj)
                MaskJsonObject(childObj, propsToMask);
            else if (kvp.Value is JsonArray childArray)
            {
                foreach (var item in childArray.OfType<JsonObject>())
                    MaskJsonObject(item, propsToMask);
            }
        }
    }

    private static string TrySerialize(object obj)
    {
        try
        {
            return JsonSerializer.Serialize(obj);
        }
        catch
        {
            return "";
        }
    }

    /// <summary>
    /// Masks a string either fully or partially using '*' characters.
    /// </summary>
    public static string? MaskValue(string str, bool full = false)
    {
        if (str == null || str.Equals("null", StringComparison.OrdinalIgnoreCase))
            return null;

        var length = str.Length;

        if (length == 0)
            return str;

        if (length == 1)
            return "*";

        if (full)
            return "********";

        var fillerLength = length / 2;
        var firstLength = (length - fillerLength) / 2;

        Span<char> result = length <= 256
            ? stackalloc char[length]
            : new char[length];

        str.AsSpan(0, firstLength).CopyTo(result[..firstLength]);

        for (var i = firstLength; i < firstLength + fillerLength; i++)
            result[i] = '*';

        str.AsSpan(firstLength + fillerLength).CopyTo(result[(firstLength + fillerLength)..]);

        return new string(result);
    }

    //private static string MaskValue(string str, bool full = false)
    //{
    //    if (str == null || str.ToLower() == "null")
    //    {
    //        return null;
    //    }

    //    switch (str.Length)
    //    {
    //        case 0:
    //            return str;
    //        case 1:
    //            return "*";
    //    }

    //    if (full)
    //    {
    //        return new string('*', 8);
    //    }

    //    string filler = new string('*', str.Length / 2);
    //    int firstLength = (str.Length - filler.Length) / 2;
    //    return str.Substring(0, firstLength) + filler + str.Substring(firstLength + filler.Length);
    //}

    private static string? GetHttpHeaders(HttpResponseMessage httpContext)
    {
        var auditingHeaders = httpContext.RequestMessage.Headers.ToList();

        return auditingHeaders.Count != 0
            ? JsonSerializer.Serialize(auditingHeaders.ToDictionary(kvp => kvp.Key, kvp => kvp.Value))
            : null;
    }

    #endregion
}
