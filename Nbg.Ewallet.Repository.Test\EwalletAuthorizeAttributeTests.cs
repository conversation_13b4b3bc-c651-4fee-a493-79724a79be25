﻿using System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;
using Moq;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Xunit;

namespace Nbg.Ewallet.Repository.Test;

public class EwalletAuthorizeAttributeTests
{
    [Theory]
    [InlineData(AuthorizationTypes.Admin, true)]
    [InlineData(AuthorizationTypes.Admin, false)]
    //[InlineData(AuthorizationTypes.RegisteredWallet, true)]
    [InlineData(AuthorizationTypes.RegisteredWallet, false)]
    [InlineData(AuthorizationTypes.TransactionView, true)]
    [InlineData(AuthorizationTypes.TransactionView, false)]
    [InlineData(AuthorizationTypes.Active, true)]
    [InlineData(AuthorizationTypes.Active, false)]
    [InlineData(AuthorizationTypes.ApproveTransaction, true)]
    [InlineData(AuthorizationTypes.ApproveTransaction, false)]
    [InlineData(AuthorizationTypes.SubmitTransaction, true)]
    [InlineData(AuthorizationTypes.SubmitTransaction, false)]
    [InlineData(AuthorizationTypes.BalanceView, true)]
    [InlineData(AuthorizationTypes.BalanceView, false)]
    public void OnAuthorization_CallsMultipleRules_Success(AuthorizationTypes rule, bool fail)
    {
        // Arrange

        var activePermission = new RepoUserPermission { WalletId = Guid.NewGuid() };
        switch (rule)
        {
            case AuthorizationTypes.Admin when fail:
                activePermission.Admin = false;
                break;
            case AuthorizationTypes.Admin:
                activePermission.Admin = true;
                break;
            case AuthorizationTypes.RegisteredWallet:
                break;
            case AuthorizationTypes.TransactionView when fail:
                activePermission.TransactionView = false;
                break;
            case AuthorizationTypes.TransactionView:
                activePermission.TransactionView = true;
                break;
            case AuthorizationTypes.Active when fail:
                activePermission.ExpirationDate = DateTime.Now.AddDays(-1);
                break;
            case AuthorizationTypes.Active:
                activePermission.ExpirationDate = DateTime.Now.AddDays(1);
                break;
            case AuthorizationTypes.ApproveTransaction when fail:
                activePermission.Approve = false;
                break;
            case AuthorizationTypes.ApproveTransaction:
                activePermission.Approve = true;
                break;
            case AuthorizationTypes.SubmitTransaction when fail:
                activePermission.Submit = false;
                break;
            case AuthorizationTypes.SubmitTransaction:
                activePermission.Submit = true;
                break;
            case AuthorizationTypes.BalanceView when fail:
                activePermission.BalanceView = false;
                break;
            case AuthorizationTypes.BalanceView:
                activePermission.BalanceView = true;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(rule), rule, null);
        }

        var requiredPermissions = new[] { rule };
        var mockOptions = new Mock<IOptions<RepositorySettings>>();
        mockOptions.Setup(o => o.Value).Returns(new RepositorySettings { DisablePermissions = false });

        var attribute = new EwalletAuthorizeFilter(mockOptions.Object, requiredPermissions);
        var httpContext = new DefaultHttpContext();

        var authContext = new AuthorizationContext { ActivePermission = activePermission };
        httpContext.Items["AuthContext"] = authContext;

        var routeData = new Microsoft.AspNetCore.Routing.RouteData();
        routeData.Values["walletid"] = authContext.ActivePermission.WalletId.ToString();

        var actionContext = new ActionContext
        {
            HttpContext = httpContext,
            RouteData = routeData,
            ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
        };
        var filterContext = new AuthorizationFilterContext(actionContext, []);
        // Act

        if (fail)
        {
            Assert.Throws<PermissionNotFoundException>(() => attribute.OnAuthorization(filterContext));
        }
        else
        {
            attribute.OnAuthorization(filterContext);
        }
    }
}
