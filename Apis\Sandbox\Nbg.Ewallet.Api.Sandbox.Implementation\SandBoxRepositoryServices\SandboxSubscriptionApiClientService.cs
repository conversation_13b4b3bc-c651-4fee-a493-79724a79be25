using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using AutoMapper;
using ibank.ThirdParty.Types.JCics;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxSubscriptionApiClientService : ISubscriptionApiClientService
{
    private readonly ISandBoxRepositoryService _sandboxRepositoryService;
    private readonly IMapper _mapper;

    public SandboxSubscriptionApiClientService(ISandBoxRepositoryService sandBoxRepositoryService,
        IMapper mapper)
    {
        _sandboxRepositoryService = sandBoxRepositoryService;
        _mapper = mapper;
    }

    public async Task<SubscriptionApiResponse> CreateSubscriptionAsync(CreateSubscriptionRequest request)
    {
        EwalletSandbox model = await _sandboxRepositoryService.GetSandBoxModel();

        if (!SubscriptionTiers(Guid.NewGuid()).TryGetValue(request.Tier, out SandboxSubscription subscription)) throw new NotFoundException();

        model.WalletsSubscriptions.Add(subscription);
        await _sandboxRepositoryService.UpdateSandboxData(model);
        return _mapper.Map<SubscriptionApiResponse>(subscription);
    }

    public async Task<SubscriptionApiResponse> GetSubscriptionAsync(Guid subscriptionId)
    {
        EwalletSandbox model = await _sandboxRepositoryService.GetSandBoxModel();
        SandboxSubscription subscription = model.WalletsSubscriptions.FirstOrDefault(x => x.SubscriptionId == subscriptionId) ?? throw new NotFoundException();
        if (subscription.TrialEndDate > DateTime.Now)
        {
            subscription.DueAmount = 0;

        }
        else
        {
            subscription.DueAmount = subscription.Amount;
        }

        await _sandboxRepositoryService.UpdateSandboxData(model);
        return _mapper.Map<SubscriptionApiResponse>(subscription);
    }

    public async Task<SubscriptionApiResponse> UpdateSubscriptionAsync(UpdateSubscriptionRequest request)
    {
        EwalletSandbox model = await _sandboxRepositoryService.GetSandBoxModel();
        SandboxSubscription subscription = model.WalletsSubscriptions.FirstOrDefault(x => x.SubscriptionId == request.SubscriptionId) ?? throw new NotFoundException();

        SubscriptionTiers(subscription.SubscriptionId).TryGetValue(request.Tier, out var updatedSubscription);
        subscription.Status = updatedSubscription.Status;
        subscription.PaymentDue = updatedSubscription.PaymentDue;
        subscription.StartDate = updatedSubscription.StartDate;
        subscription.EndDate = updatedSubscription.EndDate;
        subscription.SubscriptionBundles = updatedSubscription.SubscriptionBundles;
        subscription.Amount = updatedSubscription.Amount;
        subscription.Tier = updatedSubscription.Tier;
        subscription.DueAmount = updatedSubscription.DueAmount;
        subscription.SubscriptionId = subscription.SubscriptionId;
        subscription.OptOut = false;

        await _sandboxRepositoryService.UpdateSandboxData(model);
        return _mapper.Map<SubscriptionApiResponse>(subscription);
    }

    public async Task<SubscriptionApiResponse> OptOutSubscriptionAsync(OptOutSubscriptionRequest request)
    {
        EwalletSandbox model = await _sandboxRepositoryService.GetSandBoxModel();
        SandboxSubscription subscription = model.WalletsSubscriptions.FirstOrDefault(x => x.SubscriptionId == request.SubscriptionId) ?? throw new NotFoundException();

        subscription.OptOut = true;
        subscription.DueAmount = 0;
        subscription.Amount = 0;
        subscription.StartDate = DateTime.Now;
        subscription.PaymentDue = DateTime.MaxValue;


        await _sandboxRepositoryService.UpdateSandboxData(model);
        return _mapper.Map<SubscriptionApiResponse>(subscription);
    }


    public async Task<AvailableTiersApiResponse> GetAvailableSubscriptionTiersAsync()
    {
        AvailableTiersApiResponse response = new()
        {
            AvailableTiers = [.. SubscriptionTiers(Guid.NewGuid()).Select(x => new AvailableTier
            {
                SubscriptionBundles = [.. x.Value.SubscriptionBundles.Select(y => new SubscriptionBundle
                {
                    Value = y.Value,
                    TransactionType = y.TransactionType,
                })],
                Tier = x.Key,
            })]
        };
        return response;
    }

    private Dictionary<SubscriptionTier, SandboxSubscription> SubscriptionTiers(Guid subscriptionId) => new()
    {
        {
            SubscriptionTier.Basic,
            new SandboxSubscription()
            {
                SubscriptionId = subscriptionId,
                PaymentDue = DateTime.Now.AddMonths(1),
                Status = SubscriptionStatus.Trial,
                Tier = SubscriptionTier.Basic,
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddMonths(6),
                TrialEndDate = DateTime.Now.AddMonths(1),
                DueAmount = 0,
                Amount = 500,
                OptOut = false,
                SubscriptionBundles =
                [
                    new SandboxSubscriptionBundle()
                    {
                        TransactionType = SubscriptionTransactionType.Transfer,
                        Value = 150
                    },
                    new SandboxSubscriptionBundle()
                    {
                        TransactionType = SubscriptionTransactionType.Payment,
                        Value = 200
                    }
                ]
            }
        },
        {
            SubscriptionTier.Premium,
            new SandboxSubscription()
            {
                SubscriptionId = subscriptionId,
                PaymentDue = DateTime.Now.AddMonths(1),
                Status = SubscriptionStatus.Trial,
                Tier = SubscriptionTier.Premium,
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddMonths(9),
                TrialEndDate = DateTime.Now.AddMonths(1),
                DueAmount = 0,
                Amount = 1000,
                OptOut = false,
                SubscriptionBundles =
                [
                    new SandboxSubscriptionBundle()
                    {
                        TransactionType = SubscriptionTransactionType.Transfer,
                        Value = 700
                    },
                    new SandboxSubscriptionBundle()
                    {
                        TransactionType = SubscriptionTransactionType.Payment,
                        Value = 500
                    }
                ]
            }
        },
        {
            SubscriptionTier.Free,
            new SandboxSubscription()
            {
                SubscriptionId = subscriptionId,
                PaymentDue = DateTime.MaxValue,
                Status = SubscriptionStatus.Trial,
                Tier = SubscriptionTier.Free,
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddMonths(3),
                TrialEndDate = DateTime.Now.AddMonths(1),
                DueAmount = 0,
                Amount = 0,
                SubscriptionBundles =
                [
                    new SandboxSubscriptionBundle()
                    {
                        TransactionType = SubscriptionTransactionType.Transfer,
                        Value = 10
                    },
                    new SandboxSubscriptionBundle()
                    {
                        TransactionType = SubscriptionTransactionType.Payment,
                        Value = 7
                    }
                ]
            }
        }
    };

}
