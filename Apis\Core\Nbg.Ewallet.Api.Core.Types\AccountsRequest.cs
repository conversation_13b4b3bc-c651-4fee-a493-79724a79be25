﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Accounts request
/// </summary>
[DataContract]
public class AccountsRequest
{
    /// <summary>
    /// User id
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserID { get; set; }

    /// <summary>
    /// Network id
    /// </summary>
    [DataMember(Name = "networkId")]
    public string NetworkId { get; set; }
}
