﻿{
    "Environment": "QA", // this is the active configuration
    "Environments": {
        "SandBox": {
            //"BaseAddress": "https://localhost:44326/Nbg.Ewallet.proxy.api/",
            "BaseAddress": "https://localhost/Nbg.Ewallet.proxy.api/",
            "Users": [
                {
                    "username": "ELPIDA003000009",
                    "password": "12345AA!!",
                    "customercode": "**********"
                },
                {
                    "username": "Companyuserid1",
                    "password": "12345AA!!",
                    "customercode": "**********"
                },
                {
                    "username": "1182278",
                    "password": "12345AA!!",
                    "customercode": "**********"
                }
            ]
        },
        "Core": {
            "BaseAddress": "https://localhost/Nbg.Ewallet.api/",
            "Users": [
                {
                    "username": "ELPIDA003000009",
                    "password": "12345AA!!",
                    "customercode": "**********"
                },
                {
                    "username": "1182278",
                    "password": "12345AA!!",
                    "customercode": "**********"
                }
            ]
        },
        "SandBoxDirect": {
            "BaseAddress": "https://localhost/Nbg.Ewallet.Api.Sandbox/",
            //"BaseAddress": "https://localhost:44384/Nbg.Ewallet.Api.Sandbox/",
            "Users": [
                {
                    "username": "ELPIDA003000009",
                    "password": "12345AA!!",
                    "customercode": "**********"
                },
                {
                    "username": "1182278",
                    "password": "12345AA!!",
                    "customercode": "**********"
                }
            ]
        },
        "Production": {
            "BaseAddress": "https://localhost:44326/Nbg.Ewallet.proxy.api/",
            "Users": [
                {
                    "username": "ELPIDA003000009",
                    "password": "12345AA!!",
                    "customercode": "**********"
                },
                {
                    "username": "1182278",
                    "password": "12345AA!!",
                    "customercode": "**********"
                }
            ]
        },
        "QA": {
            "BaseAddress": "https://ibankretailqa.nbg.gr/apis.proxy.myqa/ewallet.proxy.api/v1/",
            "Users": [
                {
                    "username": "PLAYVS003200002",
                    //"username": "BIGVSA001900002",
                    "password": "12345AA!!",
                    "customercode": "**********",
                    "walletId": "39373A47-02DB-45F6-9332-070935D29866"
                },
                {
                    //"username": "PLAYVS003200003",
                    "username": "BIGVSA001900001",
                    "password": "12345AA!!",
                    "customercode": "**********",
                    "walletId": "7EB796DE-61FA-46EE-93AB-69F35B1077C1"
                },
                {
                    "username": "PLAYVS003200003",
                    "password": "12345AA!!",
                    "customercode": "**********",
                    "walletId": "7EB796DE-61FA-46EE-93AB-69F35B1077C1"
                },
                {
                    "username": "1019480",
                    "password": "12345AA!!",
                    "customercode": "**********",
                    "walletId": "77414208-0FBE-4C12-B221-EFD4F87B00ED"
                }
            ]
        }
    }
}
