ALTER TABLE [EWalle<PERSON>].[dbo].[AuthorizationRequests]
DROP COLUMN [Status]

ALTER TABLE [EWallet].[dbo].[AuthorizationRequests]
ADD [Status] NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].DiscountTransactions
DROP COLUMN TransactionType

ALTER TABLE [EWallet].[dbo].DiscountTransactions
ADD TransactionType NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].DiscountTransactions
DROP COLUMN SubscriptionPlanType

ALTER TABLE [EWallet].[dbo].DiscountTransactions
ADD SubscriptionPlanType NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].Limits
DROP COLUMN TimeFrame

ALTER TABLE [EWallet].[dbo].Limits
ADD TimeFrame NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].Limits
DROP COLUMN TransactionType

ALTER TABLE [EWalle<PERSON>].[dbo].Limits
ADD TransactionType NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].SubscriptionBundles
DROP COLUMN TransactionType

ALTER TABLE [EWallet].[dbo].SubscriptionBundles
ADD TransactionType NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].SubscriptionPlans
DROP CONSTRAINT PK_SubscriptionPlans

ALTER TABLE [EWallet].[dbo].SubscriptionPlans
DROP COLUMN SubscriptionPlanType

ALTER TABLE [EWallet].[dbo].SubscriptionPlans
ADD SubscriptionPlanType NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].UserPermissions
DROP COLUMN Approve

ALTER TABLE [EWallet].[dbo].UserPermissions
ADD Approve NVARCHAR(25)

ALTER TABLE [EWallet].[dbo].WalletPermissions
DROP COLUMN Approve

ALTER TABLE [EWallet].[dbo].WalletPermissions
ADD Approve NVARCHAR(25)

sp_rename 'Transactions.TransactionType', 'TransactionSubType', 'COLUMN';
sp_rename 'Transactions.TransferType', 'TransactionType', 'COLUMN';

ALTER TABLE [EWallet].[dbo].Limits
ADD PermissionId UNIQUEIDENTIFIER
