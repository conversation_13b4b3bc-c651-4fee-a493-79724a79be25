using FluentAssertions;
using SandboxTest.Infrastructure;
using SandboxTest.Models;
using System.Net;
using Xunit;
using Xunit.Abstractions;

namespace SandboxTest.Tests
{
    /// <summary>
    /// Comprehensive tests for Transfer functionality in the Sandbox API
    ///
    /// Test Logic Overview:
    /// This test class covers all transfer-related operations including:
    /// 1. Wallet-to-Wallet Transfers - Moving funds between wallets
    /// 2. Wallet-to-IBAN Transfers - Sending funds to external accounts
    /// 3. Batch Transfers - Processing multiple transfers at once
    /// 4. Transfer Forecasts - Estimating transfer costs and timing
    /// 5. Transfer Status Tracking - Monitoring transfer progress
    ///
    /// Each test validates both the transfer operation and the resulting
    /// state changes to ensure funds are properly moved and accounted for.
    /// </summary>
    public class TransferTests : SandboxTestBase
    {
        public TransferTests(ITestOutputHelper output) : base(output)
        {
        }
        [Fact(DisplayName = "Wallet-to-Wallet Transfer - Should transfer funds between wallets successfully")]
        public async Task WalletToWalletTransfer_ShouldTransferFunds_BetweenWalletsSuccessfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create source and target wallets
             * 2. Load funds into source wallet
             * 3. Transfer funds from source to target wallet
             * 4. Verify the transfer succeeds
             * 5. Verify balances are updated correctly in both wallets
             * 
             * Expected Result:
             * - Transfer operation returns success
             * - Source wallet balance decreases by transfer amount
             * - Target wallet balance increases by transfer amount
             * - Transfer is recorded in transaction history
             */
            var sourceWalletName = TestDataHelper.GenerateUniqueWalletName();
            var targetWalletName = TestDataHelper.GenerateUniqueWalletName();
            
            var sourceRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(sourceWalletName);
            var targetRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(targetWalletName);
            
            var sourceRegisterResponse = await Client.PostAsJsonAsync("wallet/register", sourceRegisterRequest);
            var targetRegisterResponse = await Client.PostAsJsonAsync("wallet/register", targetRegisterRequest);
            
            var sourceWallet = await AssertSuccessAndDeserialize<WalletResponse>(sourceRegisterResponse);
            var targetWallet = await AssertSuccessAndDeserialize<WalletResponse>(targetRegisterResponse);

            // Load funds into source wallet
            var loadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{sourceWallet.WalletId}/load", loadRequest);

            // Get initial balances
            var sourceInitialBalanceResponse = await Client.GetAsync($"wallet/{sourceWallet.WalletId}/balance");
            var targetInitialBalanceResponse = await Client.GetAsync($"wallet/{targetWallet.WalletId}/balance");
            
            var sourceInitialBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(sourceInitialBalanceResponse);
            var targetInitialBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(targetInitialBalanceResponse);

            // Create transfer request
            var transferAmount = TestDataHelper.GenerateAmount(100, 300);
            var transferRequest = new
            {
                TargetWalletId = targetWallet.WalletId,
                Amount = transferAmount,
                Currency = "EUR",
                Description = "Test wallet-to-wallet transfer"
            };

            // Act
            var transferResponse = await Client.PostAsJsonAsync($"wallet/{sourceWallet.WalletId}/transfers/wallet", transferRequest);

            // Assert
            var transferResult = await AssertSuccessAndDeserialize<TransferResponse>(transferResponse);
            
            transferResult.TransactionId.Should().NotBeNullOrEmpty("transfer should have a transaction ID");
            transferResult.Amount.Should().Be(transferAmount, "transfer amount should match request");
            transferResult.Status.Should().BeOneOf("Completed", "Pending", "Processing", "transfer status should be valid");

            // Verify balances are updated correctly
            var sourceUpdatedBalanceResponse = await Client.GetAsync($"wallet/{sourceWallet.WalletId}/balance");
            var targetUpdatedBalanceResponse = await Client.GetAsync($"wallet/{targetWallet.WalletId}/balance");
            
            var sourceUpdatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(sourceUpdatedBalanceResponse);
            var targetUpdatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(targetUpdatedBalanceResponse);
            
            sourceUpdatedBalance.Balance.Should().Be(sourceInitialBalance.Balance - transferAmount, 
                "source wallet balance should decrease by transfer amount");
            targetUpdatedBalance.Balance.Should().Be(targetInitialBalance.Balance + transferAmount, 
                "target wallet balance should increase by transfer amount");
        }

        [Fact(DisplayName = "Wallet-to-IBAN Transfer - Should transfer funds to external account successfully")]
        public async Task WalletToIbanTransfer_ShouldTransferFunds_ToExternalAccountSuccessfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load funds
             * 2. Transfer funds to an external IBAN
             * 3. Verify the transfer succeeds
             * 4. Verify wallet balance is updated correctly
             * 5. Verify transfer is recorded in transaction history
             * 
             * Expected Result:
             * - Transfer operation returns success
             * - Wallet balance decreases by transfer amount
             * - Transfer is recorded with correct status
             * - Transfer details include recipient IBAN
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Get initial balance
            var initialBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var initialBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(initialBalanceResponse);

            // Create transfer request
            var transferAmount = TestDataHelper.GenerateAmount(100, 300);
            var targetIban = TestDataHelper.GenerateIban();
            var transferRequest = new
            {
                TargetIban = targetIban,
                Amount = transferAmount,
                Currency = "EUR",
                Description = "Test wallet-to-IBAN transfer"
            };

            // Act
            var transferResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/transfers/iban", transferRequest);

            // Assert
            var transferResult = await AssertSuccessAndDeserialize<TransferResponse>(transferResponse);
            
            transferResult.TransactionId.Should().NotBeNullOrEmpty("transfer should have a transaction ID");
            transferResult.Amount.Should().Be(transferAmount, "transfer amount should match request");
            transferResult.Status.Should().BeOneOf("Completed", "Pending", "Processing", "transfer status should be valid");

            // Verify balance is updated correctly
            var updatedBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var updatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(updatedBalanceResponse);
            
            updatedBalance.Balance.Should().Be(initialBalance.Balance - transferAmount, 
                "wallet balance should decrease by transfer amount");
        }

        [Fact(DisplayName = "Get Transfer Status - Should return current status of transfer")]
        public async Task GetTransferStatus_ShouldReturnCurrentStatus_OfTransfer()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load funds
             * 2. Initiate a transfer
             * 3. Retrieve the transfer status
             * 4. Verify status information is complete and accurate
             * 
             * Expected Result:
             * - Transfer status is returned
             * - Status includes all relevant details (amount, date, status)
             * - Status reflects the current state of the transfer
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Create and execute transfer
            var transferAmount = TestDataHelper.GenerateAmount(100, 300);
            var targetIban = TestDataHelper.GenerateIban();
            var transferRequest = new
            {
                TargetIban = targetIban,
                Amount = transferAmount,
                Currency = "EUR",
                Description = "Test transfer for status check"
            };

            var transferResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/transfers/iban", transferRequest);
            var transfer = await AssertSuccessAndDeserialize<TransferResponse>(transferResponse);

            // Act
            var statusResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/transfers/{transfer.TransactionId}");

            // Assert
            var transferStatus = await AssertSuccessAndDeserialize<TransferResponse>(statusResponse);
            
            transferStatus.TransactionId.Should().Be(transfer.TransactionId, "transaction ID should match");
            transferStatus.Amount.Should().Be(transferAmount, "amount should match");
            transferStatus.Status.Should().NotBeNullOrEmpty("status should be provided");
            transferStatus.TransactionDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5), "transaction date should be recent");
        }

        [Fact(DisplayName = "Get Transfer History - Should return all transfers for wallet")]
        public async Task GetTransferHistory_ShouldReturnAllTransfers_ForWallet()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load funds
             * 2. Execute multiple transfers
             * 3. Retrieve transfer history
             * 4. Verify all transfers are included in history
             * 5. Verify transfer details are complete
             * 
             * Expected Result:
             * - All transfers are returned in history
             * - Transfer details include all required information
             * - Transfers are properly ordered (typically by date)
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(1000, 2000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Execute multiple transfers
            var transfer1Amount = TestDataHelper.GenerateAmount(100, 200);
            var transfer2Amount = TestDataHelper.GenerateAmount(150, 250);
            
            var transfer1Request = new
            {
                TargetIban = TestDataHelper.GenerateIban(),
                Amount = transfer1Amount,
                Currency = "EUR",
                Description = "Test transfer 1"
            };
            
            var transfer2Request = new
            {
                TargetIban = TestDataHelper.GenerateIban(),
                Amount = transfer2Amount,
                Currency = "EUR",
                Description = "Test transfer 2"
            };
            
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/transfers/iban", transfer1Request);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/transfers/iban", transfer2Request);

            // Act
            var historyResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/transfers");

            // Assert
            var transferHistory = await AssertSuccessAndDeserialize<List<TransferResponse>>(historyResponse);
            
            transferHistory.Should().NotBeEmpty("transfer history should contain transfers");
            transferHistory.Should().HaveCountGreaterOrEqualTo(2, "history should include both test transfers");
            
            foreach (var transfer in transferHistory)
            {
                transfer.TransactionId.Should().NotBeNullOrEmpty("transfer should have transaction ID");
                transfer.Amount.Should().BeGreaterThan(0, "transfer amount should be positive");
                transfer.Currency.Should().Be("EUR", "currency should be EUR");
                transfer.Status.Should().NotBeNullOrEmpty("status should be provided");
                transfer.TransactionDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromHours(1), "transaction date should be recent");
            }
        }

        [Fact(DisplayName = "Transfer Forecast - Should provide cost and timing estimates")]
        public async Task TransferForecast_ShouldProvideCostAndTimingEstimates()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet
             * 2. Request a transfer forecast for a potential transfer
             * 3. Verify forecast information is returned
             * 4. Verify forecast includes fees, timing, and other relevant details
             * 
             * Expected Result:
             * - Forecast is returned with estimated fees
             * - Estimated completion time is provided
             * - Any transfer restrictions are indicated
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Create forecast request
            var transferAmount = TestDataHelper.GenerateAmount(100, 500);
            var targetIban = TestDataHelper.GenerateIban();
            var forecastRequest = new
            {
                TargetIban = targetIban,
                Amount = transferAmount,
                Currency = "EUR"
            };

            // Act
            var forecastResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/transfers/forecast", forecastRequest);

            // Assert
            if (forecastResponse.StatusCode == HttpStatusCode.OK)
            {
                var forecast = await DeserializeResponseAsync<object>(forecastResponse);
                forecast.Should().NotBeNull("forecast should be returned");
                
                // Note: The exact structure would depend on the API implementation
                // This test validates that the endpoint is accessible and returns data
            }
            else
            {
                // Some implementations might not have forecast functionality implemented yet
                forecastResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotImplemented, HttpStatusCode.NotFound);
            }
        }

        [Fact(DisplayName = "Batch Transfer - Should process multiple transfers in one request")]
        public async Task BatchTransfer_ShouldProcessMultipleTransfers_InOneRequest()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load funds
             * 2. Create a batch of multiple transfers
             * 3. Submit the batch transfer request
             * 4. Verify the batch operation succeeds
             * 5. Verify all transfers in the batch are processed
             * 
             * Expected Result:
             * - Batch operation returns success
             * - All transfers in the batch are processed
             * - Wallet balance reflects all transfers
             * - Transfer history includes all batch transfers
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(1000, 2000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Get initial balance
            var initialBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var initialBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(initialBalanceResponse);

            // Create batch transfer request
            var transfer1Amount = TestDataHelper.GenerateAmount(100, 200);
            var transfer2Amount = TestDataHelper.GenerateAmount(150, 250);
            var totalAmount = transfer1Amount + transfer2Amount;
            
            var batchRequest = new
            {
                Transfers = new[]
                {
                    new
                    {
                        TargetIban = TestDataHelper.GenerateIban(),
                        Amount = transfer1Amount,
                        Currency = "EUR",
                        Description = "Batch transfer 1"
                    },
                    new
                    {
                        TargetIban = TestDataHelper.GenerateIban(),
                        Amount = transfer2Amount,
                        Currency = "EUR",
                        Description = "Batch transfer 2"
                    }
                }
            };

            // Act
            var batchResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/transfers/batch", batchRequest);

            // Assert
            if (batchResponse.StatusCode == HttpStatusCode.OK)
            {
                var batchResult = await DeserializeResponseAsync<object>(batchResponse);
                batchResult.Should().NotBeNull("batch result should be returned");
                
                // Verify balance is updated correctly
                var updatedBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
                var updatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(updatedBalanceResponse);
                
                updatedBalance.Balance.Should().Be(initialBalance.Balance - totalAmount, 
                    "wallet balance should decrease by total transfer amount");
                
                // Verify transfers are in history
                var historyResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/transfers");
                var transferHistory = await AssertSuccessAndDeserialize<List<TransferResponse>>(historyResponse);
                
                transferHistory.Should().Contain(t => t.Amount == transfer1Amount, "history should include first transfer");
                transferHistory.Should().Contain(t => t.Amount == transfer2Amount, "history should include second transfer");
            }
            else
            {
                // Some implementations might not have batch transfer functionality implemented yet
                batchResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotImplemented, HttpStatusCode.NotFound);
            }
        }
    }
}
