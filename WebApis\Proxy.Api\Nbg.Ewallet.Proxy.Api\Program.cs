using System;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Nbg.NetCore.SqlServer.ConfigurationProvider;
using NLog;
using NLog.Web;

namespace nbg.ewallet.proxy.api;

public class Program
{
    public static void Main(string[] args)
    {
        var baseEnv = Environment.GetEnvironmentVariable("APP_ENVIRONMENT") ?? "Live"; // Live or Sandbox
        var aspEnv = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"; // Development, QA, Production

        var fullEnv = $"{baseEnv}.{aspEnv}"; // e.g., Sandbox.Development

        var logger = LogManager.Setup()
            .LoadConfigurationFromFile($"nlog.{fullEnv}.config", optional: true)
            .GetCurrentClassLogger();

        try
        {
            logger.Info($"Starting Ewallet Proxy Web API in {fullEnv}");
            CreateHostBuilder(args, baseEnv, aspEnv).Build().Run();
        }
        catch (Exception ex)
        {
            logger.Fatal(ex, "Host terminated unexpectedly.");
            throw;
        }
        finally
        {
            LogManager.Shutdown();
        }
    }

    private static IHostBuilder CreateHostBuilder(string[] args, string baseEnv, string aspEnv) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((hostingContext, configBuilder) =>
            {
                configBuilder
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{baseEnv}.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{baseEnv}.{aspEnv}.json", optional: false, reloadOnChange: true);
#if !DEBUG
                var tempConfig = configBuilder.Build();

                var settings = new ProxySettings();
                tempConfig.GetSection(nameof(ProxySettings)).Bind(settings);

                if (!settings.SkipAddSqlServer)
                {
                    configBuilder.AddSqlServerNoWatcher();
                }
#endif
            })
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder
                    .UseStartup<Startup>()
                    .ConfigureLogging(logging => logging.ClearProviders())
                    .CaptureStartupErrors(true);
            })
            .UseNLog();
}
