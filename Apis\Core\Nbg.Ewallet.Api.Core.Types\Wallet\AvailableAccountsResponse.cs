﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Represents a response containing a list of available accounts.
/// </summary>
public class AvailableAccountsResponse
{
    /// <summary>
    /// Collection of available accounts.
    /// </summary>
    [DataMember(Name = "accounts")]
    public List<AvailableAccount> Accounts { get; set; }
}

/// <summary>
/// Represents an available account.
/// </summary>
public class AvailableAccount
{
    /// <summary>Account IBAN.</summary>
    [DataMember(Name = "iban")]
    public string IBAN { get; set; }
    /// <summary>Friendly name</summary>
    [DataMember(Name = "alias")]
    public string? Alias { get; set; }
    /// <summary>Available balance</summary>
    [DataMember(Name = "availableBalance")]
    public decimal AvailableBalance { get; set; }
    /// <summary>Ledger balance</summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }
    /// <summary>Currency code</summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; } = "EUR";
}
