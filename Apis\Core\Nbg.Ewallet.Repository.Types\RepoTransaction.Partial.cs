﻿using System;
using System.Text.Json;
using ibank.ThirdParty.Types.Core;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Repository.Types;

public partial class RepoTransaction
{
    private TransfersRequestBase _transfersRequest;
    public TransfersRequestBase TransfersRequest
    {
        get
        {
            if (TransactionType != Types.TransactionType.Transfer)
                throw new InvalidOperationException($"Invalid TransactionType: {TransactionType}");

            try
            {
                if (string.IsNullOrWhiteSpace(RequestJson)) return _transfersRequest;
                _transfersRequest ??= JsonSerializer.Deserialize<TransfersRequestBase>(RequestJson);
                return _transfersRequest;
            }
            catch (Exception)
            {
                return _transfersRequest;
            }
        }

        set
        {
            if (value != null && TransactionType == Types.TransactionType.Transfer)
                RequestJson = JsonSerializer.Serialize(value);
        }
    }

    private PaymentCoreRequest _paymentCoreRequest;
    public PaymentCoreRequest PaymentCoreRequest
    {
        get
        {
            if (TransactionType != Types.TransactionType.Payment) throw new InvalidOperationException($"Invalid TransactionType: {TransactionType}"); ;
            try
            {
                if (string.IsNullOrWhiteSpace(RequestJson)) return _paymentCoreRequest;
                _paymentCoreRequest ??= JsonSerializer.Deserialize<PaymentCoreRequest>(RequestJson);
                return _paymentCoreRequest;
            }
            catch (Exception)
            {
                return _paymentCoreRequest;
            }
        }

        set
        {
            if (value != null && TransactionType == Types.TransactionType.Payment)
                RequestJson = JsonSerializer.Serialize(value);
        }
    }
}
