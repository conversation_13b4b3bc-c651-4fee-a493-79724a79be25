﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using nbg.ewallet.repository.types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.Services.Cics.Http;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbusrpr;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbusupd;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraall;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraupd;
using Nbg.NetCore.Services.Cics.Http.Contract.Jiball;
using Nbg.NetCore.Utilities;
using static nbg.ewallet.repository.types.JBOPACResponse;

namespace Nbg.Ewallet.Repository;

//TODO: refactor this
[Obsolete("Use IMainFrameConnector instead", true)]
public class CicsConnectorService : ICicsConnectorService
{
    private readonly ILogger<CicsConnectorService> _logger;
    private readonly IAuditableCicsConnector _cicsHttpConnector;

    public CicsConnectorService(
        ILogger<CicsConnectorService> logger,
        IAuditableCicsConnector cicsHttpConnector)
    {
        _logger = logger;
        _cicsHttpConnector = cicsHttpConnector;
    }

    //JCRAALL Cics Call
    public async Task<JcraallResponsePayload> GetCustomerDataAsync(string customerCode)
    {
        var response = await _cicsHttpConnector.Jcraall(new CicsJsonRequest<JcraallRequestPayload> { Payload = CreateJcraallRequest(customerCode) });

        if (response.Exception == null) return response.Payload;

        _logger.LogError($"CicsJsonRequest Exception in JCRAALL with code {response.Exception.Code} and message {response.Exception.Descr}");
        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JcraallRequestPayload CreateJcraallRequest(string customerCode)
    {
        const string yes = "Y";
        return new JcraallRequestPayload
        {
            CustomerID = customerCode,
            EnablePerformanceTuning = yes,
            FindCompanyData = yes,
            FindLegalDocuments = yes,
            FindConfirmationDocuments = yes,
            FindAddresses = yes,
            FindLogicalAddresses = yes,
            FindSystemAddresses = yes,
            FindNames = yes,
            FindIndications = yes,
            FindAccounts = yes,
            FindAccountDetails = yes,
            FindAccountRelIndications = yes,
            FindGeneralInformation = yes,
            FindQuestionnaire = yes,
            FindUserRelations = yes
        };
    }

    // JIBALL Cics Call
    public async Task<JiballResponsePayload> GetAuthorizationLevelAsync(string userId)
    {
        var response = await _cicsHttpConnector.JiballAsync(new CicsJsonRequest<JiballRequestPayload> { Payload = CreateJiballRequest(userId) });

        if (response.Exception == null) return response.Payload;

        _logger.LogError($"CicsJsonRequest Exception in JIBALL with code {response.Exception.Code} and message {response.Exception.Descr}");
        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JiballRequestPayload CreateJiballRequest(string userId)
    {
        const string yes = "Y";
        return new JiballRequestPayload { FindCompUserDetails = yes, Userid = userId };
    }

    public async Task<JbusupdResponsePayload> ConnectAccountAsync(string userId, string account)
    {
        var payload = CreateJbusupdRequest(userId, account);
        var response = await _cicsHttpConnector.Jbusupd(new CicsJsonRequest<JbusupdRequestPayload> { Payload = payload });

        _logger.LogWarning($"ConnectAccountAsync response: {JsonSerializer.Serialize(response)}");
        if (response.Exception == null) return response.Payload;

        _logger.LogError($"CicsJsonRequest Exception in JBUSUPD with code {response.Exception.Code} and message {response.Exception.Descr}");
        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JbusupdRequestPayload CreateJbusupdRequest(string userId, string account)
    {
        return new JbusupdRequestPayload
        {
            Action = "A",
            Branch = "700",
            SubBranch = "4",
            UserId = userId,
            CreationUserId = userId,
            Account = AccountHelpers.IbanToAccount(account)
        };
    }

    public async Task<JbusrprResponsePayload> GetUserProfileAsync(string userId)
    {
        var payload = CreateJbusrprRequest(userId);
        var response = await _cicsHttpConnector.JbusrprAsync(new CicsJsonRequest<JbusrprRequestPayload> { Payload = payload });

        _logger.LogWarning($"GetUserProfileAsync response: {JsonSerializer.Serialize(response)}");
        if (response.Exception == null) return response.Payload;

        _logger.LogError($"CicsJsonRequest Exception in JBUSRPR with code {response.Exception.Code} and message {response.Exception.Descr}");
        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JbusrprRequestPayload CreateJbusrprRequest(string userId)
    {
        return new JbusrprRequestPayload { FetchLogicalAddressSystem = "X", UserId = userId, };
    }

    public async Task<CicsJsonResponse<JcraupdResponsePayload>> SubmitCustomerCommissionAsync(string userId, long? indicationType, long? indicationValue)
    {
        var payload = CreateJcraupdRequest(userId, indicationType, indicationValue);
        var response = await _cicsHttpConnector.JcraupdAsync(new CicsJsonRequest<JcraupdRequestPayload> { Payload = payload });

        _logger.LogWarning($"GetUserProfileAsync response: {JsonSerializer.Serialize(response)}");
        if (response.Exception != null)
        {
            _logger.LogError($"CicsJsonRequest Exception in JCRAUPD with code {response.Exception.Code} and message {response.Exception.Descr}");
        }

        if (response.Fault != null)
        {
            _logger.LogError($"CicsJsonRequest Exception in JCRAUPD with code {response.Fault.Detail} and message {response.Fault.FaultString}");
        }

        return response;
    }

    private static JcraupdRequestPayload CreateJcraupdRequest(string userId, long? indicationType, long? indicationValue)
    {
        var request = new JcraupdRequestPayload
        {
            Userid = userId,
            HandleIndications = "Y",
            UpdUserid = "E99999",
            UpdBranch = "700",
            SubBranch = "4",
            IndicationsData = new IndicationsData
            {
                IndicationType = indicationType,
                IndicationValue = indicationValue,
                EffectiveDate = DateTime.Now.ToString("dd.MM.yyyy"),
                EndDate = string.Empty,
                FlagDelete = string.Empty,
                FlagAuth = string.Empty
            }
        };
        return request;
    }

    public async Task<List<NetCore.Services.Cics.Http.Contract.Jcraall.Indications>> FindIndicationsAsync(string userId)
    {
        var response = await _cicsHttpConnector.Jcraall(new CicsJsonRequest<JcraallRequestPayload>
        {
            Payload = new JcraallRequestPayload { Userid = userId, FindIndications = "Y" }
        });

        if (response.Exception != null)
        {
            _logger.LogError($"CicsJsonRequest Exception in JCRAALL with code {response.Exception.Code} and message {response.Exception.Descr}");
        }

        if (response.Payload?.Indications == null) return new List<NetCore.Services.Cics.Http.Contract.Jcraall.Indications>();

        return response.Payload.Indications.ToList();
    }

    public async Task<JBOAccountInfo> CreateAccountAsync(string craCode, string userId, string productCode)
    {
        var mJBOPACRequest = new JBOPACRequest(craCode, userId, productCode);
        var response = await _cicsHttpConnector.TryExecutePackage<JBOAccountInfo>("JBOPAC", mJBOPACRequest);

        if (response.Exception != null)
        {
            _logger.LogError($"JBOPACRequest Exception with code {response.Exception?.Code} and message {response.Exception?.Descr}");
            throw new Exception(response.Exception.Descr);
        }

        if (response.Fault == null) return new JBOAccountInfo { Account = response.Payload.Account, AccountIban = response.Payload.AccountIban };

        _logger.LogError($"JBOPACRequest Fault with short message '{response.Fault?.FaultString}' and detail message: '{response.Fault?.Detail?.CICSFault}'");
        return null;
    }
}
