﻿using System;

namespace Nbg.Ewallet.Repository.Types;

public interface IPermission
{
    public Guid Id { get; set; }
    public Guid WalletId { get; set; }
    bool Submit { get; set; }
    bool Approve { get; set; }
    bool Admin { get; set; }
    bool BalanceView { get; set; }
    bool TransactionView { get; set; }
    bool InheritsAuthorizations { get; set; }
    DateTime ExpirationDate { get; set; }
    DateTime CreatedAt { get; set; }
    string AssignedBy { get; set; }
    string RevokedBy { get; set; }

    PermissionType PermissionType { get; }

    public string ToLogString();
}

public enum PermissionType
{
    WALLET,
    USER
}
