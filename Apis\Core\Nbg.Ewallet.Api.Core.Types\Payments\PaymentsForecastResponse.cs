﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Payments Forecast Response
/// </summary>
[DataContract]
public class PaymentsForecastResponse
{
    /// <summary>
    /// Mass Transaction Id
    /// </summary>
    [DataMember(Name = "batchId")]
    public Guid BatchId { get; set; } = Guid.Empty;

    public static PaymentsForecastResponse ToForecastResponse(PaymentsResponse response)
    {
        var forecastResponse = new PaymentsForecastResponse
        {
            BatchId = response.BatchId
        };
        return forecastResponse;
    }
}
