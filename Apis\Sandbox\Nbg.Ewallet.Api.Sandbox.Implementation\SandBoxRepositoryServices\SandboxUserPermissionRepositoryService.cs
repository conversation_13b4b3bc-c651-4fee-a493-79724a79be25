﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxUserPermissionRepositoryService : IUserPermissionsRepositoryService
{
    private readonly IMapper _mapper;
    private readonly ILogger<SandboxUserPermissionRepositoryService> _logger;
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;

    public SandboxUserPermissionRepositoryService(IMapper mapper, ILogger<SandboxUserPermissionRepositoryService> logger,
        IHttpContextAccessor httpContextAccessor, ISandBoxRepositoryService sandBoxRepositoryService, IHttpContextRepositoryService httpContextRepositoryService)
    {
        _mapper = mapper;
        _sandBoxRepositoryService = sandBoxRepositoryService;
        _httpContextAccessor = httpContextAccessor;
        _httpContextRepositoryService = httpContextRepositoryService;
        _logger = logger;
    }

    public async Task<List<RepoUserPermission>> FindAllByActiveAndWalletIdAndUserId(Guid walletId, string userId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        SandboxWallet wallet = mEwalletSandbox.Wallets.FirstOrDefault(x => x.WalletId == walletId)
            ?? throw new WalletNotFoundException();

        List<SandboxUserPermission> mListUserPermissions;
        if (userId != null)
        {
            mListUserPermissions = mEwalletSandbox.UserPermissions
               .Where(x => x.WalletId == walletId && x.ExpirationDate > DateTime.UtcNow && x.UserId == userId)
               .ToList();
            return _mapper.Map<List<RepoUserPermission>>(mListUserPermissions);
        }

        mListUserPermissions = mEwalletSandbox.UserPermissions
           .Where(x => x.WalletId == walletId && x.ExpirationDate > DateTime.UtcNow)
           .ToList();
        return _mapper.Map<List<RepoUserPermission>>(mListUserPermissions);
    }

    public async Task<List<RepoUserPermission>> FindAllByExpiredAndWalletIdAndUserIdAsync(Guid walletId, string userId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        SandboxWallet wallet = mEwalletSandbox.Wallets.FirstOrDefault(x => x.WalletId == walletId)
            ?? throw new WalletNotFoundException();

        if (userId == null)
        {
            List<SandboxUserPermission> mListUserPermissions = mEwalletSandbox.UserPermissions
                .Where(x => x.WalletId == walletId)
                .Where(x => x.ExpirationDate < DateTime.UtcNow)
                .ToList();
            return _mapper.Map<List<RepoUserPermission>>(mListUserPermissions);
        }
        else
        {
            List<SandboxUserPermission> mListUserPermissions = mEwalletSandbox.UserPermissions
                .Where(x => x.WalletId == walletId && x.UserId == userId)
                .Where(x => x.ExpirationDate < DateTime.UtcNow)
                .ToList();
            return _mapper.Map<List<RepoUserPermission>>(mListUserPermissions);
        }
    }

    public async Task<RepoUserPermission> FindOneByActiveAndUserIdAsync(string UserId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        SandboxUserPermission mListUserPermission = mEwalletSandbox.UserPermissions
            .FirstOrDefault(x => x.UserId == UserId && x.ExpirationDate > DateTime.UtcNow);
        return _mapper.Map<RepoUserPermission>(mListUserPermission);
    }

    public async Task<List<RepoUserPermission>> FindAllByActiveAndUserIdAndWalletIdAsync(string userid, Guid walletid)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        List<SandboxUserPermission> mListUserPermissions = mEwalletSandbox.UserPermissions
            .Where(x => x.WalletId == walletid && x.UserId == userid)
            .ToList();
        return _mapper.Map<List<RepoUserPermission>>(mListUserPermissions);
    }

    public async Task ExpireExistingPermissions(Guid myWalletId, IEnumerable<RepoUserPermission> permissions)
    {
        var callersid = _httpContextRepositoryService.GetUserId();
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        var myWAllet = mEwalletSandbox.Wallets
            .First(x => x.OwnerUserId == callersid);

        var mListWalletUsersPermissions = mEwalletSandbox.UserPermissions
            .Where(x => x.WalletId == myWAllet.WalletId && x.ExpirationDate > DateTime.UtcNow)
            .ToList();

        foreach (var UserPermissionInRequest in permissions)
        {
            // get existing permissions for user
            // and expire them
            var userPermissions = mListWalletUsersPermissions.Where(x => x.UserId == UserPermissionInRequest.UserId);
            foreach (var userPermission in userPermissions)
            {
                userPermission.ExpirationDate = DateTime.UtcNow;
            }
        }

        await _sandBoxRepositoryService.UpdateSandboxData(mEwalletSandbox);
    }

    public async Task SaveAllAsync(List<RepoUserPermission> permission)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        foreach (var UserPermissionInRequest in permission)
        {
            SandboxUserPermission mUserPermission = new SandboxUserPermission();
            mUserPermission.WalletId = UserPermissionInRequest.WalletId;
            mUserPermission.Id = UserPermissionInRequest.Id;
            mUserPermission.UserId = UserPermissionInRequest.UserId;
            mUserPermission.Admin = UserPermissionInRequest.Admin;
            mUserPermission.Approve = UserPermissionInRequest.Approve;
            mUserPermission.BalanceView = UserPermissionInRequest.BalanceView;
            mUserPermission.Submit = UserPermissionInRequest.Submit;
            mUserPermission.TransactionView = UserPermissionInRequest.TransactionView;
            mUserPermission.InheritsAuthorizations = UserPermissionInRequest.InheritsAuthorizations;
            mUserPermission.ExpirationDate = UserPermissionInRequest.ExpirationDate;
            mEwalletSandbox.UserPermissions.Add(mUserPermission);
        }

        await _sandBoxRepositoryService.UpdateSandboxData(mEwalletSandbox);
    }

    public async Task<RepoUserPermission> SaveAsync(RepoUserPermission userPermission)
    {
        var sandbox = await _sandBoxRepositoryService.GetSandBoxModel();

        var permissionToUpdate = sandbox.UserPermissions.Where(up => up.Id == userPermission.Id).FirstOrDefault();

        permissionToUpdate.ExpirationDate = userPermission.ExpirationDate;
        permissionToUpdate.Approve = userPermission.Approve;
        permissionToUpdate.BalanceView = userPermission.BalanceView;
        permissionToUpdate.Submit = userPermission.Submit;
        permissionToUpdate.TransactionView = userPermission.TransactionView;
        permissionToUpdate.InheritsAuthorizations = userPermission.InheritsAuthorizations;
        await _sandBoxRepositoryService.UpdateSandboxData(sandbox);

        return _mapper.Map<RepoUserPermission>(permissionToUpdate);
    }
}
