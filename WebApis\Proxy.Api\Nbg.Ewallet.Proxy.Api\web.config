﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="bin\Sandbox\net8.0\Nbg.Ewallet.Proxy.Api.exe" arguments="" stdoutLogEnabled="false" hostingModel="InProcess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_HTTPS_PORT" value="443" />
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Development" />
          <environmentVariable name="APP_ENVIRONMENT" value="Sandbox" />
        </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
</configuration>