using SandboxTest.Infrastructure;
using Xunit.Abstractions;

namespace SandboxTest.Utilities
{
    /// <summary>
    /// Utility class for managing sandbox operations like reset, status check, etc.
    /// </summary>
    public static class SandboxManager
    {
        /// <summary>
        /// Gets the static sandbox ID used across all tests
        /// </summary>
        public static string SandboxId => "ewallet-test-sandbox-001";

        /// <summary>
        /// Resets the sandbox completely for a fresh test sequence
        /// This deletes all sandbox data and resets the initialization flag
        /// </summary>
        /// <param name="output">Test output helper for logging</param>
        public static async Task ResetSandboxAsync(ITestOutputHelper output)
        {
            output.WriteLine("🔄 Starting complete sandbox reset...");
            output.WriteLine($"📋 Sandbox ID: {SandboxId}");
            
            using var factory = new SandboxApiFactory();
            
            try
            {
                // Delete the sandbox
                await factory.DeleteSandboxAsync();
                
                // Reset the initialization flag
                SandboxTestBase.ResetInitializationFlag();
                
                output.WriteLine("✅ Sandbox reset completed successfully");
                output.WriteLine("📝 Next test run will load fresh data from SandboxImport.json");
            }
            catch (Exception ex)
            {
                output.WriteLine($"❌ Sandbox reset failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Checks if sandbox data exists and shows status
        /// </summary>
        /// <param name="output">Test output helper for logging</param>
        public static async Task<bool> CheckSandboxStatusAsync(ITestOutputHelper output)
        {
            output.WriteLine($"🔍 Checking sandbox status for ID: {SandboxId}");
            
            using var factory = new SandboxApiFactory();
            
            try
            {
                var exists = await factory.CheckSandboxExistsAsync();
                
                if (exists)
                {
                    output.WriteLine("✅ Sandbox exists and has data");
                }
                else
                {
                    output.WriteLine("❌ Sandbox does not exist or has no data");
                }
                
                return exists;
            }
            catch (Exception ex)
            {
                output.WriteLine($"⚠️ Error checking sandbox status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Forces a fresh data load (resets flag and loads data)
        /// Use this when you want to reload data without deleting the sandbox
        /// </summary>
        /// <param name="output">Test output helper for logging</param>
        public static async Task ForceDataReloadAsync(ITestOutputHelper output)
        {
            output.WriteLine("🔄 Forcing fresh data reload...");
            
            // Reset the initialization flag
            SandboxTestBase.ResetInitializationFlag();
            
            using var factory = new SandboxApiFactory();
            
            try
            {
                // Load fresh data
                await factory.LoadSandboxDataAsync();
                
                output.WriteLine("✅ Fresh data loaded successfully");
            }
            catch (Exception ex)
            {
                output.WriteLine($"❌ Failed to reload data: {ex.Message}");
                throw;
            }
        }
    }
}
