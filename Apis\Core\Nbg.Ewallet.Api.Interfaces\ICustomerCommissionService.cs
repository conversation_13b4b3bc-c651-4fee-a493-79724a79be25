﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Interfaces;

public interface ICustomerCommissionService
{
    Task UpdateCustomerCommissionAsync(Guid walletId, TransactionSubType transactionSubType, string userId, string customerCode);
    Task UpdateCustomerCommissionAsync(Guid walletId, TransactionSubType transactionSubType, string userId, string customerCode, int commissionTransactionCount);
}
