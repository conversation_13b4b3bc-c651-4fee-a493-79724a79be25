﻿//using System;
//using Newtonsoft.Json;

//namespace Nbg.Ewallet.Api.Types.TimeConverters;

//public class UtcDateTimeConverter : JsonConverter<DateTime>
//{
//    public override DateTime ReadJson(JsonReader reader, Type objectType, DateTime existingValue, bool hasExistingValue, JsonSerializer serializer)
//        => DateTime.Parse(reader.Value.ToString()).ToUniversalTime();

//    public override void WriteJson(JsonWriter writer, DateTime value, JsonSerializer serializer)
//        => writer.WriteValue(value.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
//}
