# .editorconfig for .NET/C# projects

root = true

[*]
charset = utf-8
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.cs]
indent_style = space
indent_size = 4

# New line preferences
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true

# Naming conventions
dotnet_naming_rule.public_members_must_be_pascal.severity = warning
dotnet_naming_rule.public_members_must_be_pascal.symbols = public_symbols
dotnet_naming_rule.public_members_must_be_pascal.style = pascal_style

dotnet_naming_symbols.public_symbols.applicable_kinds = property, method, field, event, class, struct, interface, enum, delegate
dotnet_naming_symbols.public_symbols.applicable_accessibilities = public, protected, internal, protected_internal, private_protected
dotnet_naming_symbols.public_symbols.required_modifiers =

dotnet_naming_style.pascal_style.capitalization = pascal_case

dotnet_naming_rule.parameter_should_be_camel_case.severity = warning
dotnet_naming_rule.parameter_should_be_camel_case.symbols = parameter
dotnet_naming_rule.parameter_should_be_camel_case.style = camel_case_style

dotnet_naming_symbols.parameter.applicable_kinds = parameter
dotnet_naming_symbols.parameter.applicable_accessibilities = *
dotnet_naming_symbols.parameter.required_modifiers =

dotnet_naming_style.camel_case_style.capitalization = camel_case

dotnet_naming_rule.local_variables_should_be_camel_case.severity = warning
dotnet_naming_rule.local_variables_should_be_camel_case.symbols = local_variables
dotnet_naming_rule.local_variables_should_be_camel_case.style = camel_case_style

dotnet_naming_symbols.local_variables.applicable_kinds = local
dotnet_naming_symbols.local_variables.applicable_accessibilities = *
dotnet_naming_symbols.local_variables.required_modifiers =

# Private fields: _camelCase
dotnet_naming_rule.private_fields_should_be_camel_case_with_underscore.severity = warning
dotnet_naming_rule.private_fields_should_be_camel_case_with_underscore.symbols = private_fields
dotnet_naming_rule.private_fields_should_be_camel_case_with_underscore.style = camel_case_with_underscore_style

dotnet_naming_symbols.private_fields.applicable_kinds = field
dotnet_naming_symbols.private_fields.applicable_accessibilities = private
dotnet_naming_symbols.private_fields.required_modifiers =

dotnet_naming_style.camel_case_with_underscore_style.capitalization = camel_case
dotnet_naming_style.camel_case_with_underscore_style.required_prefix = _

# Use var for built-in types and when type is apparent, but use explicit type elsewhere
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
csharp_style_var_elsewhere = false:suggestion

# Prefer expression-bodied members for simple members
csharp_style_expression_bodied_methods = when_on_single_line:suggestion
csharp_style_expression_bodied_properties = when_on_single_line:suggestion
csharp_style_expression_bodied_ctors = when_on_single_line:suggestion

# Prefer file-scoped namespaces
csharp_style_namespace_declarations = file_scoped:suggestion

# Prefer explicit tuple names
dotnet_style_explicit_tuple_names = true:suggestion

# Prefer pattern matching
csharp_style_pattern_matching_over_is_with_cast_check = true:suggestion
csharp_style_pattern_matching_over_as_with_null_check = true:suggestion

# Prefer 'is not null' over '!= null'
csharp_style_prefer_is_not_expression = true:suggestion

# Prefer using directives outside namespaces (C# 10+)
csharp_using_directive_placement = outside_namespace:suggestion

# Prefer readonly for fields where possible
dotnet_style_readonly_field = true:suggestion

# Prefer const for constants
dotnet_style_prefer_const_literals = true:suggestion

# Prefer explicit access modifiers
dotnet_style_require_accessibility_modifiers = always:suggestion

# Remove duplicate or redundant rules

# Suppress ConfigureAwait(false) analyzer warning
# CA2007: Do not directly await a Task without calling ConfigureAwait
[*.cs]
dotnet_diagnostic.CA2007.severity = none


