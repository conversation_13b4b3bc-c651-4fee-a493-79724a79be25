﻿using System;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Nbg.AspNetCore.Http.Extensions;
using Nbg.Ewallet.Repository.HttpClients;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Xunit;

namespace Nbg.Ewallet.Repository.Test.Integrations;

public class AccountsAPITest : IClassFixture<AccountApiTestFixture>
{
    private AccountApiTestFixture Fixture { get; }

    public AccountsAPITest(AccountApiTestFixture fixture)
    {
        Fixture = fixture;
    }

    [Fact]
    public async Task GetFullAccountsTest()
    {
        var provider = Fixture.ServiceProvider;
        // Arrange
        var configuration = provider.GetRequiredService<IOptions<AccountsApiClientSettings>>();
        var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
        var serviceAuditRepositoryService = new Mock<IServiceAuditRepositoryService>();
        var ibanAccountUtilityMock = new Mock<IIbanAccountUtility>();
        var loggerMock = new Mock<ILogger<AccountsApiClientService>>();
        loggerMock
            .Setup(x => x.Log(It.IsAny<LogLevel>(), It.IsAny<EventId>(), It.Is<It.IsAnyType>((_, __) => true), It.IsAny<Exception>(), It.IsAny<Func<It.IsAnyType, Exception, string>>()))
            .Callback<LogLevel, EventId, object, Exception, Delegate>((level, eventId, state, exception, formatter) =>
            {
                var invoke = formatter as Func<object, Exception, string>;
                var message = invoke?.Invoke(state, exception);
                Console.WriteLine($"[{level}] {message}");
            });

        var accountsApiClientService = new AccountsApiClientService(
            configuration,
            httpClientFactory,
            serviceAuditRepositoryService.Object,
            null,
            ibanAccountUtilityMock.Object,
            loggerMock.Object);

        var request = new AccountsFullRequest
        {
            //UserID = "PLAYVS003200002",
            UserID = "PLASMA003600001",
            //ConnectedUserID = "connectedUser",
            OwnAccounts = true,
            IsCorporateUser = false,
            IncludeBeneficiaries = false,
            IncludeStatements = false,
            CheckConnectToLoan = false
        };
        // Act
        var response = await accountsApiClientService.GetAccountsFullAsync(request);
        // Assert
        Assert.NotNull(response);
        //Assert.Equal(expectedResponse.Accounts.Length, response.Accounts.Length);
    }

    [Fact]
    public async Task ValidateAccountIsAvailableToUser()
    {
        var provider = Fixture.ServiceProvider;
        // Arrange
        var configuration = provider.GetRequiredService<IOptions<AccountsApiClientSettings>>();
        var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
        var serviceAuditRepositoryService = new Mock<IServiceAuditRepositoryService>();
        var ibanAccountUtilityMock = new Mock<IIbanAccountUtility>();
        var loggerMock = new Mock<ILogger<AccountsApiClientService>>();
        loggerMock
            .Setup(x => x.Log(It.IsAny<LogLevel>(), It.IsAny<EventId>(), It.Is<It.IsAnyType>((_, __) => true), It.IsAny<Exception>(), It.IsAny<Func<It.IsAnyType, Exception, string>>()))
            .Callback<LogLevel, EventId, object, Exception, Delegate>((level, eventId, state, exception, formatter) =>
            {
                var invoke = formatter as Func<object, Exception, string>;
                var message = invoke?.Invoke(state, exception);
                Console.WriteLine($"[{level}] {message}");
            });

        var accountsApiClientService = new AccountsApiClientService(
            configuration,
            httpClientFactory,
            serviceAuditRepositoryService.Object,
            null,
            ibanAccountUtilityMock.Object,
            loggerMock.Object);

        var request = new AccountsFullRequest
        {
            //UserID = "PLAYVS003200002",
            UserID = "PLASMA003600001",
            //ConnectedUserID = "connectedUser",
            Account = "****************************",
            OwnAccounts = true,
            IsCorporateUser = false,
            IncludeBeneficiaries = false,
            IncludeStatements = false,
            CheckConnectToLoan = false
        };
        // Act
        var response = await accountsApiClientService.GetAccountsFullAsync(request);
        // Assert
        Assert.NotNull(response);
        //Assert.Equal(expectedResponse.Accounts.Length, response.Accounts.Length);
    }

}

public class AccountApiTestFixture
{
    public IServiceProvider ServiceProvider { get; }

    public IConfigurationRoot Configuration { get; }

    public AccountApiTestFixture()
    {
        ServiceCollection services = new ServiceCollection();
        Configuration = CreateConfigurationRoot();

        services.AddLogging(builder =>
        {
            builder.AddConsole(); // Enables console output
            builder.SetMinimumLevel(LogLevel.Trace); // Capture all logs (Trace and above)
        });


        services.AddSingleton<IConfiguration>(Configuration);

        services.Configure<AccountsApiClientSettings>(Configuration.GetSection("AccountsApiClientSettings"));

        services.AddHttpClient("accountsApi", "HttpClient:accountsApi");

        ServiceProvider = services.BuildServiceProvider();
    }

    public static IConfigurationRoot CreateConfigurationRoot()
    {
        return new ConfigurationBuilder()
                    .SetBasePath(LocalPath)
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .Build();
    }

    public static string LocalPath => Path.GetDirectoryName(new Uri(Assembly.GetExecutingAssembly().Location).LocalPath);
}
