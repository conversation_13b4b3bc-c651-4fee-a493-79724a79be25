using FluentAssertions;
using SandboxTest.Infrastructure;
using System.Net;
using Xunit;

namespace SandboxTest.Tests
{
    public class WalletTests : SandboxTestBase
    {
        [Fact(DisplayName = "Register Wallet - Should create a new wallet and return wallet details")]
        public async Task RegisterWallet_ShouldCreateNewWallet_AndReturnWalletDetails()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a new wallet with a unique name
             * 2. Verify the API returns success and wallet details
             * 3. Verify the wallet can be retrieved using wallet info endpoint
             * 
             * Expected Result:
             * - Wallet is created with the specified name
             * - Wallet has a valid ID and account information
             * - Wallet is retrievable via the wallet info endpoint
             */
            var walletName = $"Test Wallet {Guid.NewGuid()}";
            var registerRequest = new
            {
                WalletName = walletName
            };

            // Act
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);

            // Assert
            registerResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            var wallet = await DeserializeResponseAsync<WalletResponse>(registerResponse);

            wallet.Should().NotBeNull();
            wallet!.WalletId.Should().NotBeEmpty();
            wallet.WalletName.Should().Be(walletName);
            wallet.WalletAccount.Should().NotBeNull();
            wallet.WalletAccount.Should().StartWith("GR");

            // Verify wallet can be retrieved
            var getWalletResponse = await Client.GetAsync($"wallet/{wallet.WalletId}");
            getWalletResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            var retrievedWallet = await DeserializeResponseAsync<WalletResponse>(getWalletResponse);
            retrievedWallet.Should().NotBeNull();
            retrievedWallet!.WalletId.Should().Be(wallet.WalletId);
            retrievedWallet.WalletName.Should().Be(walletName);
        }

        [Fact(DisplayName = "Register Wallet - Should fail with empty wallet name")]
        public async Task RegisterWallet_ShouldFail_WithEmptyWalletName()
        {
            // Arrange
            /* Test Logic:
             * 1. Attempt to create a wallet with an empty name
             * 2. Verify the API returns a validation error
             * 
             * Expected Result:
             * - API returns a 400 Bad Request status code
             * - Error message indicates the wallet name is required
             */
            var registerRequest = new
            {
                WalletName = string.Empty
            };

            // Act
            var response = await Client.PostAsJsonAsync("wallet/register", registerRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var error = await DeserializeResponseAsync<ErrorResponse>(response);
            error.Should().NotBeNull();
            error!.Message.Should().Contain("wallet name");
        }

        [Fact(DisplayName = "Get Wallet - Should return wallet details for valid wallet ID")]
        public async Task GetWallet_ShouldReturnWalletDetails_ForValidWalletId()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a new wallet
             * 2. Retrieve the wallet using its ID
             * 3. Verify the retrieved wallet details match the created wallet
             * 
             * Expected Result:
             * - API returns the correct wallet details for the given wallet ID
             */
            var walletName = $"Test Wallet {Guid.NewGuid()}";
            var registerRequest = new
            {
                WalletName = walletName
            };

            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var wallet = await DeserializeResponseAsync<WalletResponse>(registerResponse);

            // Act
            var getWalletResponse = await Client.GetAsync($"wallet/{wallet!.WalletId}");

            // Assert
            getWalletResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            var retrievedWallet = await DeserializeResponseAsync<WalletResponse>(getWalletResponse);

            retrievedWallet.Should().NotBeNull();
            retrievedWallet!.WalletId.Should().Be(wallet.WalletId);
            retrievedWallet.WalletName.Should().Be(walletName);
            retrievedWallet.WalletAccount.Should().Be(wallet.WalletAccount);
        }

        [Fact(DisplayName = "Get Wallet - Should return not found for invalid wallet ID")]
        public async Task GetWallet_ShouldReturnNotFound_ForInvalidWalletId()
        {
            // Arrange
            /* Test Logic:
             * 1. Attempt to retrieve a wallet using a non-existent ID
             * 2. Verify the API returns a not found error
             * 
             * Expected Result:
             * - API returns a 404 Not Found status code
             */
            var invalidWalletId = Guid.NewGuid().ToString();

            // Act
            var response = await Client.GetAsync($"wallet/{invalidWalletId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact(DisplayName = "List Wallets - Should return all registered wallets")]
        public async Task ListWallets_ShouldReturnAllRegisteredWallets()
        {
            // Arrange
            /* Test Logic:
             * 1. Create multiple wallets
             * 2. Retrieve the list of all wallets
             * 3. Verify the list contains the created wallets
             * 
             * Expected Result:
             * - API returns a list containing all created wallets
             */
            var wallet1Name = $"Test Wallet 1 {Guid.NewGuid()}";
            var wallet2Name = $"Test Wallet 2 {Guid.NewGuid()}";

            await Client.PostAsJsonAsync("wallet/register", new { WalletName = wallet1Name });
            await Client.PostAsJsonAsync("wallet/register", new { WalletName = wallet2Name });

            // Act
            var response = await Client.GetAsync("wallet");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var wallets = await DeserializeResponseAsync<List<WalletResponse>>(response);

            wallets.Should().NotBeNull();
            wallets!.Should().HaveCountGreaterThanOrEqualTo(2);
            wallets.Should().Contain(w => w.WalletName == wallet1Name);
            wallets.Should().Contain(w => w.WalletName == wallet2Name);
        }
    }

    // Response models for deserialization
    public class WalletResponse
    {
        public Guid WalletId { get; set; }
        public string WalletName { get; set; } = string.Empty;
        public string WalletAccount { get; set; } = string.Empty;
        public string OrganizationName { get; set; } = string.Empty;
        public string RegistrationDate { get; set; } = string.Empty;
        public string VatNumber { get; set; } = string.Empty;
        public string OwnerUserId { get; set; } = string.Empty;
    }

    public class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
    }
}
