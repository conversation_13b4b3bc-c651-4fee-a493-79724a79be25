{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },

    "ConnectionStrings": {
        "Audit": "Server=v000080065;database=InternetBankingAudit;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "IBank": "Server=v000080065;database=InternetBanking;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "uniqueMessage": "Server=V000080065;initial catalog=BigDataIbank;Integrated Security=SSPI;Persist Security Info=False;Encrypt=false",
        "appSettings": "Server=v000010349\\S1;database=APIConfig;Integrated Security=SSPI;Persist Security Info=False;Encrypt=false"
    },

    "AuditMiddleware": {
        "ConnectionName": "Audit",
        "UseSqlServer": true,
        "UseBigDataClient": false,
        "SetPrimarySqlServer": true,
        "SetPrimaryBigDataClient": false,
        "RequestIdHeaderName": "request-id",
        "ServiceNamePrefix": "nbg.ewallet.api/v1",
        "IgnoredPaths": [
            "/favicon.ico"
        ],
        "RequestHttpHeaders": [
            "ByPassHeaders"
        ],
        "ResponseHttpHeaders": [
            "ByPassHeaders"
        ],
        "SensitiveProperties": []
    },

    "MultiSinkAuditWriter": {
        "primarySink": "kafka",
        "sinks": {
            "kafka": {
                "kafkaClient": {
                    "kafkaTopic": "dev-trlog-ibank-ingestion-16partitions",
                    "producerOptions": {
                        "bootstrap.servers": "pkc-1wvvj.westeurope.azure.confluent.cloud:9092",
                        "security.protocol": "Plaintext",
                        "sasl.username": "",
                        "sasl.mechanism": "PLAIN",
                        "delivery.timeout.ms": "5000",
                        "batch.size": "********",
                        "message.max.bytes": "********",
                        "acks": "1",
                        "compression.type": "snappy",
                        "linger.ms": "50"
                    }
                }
            }
        }
    },

    "Authentication": {
        "Authority": "https://myqa.nbg.gr/identity",
        "ApiName": "D8864CC2-56C2-43EB-ABD9-AF10484A2A20",
        "ApiSecret": "DFCABCA5-9BEC-4792-A8B8-134C6D7ACF01",
        "RequiredScope": "ewallet-api-v1",
        "EnableCaching": true,
        "CacheDuration": "00:30:00"
    },

    "ProxyMiddleware": {
        "Variables": {
            "CoreServiceUrl": "https://localhost/Nbg.Ewallet.Api"
            //"CoreServiceUrl": "https://localhost/Nbg.Ewallet.Api.Sandbox"

        }
    },

    "Healthcheck": {
        "CheckAllocatedMemory": true,
        "CheckUris": true,
        "UriCheckOptions": [
            {
                "Uri": "https://localhost/Nbg.Ewallet.Api/health",
                //"Uri": "https://localhost/Nbg.Ewallet.Api.Sandbox/health",
                "HttpClientName": "default",
                "HttpMethod": "GET",
                "TimeOutMilliseconds": 100000,
                "ExpectedCodes": [ 200 ]
            }
        ]
    },

    "CorporateUserAuthorizationPolicy": {
        "ApplyPolicy": false
    },

    "Sca": {
        "BaseAddress": "https://CoreAppLayerQA.nbg.gr",
        "ChallengeEndpoint": "/Nbg.NetCore.Sca.Core/sca/challenge",
        "ValidateEndpoint": "/Nbg.NetCore.Sca.Core/sca/validate",
        "DisableScaWhitelisting": false,
        "ScaTokenExpirationInSeconds": 300
    },

    //"CustomRestSca": {
    //    "BaseAddress": "https://V00008A114", //"https://coreapplayerqa.nbg.gr/", //"http://localhost/"
    //    "ChallengeEndpoint": "/Nbg.NetCore.Sca.Core/sca/challenge",
    //    "ValidateEndpoint": "/Nbg.NetCore.Sca.Core/sca/validate",
    //    "DisableScaWhitelisting": false,
    //    "ScaTokenExpirationInSeconds": 120,
    //    "ApplicationId": "07255F0F-5BCC-4254-90B6-75E70A487333"
    //},

    //"HttpClient": {
    //    "ClientRestSca": {
    //        "BaseAddress": "https://V00008A114", //"BaseAddress": "https://localhost/Nbg.NetCore.Sca.Core/",
    //        "MaxConnectionsPerServer": 20,
    //        "DangerousAcceptAnyServerCertificate": false,
    //        "Timeout": "00:03:00",
    //        "UseProxy": false,
    //        "ProxyUrl": "http://wsa.central.nbg.gr:8080"
    //    }
    //},

    "SmsOtpSettings": {
        "EnableSmsOtp": false
    },
    "UserIdRequestTransformer": {
        "HttpHeadersMappings": {
            "UserId": {
                "SourcePath": "ibank_user_id",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true,
                "Source": "Claim",
                "ThrowIfNotFoundFromSource": true
            }
        }
    }
}
