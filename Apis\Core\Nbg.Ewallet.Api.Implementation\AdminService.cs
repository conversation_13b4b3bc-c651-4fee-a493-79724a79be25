﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Admin;

namespace Nbg.Ewallet.Api.Implementation;

public class AdminService : IAdminService
{
    public Task<ConfigureB2bRulesResponse> ConfigureB2BRules(ConfigureB2bRulesRequest request)
    {
        throw new NotImplementedException();
    }

    public Task<ConfigureB2ChannelsRequest> ConfigureB2Channels(ConfigureB2ChannelsRequest request)
    {
        throw new NotImplementedException();
    }

    public Task<Terms> GetTerms()
    {
        throw new NotImplementedException();
    }

    public Task<CommonSuccessResult<object>> UploadTerms(Terms request)
    {
        throw new NotImplementedException();
    }
}
