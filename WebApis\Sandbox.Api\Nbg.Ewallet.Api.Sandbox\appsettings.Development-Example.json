{"ConnectionStrings": {"SandboxApps": "Server=localhost, 1433;database=SandboxApps;User ID=sa;Password=YourStrong(!Passw0rd);Integrated Security=false;MultipleActiveResultSets=true;TrustServerCertificate=True;Encrypt=False;"}, "SandboxServiceConfig": {"Sandbox": "SandboxApps", "StorageService": "SQLServer"}, "UrlSettings": {"AuthorizationUrl": "https://my.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope=sandbox-ewallet-api-v1&redirect_uri={{redirect_uri}}&response_type=code"}, "ConsentConnectorSettings": {"ConsentsApiUrl": "https://coreapplayerqa.nbg.gr/consent.core.api/", "ApplicationConsentTemplateId": "723948c8-1607-40b8-b81a-ebff8b3ed864"}, "NLog": {"autoReload": true, "throwExceptions": false, "throwConfigExceptions": true, "internalLogLevel": "Off", "internalLogFile": "c:/logs/internal-nlog.txt", "globalThreshold": "Trace", "variables": {"logRootFolder": "c:/logs", "applicationName": "EwalletSandbox"}, "time": {"type": "AccurateUTC"}, "default-wrapper": {"type": "AsyncWrapper", "overflowAction": "Block"}, "targets": {"async": true, "logFile": {"type": "File", "fileName": "${logRootFolder}/nlog-${applicationName}-${shortdate}.log"}, "logEvent": {"type": "EventLog", "log": "${applicationName}", "source": "${applicationName}", "layout": "${callsite:className=true:includeNamespace=true:includeSourcePath=true:methodName=true} ${message}${newline}${exception:format=ToString}"}, "debugger": {"type": "EventLog", "layout": "${callsite:className=true:includeNamespace=true:includeSourcePath=true:methodName=true} ${message}${newline}${exception:format=ToString}"}}, "rules": [{"logger": "Microsoft.*", "maxLevel": "Error", "final": true}, {"logger": "*", "minLevel": "Debug", "writeTo": "logEvent"}, {"logger": "*", "minLevel": "Trace", "writeTo": "debugger"}]}}