using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Implementation;

public class WalletService : IWalletService
{
    private readonly IMapper _mapper;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly ILogger<WalletService> _logger;
    private readonly IBigDataAzureClientService _azureClient;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly ITransactionRepositoryService _transactionRepositoryService;
    private readonly IAccountsApiClientService _accountsApiClientService;
    private readonly IIbanAccountUtility _ibanAccountUtility;

    public WalletService(
        ILogger<WalletService> logger,
        IMapper mapper,
        IWalletRepositoryService walletRepositoryService,
        IHttpContextRepositoryService httpContextRepositoryService,
        IBigDataAzureClientService bigDataAzureClientRepositoryService,
        ITransactionRepositoryService transactionRepositoryService,
        IAccountsApiClientService accountsApiClientService,
        IIbanAccountUtility ibanAccountUtility
    )
    {
        _logger = logger;
        _mapper = mapper;
        _walletRepositoryService = walletRepositoryService;
        _httpContextRepositoryService = httpContextRepositoryService;
        _azureClient = bigDataAzureClientRepositoryService;
        _transactionRepositoryService = transactionRepositoryService;
        _accountsApiClientService = accountsApiClientService;
        _ibanAccountUtility = ibanAccountUtility;
    }

    public async Task<GetWalletBalanceResponse> GetWalletBalance(Guid walletId)
    {
        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString());
        var userId = _httpContextRepositoryService.GetUserId();
        var accountBalance = await _accountsApiClientService.GetAccountBalanceAsync(new GetAccountBalanceRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(wallet.WalletAccount),
            UserId = userId,
            Currency = "070"
        });

        return new GetWalletBalanceResponse { AvailableBalance = accountBalance.AvailableBalance, LedgerBalance = accountBalance.LedgerBalance };
    }

    public async Task<Wallet> GetWalletInformation(Guid walletId)
    {
        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString());
        return _mapper.Map<Wallet>(wallet);
    }

    public async Task<Wallet> EditWalletName(Guid walletId, EditWalletNameRequest request)
    {
        var wallet = await _walletRepositoryService.UpdateWalletNameByWalletIdAsync(walletId, request.WalletName);

        return _mapper.Map<Wallet>(wallet);
    }

    public async Task<WalletLoadResponse> Load(Guid walletId, WalletLoadRequest request)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var isUserAccount = await _accountsApiClientService.ValidateUserAccount(userId, request.Iban);
        if (isUserAccount == false)
        {
            _logger.LogWarning("User account validation failed for user ID {UserId} and IBAN {IBAN}", userId, request.Iban);
            throw new AccountNotFoundException();
        }

        var targetWallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString());
        var transfersRequest = new TransfersRequest
        {
            Amount = request.Amount,
            Currency = "EUR",
            ReceiverAcc = targetWallet.WalletAccount,
            ReceiverGR = targetWallet.WalletName,
            ReceiverAccType = ReceiverAccType.IBAN,
            RemType = targetWallet.WalletAccount.GetRemType(),
            TransferType = targetWallet.WalletAccount.GetTransferType(),
            DebitAccount = request.Iban,
            Reason = "ΜΕΤΑΦΟΡΑ ΦΟΡΤΙΣΗΣ WALLET",
            InstantSend = true,
            BeneficiaryBic = AccountHelpers.NbgBic,
            IsCorporateUser = targetWallet.IsCorporateUser,
            SYDIPEL = targetWallet.OwnerCustomerCode,
            UserID = userId,
        };
        var response = await _accountsApiClientService.ExecuteTransactionAsync(transfersRequest);

        var tranasction = new RepoTransaction
        {
            Amount = request.Amount,
            ApprovedBy = null,
            BatchId = null,
            Commission = 0,
            CreatedAt = DateTime.UtcNow,
            CreditorIban = targetWallet.WalletAccount,
            CreditorName = targetWallet.OrganizationName,
            Currency = "EUR",
            DebtorIban = request.Iban,
            DebtorName = targetWallet.OrganizationName,
            ExecutedAs = userId,
            ExecutedBy = userId,
            IsInstant = null,
            Reason = "ΜΕΤΑΦΟΡΑ ΦΟΡΤΙΣΗΣ WALLET",
            Reference = response.Exception == null ? response.Payload.ReferenceNumber : null,
            RejectedBy = null,
            RequestJson = null,
            Result = response.Exception == null ? "Success" : "Failure",
            ResultReason = response.Exception == null ? null : response.Exception.Description,
            Status = "EXECUTED",
            SubmittedBy = userId,
            TransactionDate = DateTime.UtcNow,
            TransactionId = Guid.NewGuid(),
            TransactionSubType = TransactionSubType.WalletLoad,
            TransactionType = TransactionType.Transfer,
            UpdatedAt = DateTime.UtcNow,
            Valeur = response.Exception == null ? response.Payload.Valeur : null,
            WalletId = walletId
        };

        var transctionsList = new List<RepoTransaction> { tranasction };

        await _transactionRepositoryService.SaveAllAsync(transctionsList);

        if (response.Exception != null)
        {
            throw response.Exception.Code switch
            {
                "020" => new TransferExecutionExceptionInput($"{response.Exception.Description}"),
                "728" or "067" => new TransferExecutionExceptionBalance($"{response.Exception.Description}"),
                _ => new TransferExecutionException($"{response.Exception.Description}"),
            };
        }

        var balanceResponse = await _accountsApiClientService.GetAccountBalanceAsync(new GetAccountBalanceRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(targetWallet.WalletAccount),
            UserId = userId,
            Currency = "EUR"
        });

        return new WalletLoadResponse() { Balance = balanceResponse.AvailableBalance, Transaction = tranasction.MapToTransaction() };
    }

    public async Task<WalletLoadResponse> Unload(Guid walletId, WalletLoadRequest request)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var isUserAccount = await _accountsApiClientService.ValidateUserAccount(userId, request.Iban);
        if (isUserAccount == false) throw new AccountNotFoundException();

        var targetWallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString());
        var transfersRequest = new TransfersRequest
        {
            Amount = request.Amount,
            ReceiverAcc = request.Iban,
            RemType = request.Iban.GetRemType(),
            TransferType = request.Iban.GetTransferType(),
            ReceiverAccType = ReceiverAccType.IBAN,
            DebitAccount = targetWallet.WalletAccount,
            DebitAccountCurrency = "EUR",
            Reason = "ΜΕΤΑΦΟΡΑ ΑΝΑΛΗΨΗΣ ΑΠΟ WALLET",
            InstantSend = true,
            BeneficiaryBic = AccountHelpers.NbgBic,
            SYDIPEL = targetWallet.OwnerCustomerCode,
            IsCorporateUser = targetWallet.IsCorporateUser,
            UserID = userId,
        };
        var response = await _accountsApiClientService.ExecuteTransactionAsync(transfersRequest);

        var tranasction = new RepoTransaction
        {
            Amount = request.Amount,
            ApprovedBy = null,
            BatchId = null,
            Commission = 0,
            CreatedAt = DateTime.UtcNow,
            CreditorIban = request.Iban,
            CreditorName = targetWallet.OrganizationName,
            Currency = "EUR",
            DebtorIban = targetWallet.WalletAccount,
            DebtorName = targetWallet.OrganizationName,
            ExecutedAs = userId,
            ExecutedBy = userId,
            IsInstant = null,
            Reason = "ΜΕΤΑΦΟΡΑ ΑΝΑΛΗΨΗΣ ΑΠΟ WALLET",
            Reference = response.Exception == null ? response.Payload.ReferenceNumber : null,
            RejectedBy = null,
            RequestJson = null,
            Result = response.Exception == null ? "Success" : "Failure",
            ResultReason = response.Exception == null ? null : response.Exception.Description,
            Status = "EXECUTED",
            SubmittedBy = userId,
            TransactionDate = DateTime.UtcNow,
            TransactionId = Guid.NewGuid(),
            TransactionSubType = TransactionSubType.WalletWithdraw,
            TransactionType = TransactionType.Transfer,
            UpdatedAt = DateTime.UtcNow,
            Valeur = response.Exception == null ? response.Payload.Valeur : null,
            WalletId = walletId
        };

        await _transactionRepositoryService.SaveAllAsync([tranasction]);

        if (response.Exception != null)
        {
            throw response.Exception.Code switch
            {
                "020" => new TransferExecutionExceptionInput($"{response.Exception.Description}"),
                "728" or "067" => new TransferExecutionExceptionBalance($"{response.Exception.Description}"),
                _ => new TransferExecutionException($"{response.Exception.Description}"),
            };
        }

        var balanceResponse = await _accountsApiClientService.GetAccountBalanceAsync(new GetAccountBalanceRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(targetWallet.WalletAccount),
            UserId = userId,
            Currency = "EUR"
        });

        return new WalletLoadResponse() { Balance = balanceResponse.AvailableBalance, Transaction = tranasction.MapToTransaction() };
    }

    public async Task<List<WalletLite>> SearchWallets(string vatNumber, string walletName, string iban, string organizationName)
    {
        if (vatNumber == null && iban == null && walletName == null && organizationName == null)
        {
            throw new InvalidInputException();
        }

        var wallets = await _walletRepositoryService.FindAllByParamsAsync(vatNumber, iban, walletName, organizationName);
        var response = _mapper.Map<List<WalletLite>>(wallets);
        return response;
    }

    //TODO: maybe remove this and use separate controller
    //public async Task<Transaction> ApproveTransaction(Guid walletId, string transactionId)
    //{
    //    return await _transactionService.ApproveAsync(walletId, transactionId);
    //}

    //public async Task<Transaction> RejectTransaction(Guid walletId, string transactionId)
    //{
    //    return await _transactionService.RejectAsync(walletId, transactionId);
    //}

    public async Task<List<Transaction>> GetWalletTransactions(Guid walletId, TransactionRequestQueryParams request)
    {
        if (request.PageNumber < 0) throw new ArgumentOutOfRangeException(nameof(request.PageNumber), "Page number must be 0 or greater.");
        if (request.PageSize <= 0) throw new ArgumentOutOfRangeException(nameof(request.PageSize), "Page size must be greater than zero.");

        var transactions = await _transactionRepositoryService.FindAllByWalletIdAndBatchIdAndTransactionStatusAsync
            (walletId.ToString(),
            request.BatchId,
            request.Status,
            request.PageNumber,
            request.PageSize);

        return [.. transactions.Select(t => t.MapToTransaction())];
    }

    public async Task<WalletStatementsResponse> GetWalletStatements(Guid walletId, WalletStatementsRequest request)
    {
        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString()) ?? throw new WalletNotFoundException();
        var azureStatementsRequest = new AzureStatementsRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(wallet.WalletAccount),
            DateFrom = request.DateFrom?.GetDottedDate(),
            DateTo = request.DateTo?.GetDottedDate(),
            LastRowKey = request.PaginationToken,
            Limit = request.Limit,
            UserId = wallet.OwnerUserId,
            System = "E"
        };
        AzureStatementsResponse azureStatementsResponse = await _azureClient.GetStatementsAsync(azureStatementsRequest);
        var response = _mapper.Map<WalletStatementsResponse>(azureStatementsResponse);
        if (azureStatementsResponse.Transactions.Any()) response.PaginationToken = azureStatementsResponse.PaginationToken;
        return response;
    }

    public async Task<List<Transaction>> GetMyTransactionsAsync(Guid walletId)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var transactions = await _transactionRepositoryService.FindAllByWalletIdAndSubmittedByOrExecutedByOrApprovedByAsync(walletId.ToString(), userId);
        return transactions.Select(t => t.MapToTransaction()).ToList();
    }

    public async Task<byte[]> GetWalletStatementPdfExport(Guid walletId, StatementRequestQueryParams request)
    {
        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString());
        var userId = _httpContextRepositoryService.GetUserId();
        var account = await _ibanAccountUtility.GetAccountFromIbanAsync(wallet.WalletAccount);
        return await _accountsApiClientService.RetrieveWalletStatementPdfExport(userId, account, request);
    }

    public async Task<AvailableAccountsResponse> GetUserAccountsAsync(Guid walletId)
    {
        var wallet = await _walletRepositoryService.FindOneByIdAsync(walletId.ToString());
        var userId = _httpContextRepositoryService.GetUserId();

        var request = new AccountsFullRequest
        {
            UserID = userId,
            OwnAccounts = true,
            IsCorporateUser = wallet.IsCorporateUser,
            IncludeBeneficiaries = false,
            IncludeStatements = false,
            CheckConnectToLoan = false
        };

        var accountsResponse = await _accountsApiClientService.GetAccountsFullAsync(request);

        return accountsResponse.ToAvailableAccountsResponse();
    }
}
