{"id": "b2d60f37-61ba-41ea-89a4-c539ec38987c", "name": "eWallet API - QA LIVE", "values": [{"key": "access-token-url", "value": "https://myqa.nbg.gr/identity/connect/token", "type": "default", "enabled": true}, {"key": "auth-url", "value": "https://myqa.nbg.gr/identity/connect/authorize", "type": "default", "enabled": true}, {"key": "url_https_erp", "value": "https://ibankretailqa.nbg.gr/api.gateway/ibank.erp.oauth2.api.v2.2/api", "type": "default", "enabled": true}, {"key": "url_https", "value": "https://ibankretailqa.nbg.gr/apis.proxy.myqa/ewallet.proxy.api/v1", "type": "default", "enabled": true}, {"key": "url_https_bill_payments", "value": "https://ibankretailqa.nbg.gr/apis.proxy.myqa/billpaymentsapi/v1", "type": "default", "enabled": true}, {"key": "callback-url", "value": "https://www.getpostman.com/oauth2/callback/", "type": "default", "enabled": true}, {"key": "client-id", "value": "9E7EBA58-3A10-4675-948F-903228854E25", "type": "default", "enabled": true}, {"key": "client-secret", "value": "1148C59F-0F80-4317-A4A3-E866B26DB2B7", "type": "default", "enabled": true}, {"key": "walletId", "value": "", "type": "any", "enabled": true}, {"key": "scope", "value": "", "type": "default", "enabled": true}, {"key": "userId", "value": "", "type": "any", "enabled": true}, {"key": "authorizationWalletId", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid0", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid1", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid2", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid3", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid4", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid5", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid6", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid7", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid8", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid9", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid10", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid11", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid12", "value": "", "type": "any", "enabled": true}, {"key": "walletuserid13", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId0", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId1", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId2", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId3", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId4", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId5", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId6", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId7", "value": "", "type": "any", "enabled": true}, {"key": "pendingTransactionId8", "value": "", "type": "any", "enabled": true}, {"key": "companyAccount0", "value": "", "type": "any", "enabled": true}, {"key": "companyAccount1", "value": "", "type": "any", "enabled": true}, {"key": "companyAccount2", "value": "", "type": "any", "enabled": true}, {"key": "companyAccount3", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-07-03T09:56:32.889Z", "_postman_exported_using": "Postman/11.2.27"}