﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      throwExceptions="false"
      throwConfigExceptions="true"
      internalLogLevel="Off"
      internalLogFile="c:\logs\nlog-internal.log"
      globalThreshold="Trace">
    <!-- USE globalThreshold="Off" to turn off ALL tracing -->

    <extensions>
        <add assembly="Nlog.WindowsEventLog" />
    </extensions>

    <variable name="logRootFolder" value="c:\logs\ewallet" />
    <variable name="applicationName" value="ewallet" />

    <targets>

        <target xsi:type="EventLog"
                name="eventLog"
                log="${applicationName}"
                source="${applicationName}"
                layout="${callsite:className=true:includeNamespace=true:includeSourcePath=true:methodName=true} ${message}${newline}${exception:format=ToString}" />

        <target xsi:type="File"
                 name="filelogger"
                 fileName="${logRootFolder}\logs-${shortdate}.log"
                 layout="${longdate}|${aspnet-traceidentifier}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}" />
    </targets>

    <rules>
        <logger
          name="*"
          minlevel="Warn"
          writeTo="eventLog">
        </logger>

        <logger
          name="*"
          minlevel="Warn"
          writeTo="eventLog" />

        <logger
          name="Filelogger"
          minlevel="Info"
          writeTo="filelogger" />
    </rules>
</nlog>
