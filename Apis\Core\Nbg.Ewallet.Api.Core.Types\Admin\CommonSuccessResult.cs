﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace Nbg.Ewallet.Api.Types.Admin;

/// <summary>
/// Status Response
/// </summary>
[DataContract]
public class CommonSuccessResult<T>
{
    /// <summary>
    /// Request Status Response
    /// </summary>
    [DataMember(Name = "success")]
    public bool Success { get; set; }

    /// <summary>
    /// Associated payload
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [DataMember(Name = "payload")]
    public T Payload { get; set; }
}
