﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

public class B2BSearchResponse
{
    /// <summary>
    /// Company Name used inside wallet environment
    /// </summary>
    [DataMember(Name = "name")]
    public string name { get; set; }

    /// <summary>
    /// Company vat number
    /// </summary>
    [DataMember(Name = "vatNumber")]
    public string vatNumber { get; set; }

    /// <summary>
    /// Company’s wallet account
    /// </summary>
    [DataMember(Name = "walletAccount")]
    public string walletAccount { get; set; }
}
