﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;

namespace Nbg.Ewallet.Api.Controllers
{
    /// <summary>
    /// Retrieves if a specific vatNumber or company Name is registered in Wallet, or if a specific wallet account iban exists inside wallet
    /// </summary>
    [Produces("application/json")]
    [Consumes("application/json")]
    [ApiExplorerSettings(GroupName = "B2B", IgnoreApi = true)]
    public class B2BController : BaseController
    {
        private readonly IB2BService _b2BService;
        public B2BController(IB2BService b2BService, ILogger<B2BController> logger) : base(logger)
        {
            _b2BService = b2BService;
        }

        /// <summary>
        /// Search
        /// </summary>
        [HttpPost]
        [Route("search", Name = "Search")]
        public async Task<ActionResult<B2BSearchResponse>> Search(B2BSearchRequest request)
        {
            return await _b2BService.Search(request);
        }
    }
}
