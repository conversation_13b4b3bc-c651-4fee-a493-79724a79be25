USE EWallet
GO

-- Alter TransactionType from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN TransactionType nvarchar(255) NULL;
GO

-- Alter TransactionSubType from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN TransactionSubType nvarchar(255) NULL;
GO

-- Alter Currency from nvarchar(3) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN Currency nvarchar(255) NOT NULL;
GO

-- Alter DebtorIban from nvarchar(50) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN DebtorIban nvarchar(255) NOT NULL;
GO

-- Alter Reference from nvarchar(50) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN Reference nvarchar(255) NULL;
GO

-- Alter <PERSON>ban from nvarchar(50) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN CreditorIban nvarchar(255) NOT NULL;
GO

-- Alter CreditorName from nvarchar(50) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN CreditorName nvarchar(255) NOT NULL;
GO

-- Alter Reason from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN Reason nvarchar(255) NOT NULL;
GO

-- Alter Status from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN Status nvarchar(255) NOT NULL;
GO

-- Alter SubmittedBy from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN SubmittedBy nvarchar(255) NOT NULL;
GO

-- Alter ApprovedBy from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN ApprovedBy nvarchar(255) NULL;
GO

-- Alter ExecutedBy from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN ExecutedBy nvarchar(255) NULL;
GO

-- Alter ExecutedAs from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN ExecutedAs nvarchar(255) NULL;
GO

-- Alter RejectedBy from nvarchar(25) to nvarchar(255)
ALTER TABLE dbo.Transactions
ALTER COLUMN RejectedBy nvarchar(255) NULL;
GO