﻿using System.Collections.Generic;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static List<SandboxSubscriptionPlan> GenerateSubscriptionPlans()
    {
        return new List<SandboxSubscriptionPlan>
        {
            new SandboxSubscriptionPlan
            {
                Id = SubscriptionTier.Basic,
                discounts = new List<DiscountTransaction>
                {
                    {
                        new DiscountTransaction
                        {
                            TransactionType = SubscriptionTransactionType.Transfer,
                            Value = 1.5M
                        }
                    }
                }
            },
            {
                new SandboxSubscriptionPlan
                {
                    Id = SubscriptionTier.Premium,
                    discounts = new List<DiscountTransaction>
                    {
                        {
                            new DiscountTransaction
                            {
                                TransactionType = SubscriptionTransactionType.Transfer,
                                Value = 1.2M
                            }
                        }
                    }
                }
            }
        };
    }
}
