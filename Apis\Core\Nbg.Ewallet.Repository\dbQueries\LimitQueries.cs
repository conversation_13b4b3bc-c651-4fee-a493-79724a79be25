﻿namespace Nbg.Ewallet.Repository.dbQueries;

internal static class LimitQueries
{
    internal const string InsertUserLimits =
        "INSERT INTO [EWallet].[dbo].[Limits] " +
        "(PermissionId,TransactionType,Amount,TimeFrame) " +
        "VALUES (@PermissionId,@TransactionType,@Amount,@TimeFrame)";

    internal const string GetLimitByPermissionIdType =
        "SELECT Id, Amount, Active, TimeFrame, TransactionType, PermissionId " +
        "FROM [EWallet].[dbo].[Limits] " +
        "WHERE PermissionId = @PermissionId " +
        "AND TransactionType = @TransactionType";

    internal const string GetLimitByPermissionId =
        "SELECT Id, Amount, Active, TimeFrame, TransactionType, PermissionId " +
        "FROM [EWallet].[dbo].[Limits] " +
        "WHERE PermissionId = @PermissionId";

    internal const string GetUserLimits =
        "SELECT Id, Amount, Active, TimeFrame, TransactionType, PermissionId " +
        "FROM [EWallet].[dbo].[Limits] " +
        "WHERE [PermissionId] = @PermissionId";
}
