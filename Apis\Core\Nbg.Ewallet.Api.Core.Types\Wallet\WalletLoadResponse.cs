﻿using System.Runtime.Serialization;
using Nbg.Ewallet.Api.Types.Transfers;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Wallet Load Response
/// </summary>
public class WalletLoadResponse
{
    /// <summary>
    /// Account Balance after loading
    /// </summary>
    [DataMember(Name = "balance")]
    public decimal? Balance { get; set; }

    /// <summary>
    /// Transaction details after loading the wallet
    /// </summary>
    [DataMember(Name = "transaction")]
    public Transaction Transaction { get; set; }
}
