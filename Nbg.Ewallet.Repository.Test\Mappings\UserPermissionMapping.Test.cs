﻿using System;
using System.Collections.Generic;
using AutoMapper;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using Xunit;

namespace Nbg.Ewallet.Repository.Test.Mappings
{
    public class UserProfile : Profile
    {
        public UserProfile()
        {
            CreateMap<RepoUserPermission, UserPermission>()
                .ForMember(x => x.CreationDate, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(rw => rw.WalletId, opt => opt.MapFrom(src => src.WalletId))
                .ForMember(rw => rw.Id, opt => opt.MapFrom(src => src.Id));

            CreateMap<UserPermission, RepoUserPermission>()
                .ForMember(x => x.CreatedAt, opt => opt.MapFrom(src => src.CreationDate))
                .ForMember(x => x.Id, opt => opt.MapFrom(src => src.Id));
        }
    }

    public class UserPermissionMappingTest
    {
        private readonly IMapper _mapper;
        public UserPermissionMappingTest()
        {
            var config = new MapperConfiguration(cfg => cfg.AddProfile<UserProfile>());
            //config.AssertConfigurationIsValid();
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void TestMapping()
        {
            List<RepoUserPermission> repoPermissions =
                [
                new (){ Id = Guid.NewGuid(), UserId = "user1", WalletId = Guid.NewGuid(), ExpirationDate = DateTime.Now.AddDays(-100), Submit = true, Approve = false, BalanceView = true, TransactionView = true, Admin = false, InheritsAuthorizations = false, CreatedAt = DateTime.UtcNow, AssignedBy = "admin1", RevokedBy = null },
                new (){ Id = Guid.NewGuid(), UserId = "user2", WalletId = Guid.NewGuid(), ExpirationDate = DateTime.Now.AddDays(-50), Submit = true, Approve = false, BalanceView = true, TransactionView = true, Admin = false, InheritsAuthorizations = false, CreatedAt = DateTime.UtcNow, AssignedBy = "admin1", RevokedBy = null },
                new (){ Id = Guid.NewGuid(), UserId = "user3", WalletId = Guid.NewGuid(), ExpirationDate = DateTime.Now.AddDays(-10), Submit = true, Approve = false, BalanceView = true, TransactionView = true, Admin = false, InheritsAuthorizations = false, CreatedAt = DateTime.UtcNow, AssignedBy = "admin1", RevokedBy = null },
                new (){ Id = Guid.NewGuid(), UserId = "user4", WalletId = Guid.NewGuid(), ExpirationDate=default,Submit = true, Approve = false, BalanceView = true, TransactionView = true, Admin = false, InheritsAuthorizations = false, CreatedAt = DateTime.UtcNow, AssignedBy = "admin1", RevokedBy = null },
                ];

            var permissions = _mapper.Map<List<UserPermission>>(repoPermissions);

            for (var
                i = 0; i < permissions.Count; i++)
            {
                var permission = permissions[i];
                var repoPermission = repoPermissions[i];

                Assert.Equal(repoPermission.ExpirationDate, permission.ExpirationDate);
            }

        }
    }
}
