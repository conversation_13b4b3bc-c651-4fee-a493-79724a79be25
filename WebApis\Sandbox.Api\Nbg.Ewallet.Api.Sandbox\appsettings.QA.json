{"ConnectionStrings": {"SandboxApps": "Server=V0000A0016,56811;database=SandboxApps;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False"}, "SandboxServiceConfig": {"Sandbox": "SandboxApps", "StorageService": "SQLServer"}, "UrlSettings": {"AuthorizationUrl": "https://myqa.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope=sandbox-ewallet-api-v1&redirect_uri={{redirect_uri}}&response_type=code"}, "Authentication": {"Authority": "https://myqa.nbg.gr/identity", "ApiName": "D8864CC2-56C2-43EB-ABD9-AF10484A2A20", "ApiSecret": "01718ECF-A3C7-4F2C-8AAD-252EC793EC23", "ApiScope": "sandbox-ewallet-api-v1", "ManagementApiScope": "sandbox-ewallet-management"}, "ConsentConnectorSettings": {"ConsentsApiUrl": "https://coreapplayerqa.nbg.gr/consent.core.api/", "ApplicationConsentTemplateId": "bec0b570-d86f-4481-93bf-c7db8c9b4d36"}, "ScaClient": {"BaseAddress": "https://coreapplayerqa.nbg.gr/nbg.netcore.sca.core/", "DangerousAcceptAnyServerCertificate": true, "ProxyUrl": "", "UseProxy": false, "ProxyUsername": false, "ProxyPassword": false, "ProxyDomain": true, "MaxConnectionsPerServer": 10}, "ScaFilter": {"ChallengeEndpoint": "sca/challenge", "ValidateEndpoint": "sca/validate", "DisableScaWhitelisting": true, "ScaTokenExpirationInSeconds": 300}, "NLog": {"autoReload": true, "throwExceptions": false, "throwConfigExceptions": true, "internalLogLevel": "Off", "internalLogFile": "c:/logs/internal-nlog.txt", "globalThreshold": "Trace", "variables": {"logRootFolder": "c:/logs", "applicationName": "EwalletSandbox"}, "time": {"type": "AccurateUTC"}, "default-wrapper": {"type": "AsyncWrapper", "overflowAction": "Block"}, "targets": {"async": true, "logFile": {"type": "File", "fileName": "${logRootFolder}/nlog-${applicationName}-${shortdate}.log"}, "logEvent": {"type": "EventLog", "log": "${applicationName}", "source": "${applicationName}", "layout": "${callsite:className=true:includeNamespace=true:includeSourcePath=true:methodName=true} ${message}${newline}${exception:format=ToString}"}}, "rules": [{"logger": "Microsoft.*", "maxLevel": "Error", "final": true}, {"logger": "*", "minLevel": "Debug", "writeTo": "logEvent"}]}}