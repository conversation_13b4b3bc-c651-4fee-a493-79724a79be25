﻿using System.Text.Json.Serialization;

namespace Nbg.Ewallet.Repository.Types;

/// <summary>
/// timeframe enum definition
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum TimeFrameType
{
    /// <summary>
    /// DAILY timeframe
    /// </summary>
    DAILY,
    /// <summary>
    /// MONTHLY timeframe
    /// </summary>
    MONTHLY,
    /// <summary>
    /// YEARLY timeframe
    /// </summary>
    YEARLY
}
