﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Wallet Statements Response
/// </summary>
public class WalletStatementsResponse
{
    /// <summary>
    /// List of Wallet Statements
    /// </summary>
    [DataMember(Name = "statements")]
    public List<Statement> Statements { get; set; }

    /// <summary>
    /// Pagination Token
    /// </summary>
    [DataMember(Name = "paginationToken")]
    public string PaginationToken { get; set; }
}
