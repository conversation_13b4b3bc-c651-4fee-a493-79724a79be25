﻿using System;
using System.Collections.Generic;
using System.Linq;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static List<SandboxTransaction> GenerateTransactions(List<SandboxWallet> wallets)
    {
        return wallets.SelectMany(w =>
        {
            return Enumerable.Range(0, 10).Select(x => new SandboxTransaction
            {
                SerialNum = (10 - x).ToString("D"),
                WalletId = w.WalletId,
                UserId = w.OwnerUserId,
                TransactionId = Guid.NewGuid(),
                TransactionDate = DateTime.Now.AddDays(-x),
                Branch = "",
                Trans = "10",
                Amount = SandBoxRandomDataHelper.GenerateRandomNumber(1, 100),
                Currency = "EUR",
                AmountEquivalent = decimal.Zero,
                CreditDebit = x % 2 == 0 ? "Credit" : "Debit",
                Valeur = DateTime.Now.AddDays(-x),
                Description = string.Empty,
                AccountingBalance = decimal.Zero,
                Reference = string.Empty,
                ExternalSystem = string.Empty,
                RelatedAccount = string.Empty,
                RelatedName = string.Empty,
                Timestamp = DateTime.Now.AddDays(-x),
                Account = w.WalletAccount,
                CounterpartyName = string.Empty,
                CustomerInfo = string.Empty,
                Operation = "E",
                OperationDesc = string.Empty,
                ReasonInfo = string.Empty,
                CounterpartyBank = string.Empty,
                AdditionalInfo = string.Empty,
                TransactionSubType = (TransactionSubType)SandBoxRandomDataHelper.GenerateRandomNumber(0, 4),
                DebitIban = SandBoxRandomDataHelper.GenerateIBAN(),
                CreditIban = w.WalletAccount,
                Status = TransactionStatus.EXECUTED,
                TransactionType = TransactionType.Transfer,
                SubmittedBy = w.OwnerUserId,
                ExecutedBy = w.OwnerUserId,
            }).ToList();
        }).ToList();
    }
}
