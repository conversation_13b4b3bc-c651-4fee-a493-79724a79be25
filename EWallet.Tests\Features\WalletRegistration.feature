﻿#Feature: Wallet registration
# 
#Scenario Outline: As an IB user, I want to register for a new wallet with the selected Wallet Name and Plan Id
#	Given I am logged in as the user <userId>
#   When I register a new wallet with wallet name <walletName>
#   And I create a new subscription with Plan ID <planId> for wallet <walletName>
#   And I proceed to connect a special purpose account with the newly created wallet with <walletName>
#   Then the <walletName> created is connected with a special purpose account
#   And there is an active subcription connected with the <walletName> created wallet with Plan ID equal to <planId>
# 
##make sure the user under the userId Column is be defined in appsettings.Development.json so the password can be retrieved
#   Examples:
#       | userId   | walletName                      | planId    |
#       | 0        | first registered wallet         | Basic     |
#       | 1        | second registered wallet        | Advanced  |
