﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Implementation;

public partial class TransfersService : ITransfersService
{
    private readonly ITransactionService _transactionService;
    private readonly ITransferRequestProvider _transferRequestProvider;
    private readonly IBatchTransferRequestProvider _batchTransferRequestProvider;
    private readonly IMapper _mapper;

    public TransfersService(
        ITransactionService transactionService,
        ITransferRequestProvider transferRequestProvider,
        IBatchTransferRequestProvider batchTransferRequestProvider,
        IMapper mapper)
    {
        _transactionService = transactionService;
        _transferRequestProvider = transferRequestProvider;
        _batchTransferRequestProvider = batchTransferRequestProvider;
        _mapper = mapper;
    }

    public async Task<CalculateFeesResponse> CalculateFeesAsync(TransfersRequestBase request, Guid walletId)
    {
        var transfersRequest = _mapper.Map<TransfersRequest>(request);
        var response = await _transactionService.GetCommissionAsync(_transferRequestProvider, transfersRequest, walletId);
        return response;
    }

    public async Task<BatchTransferExpensesCommissionsResponse> BatchCalculateFeesAsync(BatchTransfersRequest request, Guid walletId)
    {
        var response = await _transactionService.GetCommissionAsync(_batchTransferRequestProvider, request, walletId);
        return response;
    }

    public async Task<TransfersResponse> TransferAsync(TransfersRequest request, string walletId, Guid? batchId = null)
    {
        return await _transactionService.ExecuteAsync(_transferRequestProvider, request, walletId);
    }

    public async Task<BatchTransfersResponse> BatchAsync(BatchTransfersRequest request, string walletId)
    {
        return await _transactionService.ExecuteAsync(_batchTransferRequestProvider, request, walletId);
    }

    public async Task<TransfersForecatsResponse> TransferForecastAsync(TransfersRequest request, string walletId, Guid? batchId = null)
    {
        var result = await _transactionService.ExecuteForecastAsync(_transferRequestProvider, request, walletId);
        return TransfersForecatsResponse.ForecatsResponse(result);
    }

    public async Task<BatchTransfersForecastResponse> BatchForecastAsync(BatchTransfersRequest request, string walletId)
    {
        var result = await _transactionService.ExecuteForecastAsync(_batchTransferRequestProvider, request, walletId);
        return BatchTransfersForecastResponse.ForecatsResponse(result);
    }
}
