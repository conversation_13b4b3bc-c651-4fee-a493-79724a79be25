﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.EWallet.Repository.Types.coreApis;
using Nbg.NetCore.Common.Types;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxAccountsApiClientService : IAccountsApiClientService
{
    private readonly ISandBoxRepositoryService _sandboxRepositoryService;

    public SandboxAccountsApiClientService(ISandBoxRepositoryService sandBoxRepositoryService)
    {
        _sandboxRepositoryService = sandBoxRepositoryService;
    }

    public async Task<Response<TransfersResponseBase>> ExecuteTransactionAsync(TransfersRequestBase request)
    {
        var sandbox = await _sandboxRepositoryService.GetSandBoxModel();
        var sourceAccount = sandbox.AvailableAccounts.FirstOrDefault(a => a.IBAN == request.DebitAccount)
            ?? throw new AccountNotFoundException();

        var destinationAccount = sandbox.AvailableAccounts.FirstOrDefault(a => a.IBAN == request.ReceiverAcc);

        if (sourceAccount.AvailableBalance < request.Amount)
        {
            return new Response<TransfersResponseBase> { Exception = new ResponseMessage() { Code = "728", Description = "Ανεπαρκές υπόλοιπο" } };
        }

        sourceAccount.AvailableBalance -= request.Amount;
        if (destinationAccount is not null)
        {
            destinationAccount.AvailableBalance += request.Amount;
        }
        await _sandboxRepositoryService.UpdateSandboxData(sandbox);

        var commissionResponse = await GetTransferCommission(request);
        var fromWallet = sandbox.Wallets.FirstOrDefault(w => w.WalletAccount == request.DebitAccount);
        return new Response<TransfersResponseBase>
        {
            Payload = new TransfersResponseBase()
            {
                TransferAmount = request.Amount,
                ReferenceNumber = Guid.NewGuid().ToString("N").Left(8),
                Currency = request.Currency ?? "EUR",
                DebitAmountOut = request.Amount,
                DebtorIBAN = request.DebitAccount,
                DebtorName = fromWallet?.WalletName,
                EteComissionOut = commissionResponse.EteCommission,
                UrgentExpencesOut = commissionResponse.UrgentExpenses,
                NonStpExpencesOut = commissionResponse.NonStpExpenses,
                DeptExpencesOut = commissionResponse.DeptExpenses,
                OnlineExpensesOut = commissionResponse.OnlineExpenses,
                SumComissionOut = commissionResponse.SumCommission,
                AvailableBalance = sourceAccount.AvailableBalance,
                Valeur = request.ReceiverAcc.GetRemType() == "RNB" ? null : DateTime.UtcNow.AddDays(2).Date,
                RemType = request.ReceiverAcc.GetRemType(),
                TransactionDate = DateTime.UtcNow,
                Beneficiaries = !string.IsNullOrWhiteSpace(request.ReceiverGR) ? [request.ReceiverGR] : [],
                IsDuplicate = false,
            }
        };
    }

    public async Task<GetAccountBalanceResponse> GetAccountBalanceAsync(GetAccountBalanceRequest request)
    {
        var sandbox = await _sandboxRepositoryService.GetSandBoxModel();

        var availableAccount = sandbox.AvailableAccounts.FirstOrDefault(a => AccountHelpers.IbanToAccount(a.IBAN) == request.Account);
        if (availableAccount is not null)
        {
            return new GetAccountBalanceResponse
            {
                AvailableBalance = availableAccount.AvailableBalance,
                LedgerBalance = availableAccount.LedgerBalance
            };
        }

        throw new AccountNotFoundException();
    }

    public async Task<OpenAccountResponse> OpenAccountAsync(OpenAccountRequest request)
    {
        var response = new OpenAccountResponse
        {
            CustomerFullName = "",
            Iban = SandBoxRandomDataHelper.GenerateIBAN(),
            NewAccountNumber = SandBoxRandomDataHelper.GenerateIBAN(),
            OpenDate = DateTime.UtcNow
        };
        return response;
    }

    public async Task<TransferExpensesCommissionsResponse> CalculateTransferExpensesCommissionsAsync(TransferExpensesCommissionsRequest request)
    {
        var commissionRequestPayload = new TransfersRequest
        {
            Amount = request.Amount,
            BeneficiaryBic = request.BeneficiaryBic,
            DebitAccount = request.DebitAccount,
            Currency = request.DebitAccountCurrency,
            OnlineSend = request.OnlineSend,
            InstantSend = request.InstantSend,
            Emergency = request.Emergency,
            ReceiverAccType = request.ReceiverAccType ?? ReceiverAccType.IBAN,
            RemCategory = request.RemCategory ?? "1",
            Expenses = request.Expenses ?? ExpensesType.OUR
        };
        var commission = CalculateFees(commissionRequestPayload);

        return await commission;
    }

    public async Task<BalancesResponse> GetAccountsAsync(BalancesRequest request)
    {
        var mEwalletSandbox = await _sandboxRepositoryService.GetSandBoxModel();
        return new BalancesResponse { Accounts = mEwalletSandbox.Wallets.Select(x => new AccountBalance { Account = AccountHelpers.IbanToAccount(x.WalletAccount) }).ToList(), };
    }

    public async Task<bool> ValidateUserAccount(string userId, string ibanAccount)
    {
        var mEwalletSandbox = await _sandboxRepositoryService.GetSandBoxModel();
        var userAccounts = mEwalletSandbox.AvailableAccounts.FirstOrDefault(x => x.UserId == userId && x.IBAN == ibanAccount);
        return userAccounts is not null;
    }

    public async Task<byte[]> RetrieveWalletStatementPdfExport(string userId, string account, StatementRequestQueryParams request)
    {
        var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Files", "response.pdf");

        byte[] fileResult = File.ReadAllBytes(filePath);

        return fileResult;
    }

    #region [ Private Helpers ]

    private async Task<TransferExpensesCommissionsResponse> GetTransferCommission(TransfersRequestBase request)
    {
        var commissionRequestPayload = new TransfersRequest
        {
            Amount = request.Amount,
            BeneficiaryBic = request.BeneficiaryBic,
            DebitAccount = request.DebitAccount,
            Currency = request.Currency,
            OnlineSend = request.OnlineSend,
            InstantSend = request.InstantSend,
            Emergency = request.Emergency,
            ReceiverAccType = request.ReceiverAccType ?? ReceiverAccType.IBAN,
            RemCategory = request.RemCategory ?? "1",
            Expenses = request.Expenses ?? ExpensesType.OUR
        };
        var commission = CalculateFees(commissionRequestPayload);

        return await commission;
    }

    private async Task<TransferExpensesCommissionsResponse> CalculateFees(TransfersRequestBase request)
    {
        decimal expenses = request.Amount < 15000 ? 1 : 5;
        var debtorExpenses = request.Expenses == ExpensesType.OUR ? 2m : 0;
        decimal onlineExpenses = request.OnlineSend == true ? 1 : 0;
        decimal instantExpenses = request.InstantSend == true ? 1 : 0;
        decimal urgentExpenses = request.Emergency == true ? 10 : 0;

        if (request.BeneficiaryBic == "ETHNGRAA")
        {
            return new TransferExpensesCommissionsResponse
            {
                BankTitle = null,
                NetAmount = request.Amount,
                SumCommission = 0.0m,
                EteCommission = 0.0m,
                DeptExpenses = 0.0m,
                OnlineExpenses = 0.0m,
                DebitAmount = request.Amount,
                UrgentExpenses = 0.0m,
                ExchangeProfit = -1,
                ExchangeRate = -1,
                NonStpExpenses = -1,
                SumCommissionCurrency = 0,
                CreditAmountCurrency = 0
            };
        }

        return new TransferExpensesCommissionsResponse
        {
            BankTitle = null,
            NetAmount = request.Amount,
            SumCommission = expenses + onlineExpenses + debtorExpenses + instantExpenses + urgentExpenses,
            EteCommission = expenses + instantExpenses,
            DeptExpenses = debtorExpenses != 0 ? debtorExpenses : -1,
            OnlineExpenses = onlineExpenses != 0 ? onlineExpenses : -1,
            DebitAmount = expenses + onlineExpenses + instantExpenses + urgentExpenses + request.Amount,
            UrgentExpenses = urgentExpenses != 0 ? urgentExpenses : -1,
            ExchangeProfit = -1,
            ExchangeRate = -1,
            NonStpExpenses = -1,
            SumCommissionCurrency = 0,
            CreditAmountCurrency = 0
        };
    }

    public async Task<AccountsFullResponse> GetAccountsFullAsync(AccountsFullRequest request)
    {
        var result = new AccountsFullResponse { Accounts = [] };
        var mEwalletSandbox = await _sandboxRepositoryService.GetSandBoxModel();
        var accounts = mEwalletSandbox.AvailableAccounts.Where(a => a.UserId == request.UserID);

        foreach (var account in accounts)
        {
            var accountFull = new AccountFull
            {
                IBAN = account.IBAN,
                Alias = account.Alias,
                AvailableBalance = account.AvailableBalance,
                LedgerBalance = account.LedgerBalance,
                Currency = account.Currency
            };
            result.Accounts.Add(accountFull);
        }

        return result;
    }

    // public Task<JbusrprResponsePayload> GetUserProfileAsync(string userId)
    // {
    //     var res = new JbusrprResponsePayload { Common = new Common { TaxId = string.Empty } };
    //     return Task.FromResult(res);
    // }

    #endregion
}
