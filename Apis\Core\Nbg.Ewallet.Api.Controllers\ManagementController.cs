using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.AspNetCore.ExceptionHandling;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.NetCore.Common.Types;
using Nbg.NetCore.HttpExceptions;

namespace Nbg.Ewallet.Api.Controllers
{
    /// <summary>
    /// This controller is not part of the public api.
    /// It is used by NBG UI project to manage a consent.
    /// Thus there is no reason to be exported with swagger.
    /// UI actions are all POST and expect an IRequest object
    /// </summary>
    [ApiController]
    [ApiExplorerSettings(IgnoreApi = true)]
    public class ManagementController : ControllerBase
    {
        private readonly IManagementService _managementService;
        private readonly IProfileService _profileService;
        private readonly ExceptionHandler _exceptionHandler;

        public ManagementController(ILogger<ManagementController> logger,
            IManagementService managementService,
            IProfileService profileService)
        {
            _managementService = managementService;
            _profileService = profileService;
            _exceptionHandler = new ExceptionHandler(logger, typeof(BadRequestException));
        }

        /// <summary>
        /// Retrieve accounts
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("management/accounts", Name = "GetAccounts")]
        public async Task<Response<AccountsResponse>> GetAccounts(Request<AccountsRequest> request)
        {
            var response = await _exceptionHandler.HandleExceptionAsync(async () => await _managementService.GetUserAccountsAsync(request.Payload));
            return response;
        }

        /// <summary>
        /// Retrieve consent information
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("management/consent", Name = "GetConsent")]
        public async Task<Response<ConsentResponse>> GetConsent(Request<RetrieveConsentRequest> request)
        {
            var response = await _exceptionHandler.HandleExceptionAsync(async () => await _managementService.GetConsentAsync(request.Payload));
            return response;
        }

        /// <summary>
        /// Authorize consent
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("management/consent-authorization", Name = "AuthorizeConsent")]
        public async Task<Response<ConsentResponse>> AuthorizeConsent(Request<UpdateConsentRequest> request)
        {
            var response = await _exceptionHandler.HandleExceptionAsync(async () => await _managementService.AuthorizeConsentAsync(request.Payload));
            return response;
        }

        /// <summary>
        /// Reject consent
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("management/consent-rejection", Name = "RejectConsent")]
        public async Task<Response<ConsentResponse>> RejectConsent(Request<UpdateConsentRequest> request)
        {
            var response = await _exceptionHandler.HandleExceptionAsync(async () => await _managementService.RejectConsentAsync(request.Payload));
            return response;
        }

        /// <summary>
        /// Retrieves the current user profile.
        /// </summary>
        /// <remarks>
        /// The user profile is consisted of user specific information, user permissions on own wallet and access to other wallets.
        /// </remarks>
        [HttpPost]
        [Route("management/getprofile", Name = "getprofile")]
        public async Task<Response<ProfileResponse>> GetProfile(Request<EmptyRequest> request)
        {
            var response = await _exceptionHandler.HandleExceptionAsync(_profileService.GetProfile);
            return response;
        }
    }
}
