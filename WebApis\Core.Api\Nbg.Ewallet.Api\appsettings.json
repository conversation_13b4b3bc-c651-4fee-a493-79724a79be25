{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "ConsentApplicationSettings": {"ConnectionName": "Consents"}, "BigDataAzureClientSettings": {"Client": "bigDataStetementsAzureApi", "GetStatements": "statements/getStatements/"}, "CorporateApiClientSettings": {"Client": "corporateApi", "GetAvailableProducts": "corporateManagement/getAvailableProducts/", "GetLinkedProducts": "corporateManagement/getLinkedProducts/", "UpdateProducts": "corporateManagement/updateProducts/", "GetCompanyUsers": "corporateManagement/getCompanyUsers/"}, "AccountsApiClientSettings": {"Client": "accountsApi", "CallGenericTransfer": "transfers/genericTransferV4/", "GetAccountDetails": "accounts/details", "Accounts": "accounts/accounts", "OpenAccount": "accounts/openAccount", "CalculateTransferExpensesCommissions": "transfers/calculateTransferExpensesCommissionsV1/", "GetStatementsPdfExport": "accounts/statementpdfexport"}, "SubscriptionApiClientSettings": {"Client": "subscriptionApi", "CreateSubscription": "subscription/create", "GetSubscription": "subscription/get", "UpdateSubscription": "subscription/update", "OptOutSubscription": "subscription/opt-out", "AvailableSubscriptionTiers": "subscription/available-tiers"}}