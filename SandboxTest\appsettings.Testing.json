{"ConnectionStrings": {"SandboxApps": "Server=(localdb)\\MSSQLLocalDB;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True;Encrypt=False;"}, "SandboxServiceConfig": {"Sandbox": "SandboxApps", "StorageService": "SQLServer"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "SandboxSettings": {"ImportFilePath": "Assets/SandboxImport.json"}, "UrlSettings": {"AuthorizationUrl": "https://my.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope=sandbox-ewallet-api-v1&redirect_uri={{redirect_uri}}&response_type=code"}, "ConsentConnectorSettings": {"ConsentsApiUrl": "https://coreapplayerqa.nbg.gr/consent.core.api/", "ApplicationConsentTemplateId": "723948c8-1607-40b8-b81a-ebff8b3ed864"}}