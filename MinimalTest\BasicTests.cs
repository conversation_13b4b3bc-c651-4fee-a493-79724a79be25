using Xunit;
using FluentAssertions;

namespace MinimalTest
{
    public class BasicTests
    {
        [Fact]
        public void BasicTest_ShouldPass()
        {
            var result = 1 + 1;
            result.Should().Be(2);
        }

        [Fact]
        public void StringTest_ShouldWork()
        {
            var text = "Hello World";
            text.Should().Contain("World");
        }
    }
}
