using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Nbg.AspNetCore.Audit.Filter.BigData;
using Nbg.AspNetCore.Http.Extensions;
using Nbg.AspNetCore.Identity.iBank.OAuth2;
using Nbg.AspNetCore.ProxyMiddleware;
using Nbg.AspNetCore.ProxyMiddleware.Plugins.Sca;
using Nbg.AspNetCore.ProxyMiddleware.Plugins.ScaRest;
using Nbg.AspNetCore.ProxyMiddleware.Plugins.ScaRestSandbox;
using Nbg.AspNetCore.ProxyMiddleware.Plugins.Validation;
using Nbg.AspNetCore.ProxyMiddleware.Transform.Modules;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.NetCore.ApiSandbox.Extensions;
using Nbg.NetCore.Healthchecks.Checks;
using Nbg.NetCore.Healthchecks.Reports;
using Nbg.NetCore.iBank.Profile.Implementation;
using Nbg.NetCore.Proxy.Utilities;

namespace nbg.ewallet.proxy.api
{
    public class Startup
    {
        private readonly List<string> _smsOtpRoutes =
        [
            "SetWalletPermissionsForOtherWallets",
            "SetWalletUserPermissions",
            "RevokeUserPermissionsForUser",
            "RevokeExternalWalletPermissions",
            "Load",
            "Unload",
            "Create",
            "Execute",
            "Transfer",
            "Batch",
            "approvetransaction",
            "rejecttransaction",
            "requestauthorization",
            "rejectrequestauthorization",
            "Pay"
        ];

        private readonly IConfiguration _configuration;

        public Startup(IConfiguration configuration)
        {
            _configuration = configuration;
            _configuration.ConfigureServicePointManager();
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLower();

            services.AddDistributedMemoryCache(options =>
            {
                options.ExpirationScanFrequency = TimeSpan.FromMinutes(10);
            });

            services.AddSingleton<CorporateUserValidationPlugin>();
            services.Configure<CorporateUserAuthorizationPolicySettings>(_configuration.GetSection("CorporateUserAuthorizationPolicy"));
            services.Configure<SmsOtpSettings>(_configuration.GetSection("SmsOtpSettings"));
            //services.Configure<ValidationsControlPolicy>(_configuration.GetSection("ValidationsControlPolicy"));

            services.AddiBankIdentityOAuth2();
            services.AddHttpContextAccessor();
            services.AddHttpClient("default", "HttpClient:default");

            services.AddHealthChecks()
                .AddAllocatedMemoryCheck()
                .AddUriCheck();

            services.AddProfileService("ProfileService", "CicsHttpConnector");

            var smsOtpSettings = _configuration.GetSection("SmsOtpSettings").Get<SmsOtpSettings>();


            var isSandbox = env.Contains("Sandbox", StringComparison.OrdinalIgnoreCase) && smsOtpSettings.UseSandboxSca;

            var proxyBuilder = services.AddProxyMiddleware()
                .EnableSca(callbacks => callbacks.AddNamedSingleton<IScaGeneratorProvider, ScaGeneratorProvider>("default"))
                .RouteMap((sp, routeMap) =>
                {
                    var routes = routeMap.Where(r => r.Local.Contains("management")).ToList();
                    routes.AddFilter<UserIdValidationPlugin>();
                    routes.AddFilter<CorporateUserValidationPlugin>();

                    if (!smsOtpSettings.EnableSmsOtp) return;

                    foreach (var route in _smsOtpRoutes)
                    {
                        if (isSandbox)
                            RegisterSandboxScaEntpoint(routeMap, route);
                        else
                            RegisterScaEntpoint(routeMap, route);
                    }
                })
                .EnableRestAuditModule()
                .EnableTransformerModule();

            if (isSandbox)
            {
                proxyBuilder.EnableSandboxRestSca(callbacks => callbacks.AddNamedSingleton<IScaGeneratorProvider, ScaGeneratorProvider>("scaOtpProvider"));
            }
            else
            {
                proxyBuilder.EnableSca(callbacks => callbacks.AddNamedSingleton<IScaGeneratorProvider, ScaGeneratorProvider>("scaOtpProvider"));
            }


            if (env.Contains("Sandbox", StringComparison.OrdinalIgnoreCase))
            {
                services.AddGenericControllerTypes(_configuration, [typeof(EwalletDataModel)]);
            }
            else
                services.AddDefaultAuditFilterBigDataWriter();

            services.AddJwtAuthentication();
            services.AddControllers();
        }

        private static void RegisterScaEntpoint(RouteMap routeMap, string routeName, string sectionName = "CustomRestScaFilterConfig")
        {
            var route = routeMap.GetByName(routeName);
            var section = route.Options.Configuration.GetSection(sectionName);
            if (section != null && section.GetChildren().Any())
            {
                var scaRestRouteOptions = new ScaRouteOptions();
                section.Bind(scaRestRouteOptions);
                route.AddRestScaPlugin(scaRestRouteOptions);
            }
        }

        private static void RegisterSandboxScaEntpoint(RouteMap routeMap, string routeName, string sectionName = "CustomRestScaFilterConfig")
        {
            var route = routeMap.GetByName(routeName);
            var section = route.Options.Configuration.GetSection(sectionName);
            if (section != null && section.GetChildren().Any())
            {
                ScaRouteOptions scaRestRouteOptions = new ScaRouteOptions();
                section.Bind(scaRestRouteOptions);
                route.AddSandboxRestScaPlugin(scaRestRouteOptions);
            }
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseHttpsRedirection();
            }
            else
            {
                app.UseHsts(hsts => hsts.MaxAge(365));
                app.UseReferrerPolicy(options => options.NoReferrer());
                app.UseCsp(options => options
                    .DefaultSources(s => s.Self())
                    .ObjectSources(s => s.Self())
                    .FontSources(s => s.CustomSources("https:", "data:"))
                    .ImageSources(s => s.Self().CustomSources("data:"))
                    .ScriptSources(s => s.Self().UnsafeEval())
                    .StyleSources(s => s.Self().UnsafeInline().CustomSources("https:")));
            }

            app.UseDefaultFiles();
            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthentication();

            app.UseProxyMiddleware();

            app.UseEndpoints(endpoints =>
            {
                // register health check endpoint
                endpoints.MapHealthChecks("/health", new HealthCheckOptions
                {
                    ResponseWriter = HealthCheckReports.WriteJson,
                    AllowCachingResponses = false
                });
            });
        }
    }
}
