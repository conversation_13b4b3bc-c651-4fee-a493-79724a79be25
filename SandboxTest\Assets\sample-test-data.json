{"testData": {"description": "Sample test data for NBG eWallet API sandbox testing", "version": "1.0", "wallets": [{"walletId": "11111111-1111-1111-1111-111111111111", "walletName": "Test Business Wallet 1", "organizationName": "Test Organization 1", "balance": 1000.0, "currency": "EUR"}, {"walletId": "22222222-2222-2222-2222-222222222222", "walletName": "Test Business Wallet 2", "organizationName": "Test Organization 2", "balance": 2000.0, "currency": "EUR"}], "users": [{"userId": "<EMAIL>", "firstName": "Test", "lastName": "User1", "email": "<EMAIL>", "permissions": ["read", "write"]}, {"userId": "<EMAIL>", "firstName": "Test", "lastName": "User2", "email": "<EMAIL>", "permissions": ["read"]}], "transactions": [{"transactionId": "tx-001", "fromWallet": "11111111-1111-1111-1111-111111111111", "toWallet": "22222222-2222-2222-2222-222222222222", "amount": 100.0, "currency": "EUR", "description": "Test transfer"}]}}