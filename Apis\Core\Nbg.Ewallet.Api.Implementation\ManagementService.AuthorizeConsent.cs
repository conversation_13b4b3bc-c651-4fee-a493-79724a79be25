﻿using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using nbg.ewallet.repository.types;
using nbg.netcore.consent.repository.types;
using nbg.netcore.consent.types;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;
using Nbg.EWallet.Repository.Types.coreApis;
using Nbg.NetCore.CosmosConnector.Types.Constants;
using Nbg.NetCore.CosmosConnector.Types.Customer;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;
using Nbg.NetCore.HttpExceptions;
using Nbg.NetCore.Utilities;
using Nbg.OpenBanking.Utilities;

namespace Nbg.Ewallet.Api.Implementation;

public partial class ManagementService
{
    private void ValidateRequest(Types.Wallet.UpdateConsentRequest request)
    {
        if (request.ConsentId.Clear() == null)
            throw new BadRequestException("Incorrect JSON Format");
    }

    private (string customerCode, string userId) GetUserContext()
    {
        var customerCode = _httpContextRepositoryService.GetCustomerCode();
        var userId = _httpContextRepositoryService.GetUserId();
        return (customerCode, userId);
    }

    private async Task<(Customer customerData, CustomerAuthorizationLevel authCustomerData, CustomerProductDetails customerProductDetails)> GetCustomerData(string customerCode, string userId)
    {
        var customerData = await _mainFrameConnector.GetCustomerDataAsync(customerCode);
        var authCustomerData = await _mainFrameConnector.GetAuthorizationLevelAsync(userId);
        var customerProductDetails = await _mainFrameConnector.GetCustomerProductDetailsAsync(customerCode);
        return (customerData, authCustomerData, customerProductDetails);
    }

    private async Task<CustomerData> ValidateCustomerData(Customer customerData, CustomerProductDetails customerProductDetails, CustomerAuthorizationLevel authCustomerData)
    {
        return await _validationService.ValidateCustomerDataAsync(customerData, customerProductDetails, authCustomerData);
    }

    private async Task<EWalletConsent> GetAndValidateConsent(string consentId)
    {
        var repoConsentFull = await _consentsRepositoryService.GetConsentAsync(consentId);
        var consent = _mapper.Map<EWalletConsent>(repoConsentFull);
        consent.Data = JsonSerializer.Deserialize<EWalletConsentData>(repoConsentFull.ConsentData);
        if (consent.Status != ConsentStatus.AwaitingAuthorization)
            throw new BadRequestException($"Can not authorize consent with status {consent.Status.EnumToString()}");
        return consent;
    }

    private async Task CheckTaxIdAndWallets(string userId, string customerCode)
    {
        var taxId = await _mainFrameConnector.GetTaxIdFromUserProfileAsync(userId, customerCode);
        if (!string.IsNullOrEmpty(taxId))
        {
            var walletsList = await _walletRepositoryService.FindAllByParamsAsync(taxId, null, null, null);
            if (walletsList.Count > 0) throw new WalletAlreadyExistsException();
        }
    }

    private void CheckCustomerEligibility(CustomerData validatedCustomerData)
    {
        if (validatedCustomerData.ValidationControls.Count > 0
            || validatedCustomerData.IsActive == false
            || validatedCustomerData.HasMissingInformation == true)
        {
            throw new RegistrationElegibilityException();
        }
    }

    private async Task CheckWalletExists(string userId)
    {
        if (await _walletRepositoryService.ExistsByOwnerUserIdAsync(userId))
            throw new WalletAlreadyExistsException();
    }

    private async Task<Wallet> CreateAndSaveWallet(string userId, EWalletConsent consent, CustomerData validatedCustomerData, Customer customerData)
    {
        var newWalletId = Guid.NewGuid();
        var newWallet = new Wallet
        {
            WalletId = newWalletId,
            OwnerUserId = userId,
            WalletName = consent.Data.WalletName,
            RegistrationDate = DateTime.UtcNow,
            VatNumber = validatedCustomerData.VatNumber,
            OrganizationName = validatedCustomerData.Name,
            IsCorporateUser = customerData.BasicInfo.Type == CustomerTypes.LegalEntity,
            OwnerCustomerCode = customerData.BasicInfo.CifId
        };
        var repoWallet = _mapper.Map<RepoWallet>(newWallet);
        repoWallet.TenantId = _httpContextRepositoryService.GetClientId();
        await _walletRepositoryService.SaveAsync(repoWallet);
        return newWallet;
    }

    private async Task AssignAdminPermissions(string userId, Guid newWalletId)
    {
        var userPermission = new UserPermission
        {
            Id = Guid.NewGuid(),
            Admin = true,
            Approve = true,
            BalanceView = true,
            CreationDate = DateTime.UtcNow,
            ExpirationDate = new DateTime(2999, 12, 31, 0, 0, 0),
            Limits = null,
            Submit = true,
            TransactionView = true,
            InheritsAuthorizations = true,
            UserId = userId,
            WalletId = newWalletId,
        };
        var repoUserPermission = _mapper.Map<RepoUserPermission>(userPermission);
        repoUserPermission.AssignedBy = userId;
        await _userPermissionsRepositoryService.SaveAllAsync(new List<RepoUserPermission> { repoUserPermission });
    }

    private async Task<string> CreateAndConnectAccount(Wallet newWallet, string customerCode, string userId)
    {
        var req = new OpenAccountRequest
        {
            UserCra = customerCode,
            Currency = "EUR",
            IsSmsOtp = false,
            TanNumber = null,
            TermsAccepted = true,
            UserID = userId,
            IsCorporateUser = newWallet.IsCorporateUser
        };
        var result = await _createAccountService.OpenAccountAsync(req);
        var account = result.Iban;
        if (newWallet.IsCorporateUser) await _corporateApiClientService.ConnectAccountAsync(userId, account);
        return account;
    }

    private async Task UpdateWalletAccount(Guid walletId, string account)
    {
        await _walletRepositoryService.UpdateWalletAccountByWalletIdAsync(walletId.ToString(), account);
    }

    private async Task<SubscriptionApiResponse> CreateSubscription(EWalletConsent consent, Guid newWalletId)
    {
        var createSubscriptionRequest = new CreateSubscriptionRequest { Tier = consent.Data.PlanId };
        var subscriptionResponse = await _subscriptionApiClientService.CreateSubscriptionAsync(createSubscriptionRequest);

        await _walletRepositoryService.UpdateWalletSubscriptionByWalletIdAsync(newWalletId, subscriptionResponse.SubscriptionId);

        return subscriptionResponse;
    }

    private async Task<ConsentResponse> UpdateConsentAndReturnResponse(Types.Wallet.UpdateConsentRequest request, EWalletConsent consent)
    {
        return await UpdateConsentAsync(request, consent);
    }

    private async Task<ConsentResponse> UpdateConsentAsync(Types.Wallet.UpdateConsentRequest request, EWalletConsent consent)
    {
        consent.Status = ConsentStatus.Authorized;
        var consentData = consent.ConsentData.GetDeserializedObject<EWalletConsentData>();
        consentData.DefaultIban = request.Iban;
        consentData.WalletName = request.WalletName;
        consent.ConsentData = consentData.GetSerializedObject();

        var updatingRepoConsent = _mapper.Map<RepoConsent>(consent);
        var updatedRepoConsentFull = await _consentsRepositoryService.UpdateConsentAsync(request.ConsentId, updatingRepoConsent);
        var updatedConsent = _mapper.Map<EWalletConsent>(updatedRepoConsentFull);

        return ToConsentResponse(updatedConsent);
    }

}
