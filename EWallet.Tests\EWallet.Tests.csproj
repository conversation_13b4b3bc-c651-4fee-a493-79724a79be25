<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <UpToDateCheckInput Remove="Features\WalletRegistration.feature" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="DotNetSeleniumExtras.WaitHelpers" Version="3.11.0" />
        <PackageReference Include="FluentAssertions" Version="6.12.2" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.18" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="MSTest.TestFramework" Version="3.9.3" />
        <PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />
        <PackageReference Include="Selenium.WebDriver" Version="4.26.1" />
        <PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="131.0.6778.8500" />
        <PackageReference Include="SpecFlow" Version="3.9.74" />
        <PackageReference Include="SpecFlow.Tools.MsBuild.Generation" Version="3.9.74" />
        <PackageReference Include="SpecFlow.xUnit" Version="3.9.74" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Apis\Core\Nbg.Ewallet.Api.Core.Types\Nbg.Ewallet.Api.Types.csproj" />
        <ProjectReference Include="..\WebApis\Sandbox.Api\Nbg.Ewallet.Api.Sandbox\Nbg.Ewallet.Api.Sandbox.csproj" />
    </ItemGroup>
    <ItemGroup>
        <None Update="appsettings.Development.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>