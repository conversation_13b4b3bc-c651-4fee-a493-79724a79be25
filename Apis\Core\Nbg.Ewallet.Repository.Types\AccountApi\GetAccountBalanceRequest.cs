﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

public class GetAccountBalanceRequest
{
    /// <summary>The NBG account number (e.g. 11 digits)</summary>
    [DataMember(Name = "account")]
    [Required]
    public string Account { get; set; }

    /// <summary>
    /// UserId that is linked to users account
    /// </summary>
    [DataMember(Name = "connectedUserId")]
    public string ConnectedUserId { get; set; }


    /// <summary>
    /// UserId
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserId { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }
}
