using System;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Implementation;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Sandbox;
using Nbg.Ewallet.Api.Sandbox.Conventions;
using Nbg.Ewallet.Api.Sandbox.Implementation;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;
using Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;
using Nbg.Ewallet.Api.Sandbox.Implementation.Services;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository;
using Nbg.Ewallet.Repository.HttpClients;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.Ewallet.Repository.UnitOfWork;
using Nbg.Ewallet.Repository.Utilities;
using Nbg.NetCore.ApiSandbox.Extensions;
using Nbg.NetCore.ApiSandbox.Interfaces;
using Nbg.NetCore.Healthchecks.Reports;
using Nbg.OpenBanking.ConsentConnector;
using NLog;
using NLog.Web;
using Extensions = Nbg.Ewallet.Api.Sandbox.Extensions;
using ProfileService = Nbg.Ewallet.Api.Implementation.ProfileService;

var logger = LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
try
{
    logger?.Info(Assembly.GetExecutingAssembly().GetName().Name + " Started");
    var builder = WebApplication.CreateBuilder(args);

    var environment = Extensions.EnvironmentValue;
    builder.Services.AddControllers(options =>
    {
        options.Filters.Add(new ValidateModelAttribute()); // Applying globally
    }).AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
        options.JsonSerializerOptions.Converters.Add(new SystemTextJsonUtcDateTimeConverter());
        options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    }).ConfigureApiBehaviorOptions(options => { options.SuppressModelStateInvalidFilter = true; });

    // Comment added by JerryC
    // Add services to the container.
    // Here the sandbox controller and controllers actions are added
    // dynamically from the assembly
    builder.Services.AddGenericControllerTypes(builder.Configuration, [typeof(EwalletDataModel)]);

    builder.Services.RegisterGenericServices();
    builder.Services.AddSwaggerServiceCollection(builder);
    builder.Services.AddConsentConnector();
    builder.Services.AddControllers(SetMvcOptions);
    builder.Services.AddHttpContextAccessor();
    builder.Services.AddAutoMapper(typeof(ImplementationMappingProfile).Assembly, typeof(ImplementationMappingSandBoxProfile).Assembly);

    builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
    builder.Services.AddScoped<IUnitOfWorkFactory, UnitOfWorkFactory>();
    builder.Services.Configure<UnitOfWorkOptions>(cfg => cfg.DisposeBehavior = DisposeBehavior.Commit);


    // Common Implementation
    builder.Services.AddScoped<IProfileService, ProfileService>();
    builder.Services.AddScoped<IWalletService, WalletService>();
    builder.Services.AddScoped<IWalletRegistrationService, WalletRegistrationService>();
    builder.Services.AddScoped<IWalletSubscriptionService, WalletSubscriptionService>();
    builder.Services.AddScoped<IWalletPermissionsService, WalletPermissionsService>();
    builder.Services.AddScoped<IWalletUserPermissionsService, WalletUserPermissionsService>();
    builder.Services.AddScoped<IWalletAuthorizationRequestsService, WalletAuthorizationRequestsService>();

    builder.Services.AddScoped<IConsentsRepositoryService, ConsentsRepositoryService>();
    builder.Services.AddScoped<IConsentService, ConsentService>();
    builder.Services.AddScoped<IManagementService, ManagementService>();
    builder.Services.AddScoped<IConsentApiService, ConsentApiService>();
    builder.Services.AddScoped<ISubscriptionUsageCalculatorService, SubscriptionUsageCalculatorService>();
    builder.Services.AddScoped<ILimitCalculatorService, LimitCalculatorService>();
    builder.Services.AddScoped<ICustomerCommissionService, CustomerCommissionService>();
    builder.Services.AddScoped<ITransactionService, TransactionService>();
    builder.Services.AddScoped<IPaymentsRequestProvider, PaymentsRequestProvider>();
    builder.Services.AddScoped<ITransferRequestProvider, TransferRequestProvider>();
    //builder.Services.AddScoped<IErrorCodeHttpStatusResolver, ErrorCodeHttpStatusResolver>();
    builder.Services.AddScoped<IAuthContextService, AuthContextService>();
    builder.Services.AddScoped<IPaymentsService, PaymentsService>();
    builder.Services.AddScoped<ITransfersService, TransfersService>();
    builder.Services.AddScoped<IBatchTransferRequestProvider, BatchTransferRequestProvider>();
    builder.Services.AddScoped<IBatchPaymentsRequestProvider, BatchPaymentsRequestProvider>();
    // Sandbox Implementation

    builder.Services.AddScoped<ITermsService, TermsSandboxService>();
    builder.Services.AddScoped<ISubscriptionPlansService, SubscriptionPlansService>();
    builder.Services.AddScoped<IBigDataAzureClientService, SandBoxBigDataAzureClientRepositoryService>();
    builder.Services.AddTransient<ISandboxDataGenerator, EwalletDataGenerator>();
    builder.Services.AddScoped<IAdminService, AdminSandboxService>();
    builder.Services.AddScoped<IB2BService, B2BSandboxService>();
    builder.Services.AddScoped<IHttpContextRepositoryService, SandBoxHttpContextRepositoryService>();
    builder.Services.AddScoped<IWalletPermissionsRepositoryService, SanboxWalletPermissionRepositoryService>();
    builder.Services.AddScoped<IUserPermissionsRepositoryService, SandboxUserPermissionRepositoryService>();
    builder.Services.AddScoped<IWalletRepositoryService, SandboxWalletRepositoryService>();
    builder.Services.AddScoped<IAccountsApiClientService, SandboxAccountsApiClientService>();
    builder.Services.AddScoped<ISubscriptionApiClientService, SandboxSubscriptionApiClientService>();
    builder.Services.AddScoped<ITransactionRepositoryService, SandboxTransactionRepositoryService>();
    builder.Services.AddScoped<ILimitRepositoryService, SandboxLimitRepositoryService>();
    builder.Services.AddScoped<IAuthorizationRepositoryService, SandboxAuthorizationRepositoryService>();
    builder.Services.AddScoped<ISandBoxRepositoryService, SandBoxRepositoryService>();
    builder.Services.AddScoped<IMainFrameConnector, SandboxConnector>();
    builder.Services.AddScoped<IValidationService, SandboxValidationService>();
    builder.Services.AddScoped<ICorporateApiClientService, SandboxCorporateApiClientService>();
    builder.Services.AddScoped<ITPPApiClientService, SandboxTPPApiClientService>();
    builder.Services.AddScoped<ICoreManagementInterfaceService, SandBoxCoreManagementRepositoryService>();
    builder.Services.AddScoped<ICreateAccountService, SandboxCreateAccountService>();

    builder.Services.AddScoped<IIbanAccountUtility, IbanAccountUtility>();

    builder.Services.Configure<UrlSettings>(builder.Configuration.GetSection("UrlSettings"));
    builder.Logging.ClearProviders();
    builder.Host.UseNLog();

    var app = builder.Build();

    //app.UseHttpsRedirection();
    app.UseDefaultFiles();
    app.UseStaticFiles();
    app.UseRouting();

    app.UseSwaggerApp();
    app.UseEWalletExceptionHandling();

    app.UseWhen(context => (context.Request.Path.ToString().ToLower().Contains("/sandbox") == false),
        aApp => { aApp.UseEWalletAuthorization(); });

    app.UseEndpoints(endpoints =>
    {
        endpoints.MapControllers(); //.RequireAuthorization();
        endpoints.MapHealthChecks("/health",
            new HealthCheckOptions { ResponseWriter = HealthCheckReports.WriteJson, AllowCachingResponses = false });
    });
    logger?.Info("EWalletSanbox Started");

    await app.RunAsync();
}
catch (Exception exception)
{
    logger?.Error(exception, "Stopped program because of exception");
    throw;
}
finally
{
    LogManager.Shutdown();
}

void SetMvcOptions(MvcOptions options)
{
    options.ReturnHttpNotAcceptable = true;

    //options.Conventions.Add(new AuthorizeFilterConvention());
    options.Conventions.Add(new SecurityFilterConvention());
    options.Conventions.Add(new ScaFilterConvention());
}

public class ValidateModelAttribute : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
        {
            throw new ValidationException("Validation Failed", null, context.ModelState);
        }
    }
}

// so the test projects can see the program class because we use top level statements here
public partial class Program
{
}
