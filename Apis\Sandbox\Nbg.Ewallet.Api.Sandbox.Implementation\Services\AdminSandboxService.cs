﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Admin;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Services;

public class AdminSandboxService : IAdminService
{
    private ISandBoxRepositoryService _sandboxRepositoryService;
    public AdminSandboxService(ISandBoxRepositoryService sandboxRepositoryService)
    {
        _sandboxRepositoryService = sandboxRepositoryService;
    }

    public async Task<ConfigureB2bRulesResponse> ConfigureB2BRules(ConfigureB2bRulesRequest request)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();
        await _sandboxRepositoryService.UpdateSandboxData(model);

        throw new NotImplementedException();
    }

    public async Task<ConfigureB2ChannelsRequest> ConfigureB2Channels(ConfigureB2ChannelsRequest request)
    {
        throw new NotImplementedException();
    }

    Task<CommonSuccessResult<object>> IAdminService.UploadTerms(Terms request)
    {
        throw new NotImplementedException();
    }
}
