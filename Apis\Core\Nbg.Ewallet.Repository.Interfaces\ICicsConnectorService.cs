﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using nbg.ewallet.repository.types;
using Nbg.NetCore.Services.Cics.Http;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbusrpr;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbusupd;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraall;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraupd;
using Nbg.NetCore.Services.Cics.Http.Contract.Jiball;

namespace Nbg.Ewallet.Repository.Interfaces;

[Obsolete("Use IMainFrameConnector instead", true)]
public interface ICicsConnectorService
{
    Task<JcraallResponsePayload> GetCustomerDataAsync(string customerCode);
    Task<JiballResponsePayload> GetAuthorizationLevelAsync(string userId);
    Task<JbusupdResponsePayload> ConnectAccountAsync(string userId, string account);
    Task<JbusrprResponsePayload> GetUserProfileAsync(string userId);

    Task<CicsJsonResponse<JcraupdResponsePayload>> SubmitCustomerCommissionAsync(string userId, long? indicationKey,
        long? indicationValue);

    Task<List<Indications>> FindIndicationsAsync(string userId);
    Task<JBOPACResponse.JBOAccountInfo> CreateAccountAsync(string craCode, string userId, string productCode);
}
