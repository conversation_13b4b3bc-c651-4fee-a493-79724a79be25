﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Wallet Balance Response
/// </summary>
public class WalletBalanceResponse
{
    /// <summary>
    /// Account Ledger Balance
    /// </summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }

    /// <summary>
    /// Account Available Balance
    /// </summary>
    [DataMember(Name = "availableBalance")]
    public decimal AvailableBalance { get; set; }
}
