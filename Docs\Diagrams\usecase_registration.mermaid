sequenceDiagram
    autonumber
    note left of Client: Get Client Credentials Token
    Client->>IDS: POST /identity/connect/token
    IDS-->>Client: { Client Credentials Token }
    note left of Client: EpsilonSmart UI
    Client->>eWalletApi: POST consents/create
    eWalletApi-->> Client: { consentId & authorization URL}
    note left of Client: redirect to <br/> authorization URL
    note left of Client: Completes journey in <br/> NBG UI
    note left of Client: Redirect to <br/> Epsilon UI with <br/> authorization code
    Client->>IDS: /identity/connect/token { authorization code }
    IDS-->>Client: { access token }
    Client->>eWalletApi: POST /wallet/register { consent Id }
    eWalletApi->> pendingCoreApi: getCompany { userId }
    pendingCoreApi-->>eWalletApi: { VAT, CRA, etc.. }
    eWalletApi->>eWalletDB: Create Wallet & UserPermission Entries
    eWalletApi-->>Client: { walletId }
    note left of Client: User is prompted <br/> for payment
    Client->>eWalletApi: POST /wallet/walletId/subscription Subscription Info
    note left of eWalletApi: Store and <br/>configure subscription
    eWalletApi->>eWalletDB: Create Subscription & Payment entries
    eWalletApi-->>Client: OK PAYMENT
    Client->>eWalletApi: POST /wallet/account { walletId }
    eWalletApi->>accountsCoreApi: POST /accounts/openAccount { accountType }
    accountsCoreApi-->eWalletApi: { IBAN }
    eWalletApi->eWalletDB: Update Wallet Entry
    eWalletApi-->>Client: { walletAccount }