﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// The User Permissions assigned to the User Id for the connected wallet.
/// </summary>
public class UserPermission
{
    /// <summary>
    /// The permission unique identifier.
    /// </summary>
    [DataMember(Name = "id")]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// The Wallet Id of the wallet that this permission applies to.
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// The IB User ID of the user that the permission is assigned to.
    /// </summary>
    [DataMember(Name = "userId"), Required]
    public string UserId { get; set; }

    /// <summary>
    /// This permissions allows the user to submit a transaction for approval.
    /// </summary>
    [DataMember(Name = "submit")]
    public bool Submit { get; set; }

    /// <summary>
    /// This permission allows the user to execute a tranasction without approval,
    /// or approve a transaction that is pending approval.
    /// NO, LIMIT, NO_CONSTRAINT
    /// </summary>
    [DataMember(Name = "approve")]
    public bool Approve { get; set; }

    /// <summary>
    /// This permission allows the user to view the remaining balance of the wallet account.
    /// </summary>
    [DataMember(Name = "balanceView")]
    public bool BalanceView { get; set; }

    /// <summary>
    /// This permission allows the user to view the transactions or statements of the wallet account.
    /// </summary>
    [DataMember(Name = "transactionView")]
    public bool TransactionView { get; set; }

    /// <summary>
    /// This permission allows the corporate user to inherit the authorizations provided to the wallet they belong to.
    /// </summary>
    [DataMember(Name = "inheritsAuthorizations")]
    public bool InheritsAuthorizations { get; set; }

    /// <summary>
    /// Administration permission assigned only to the wallet creator/owner.
    /// </summary>
    [DataMember(Name = "admin")]
    public bool Admin { get; set; }

    /// <summary>
    /// Limits per transaction type that apply to the Submit, or Approve permission.
    /// </summary>
    [DataMember(Name = "limits")]
    public List<Limit> Limits { get; set; }

    /// <summary>
    /// Creation datetime of the permission. It is also the assignment date.
    /// </summary>
    [DataMember(Name = "creationDate")]
    public DateTime CreationDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Datetime of the expiration of the permissions.
    /// It is also the revocation datetime, if permissions are explicitly revoked by the admin.
    /// </summary>
    [DataMember(Name = "expirationdate")]
    public DateTime ExpirationDate { get; set; } = DateTime.UtcNow;

}

/// <summary>
/// User Permssions Request Wrapper
/// </summary>
public class UserPermissionResponse
{
    /// <summary>
    /// List of User Permissions
    /// </summary>
    [DataMember(Name = "permission")]
    public List<UserPermission> Permissions { get; set; }
}
