﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// GetWalletBalanceResponse definition
/// </summary>
public class GetWalletBalanceResponse
{
    /// <summary>
    /// Wallet Account ledger balance
    /// </summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }

    /// <summary>
    /// Wallet Account available balance
    /// </summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal AvailableBalance { get; set; }
}
