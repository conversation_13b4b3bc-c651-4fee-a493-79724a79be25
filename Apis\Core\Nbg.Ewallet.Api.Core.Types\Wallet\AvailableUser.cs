﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// A list of the users connected to the IB corporate account.
/// </summary>
public class AvailableUserResponse
{
    /// <summary>
    /// List of ib users that belong to the specific company
    /// </summary>
    [DataMember(Name = "users")]
    public List<AvailableUser> Users { get; set; }
}

/// <summary>
/// A connected corporate account user in IB.
/// </summary>
public class AvailableUser
{
    /// <summary>
    /// User ID of the user.
    /// </summary>
    [DataMember(Name = "userid")]
    public string Userid { get; set; }
    /// <summary>
    /// Alias, set by the IB with the S user profile.
    /// </summary>
    [DataMember(Name = "alias")]
    public string Alias { get; set; }
}
