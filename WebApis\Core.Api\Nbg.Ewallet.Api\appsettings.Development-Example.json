{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },

    "ConnectionStrings": {
        "EWallet": "Server=v000080065;database=EWallet;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False;MultipleActiveResultSets=True",
        //"EWallet": ".;database=EWallet;Integrated Security=SSPI;MultipleActiveResultSets=true;TrustServerCertificate=True;Encrypt=False;",
        "Consents": "Server=v000080065;database=BusinessConsent;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        //"ConfDocsConnection": "Server=v000080065;database=CRAInfoExternal;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "AccountingEngine": "Server=V000080065;DataBase=AccountingEngine;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False"
    },

    "CicsHttpConnector": {
        "Host": "http://***********",
        "Port": 2500,
        "MaxConnectionsLimit": 10,
        "Timeout": 20000,
        "LocalCertificateFingerprint": "",
        "DangerousAcceptAnyServerCertificate": false,
        "UseProxy": false
    },

    "BigDataAzureClientSettings": {
        "Client": "bigDataStetementsAzureApi",
        "GetStatements": "statements/getStatements/"
    },

    "CorporateApiClientSettings": {
        "Client": "corporateApi",
        "GetAvailableProducts": "corporateManagement/getAvailableProducts/",
        "GetLinkedProducts": "corporateManagement/getLinkedProducts/",
        "UpdateProducts": "corporateManagement/updateProducts/",
        "GetCompanyUsers": "corporateManagement/getCompanyUsers/"
    },

    "AccountsApiClientSettings": {
        "Client": "accountsApi",
        "CallGenericTransferV4": "transfers/genericTransferV4/",
        "GetAccountDetails": "accounts/details"
    },

    "TPPApiClientSettings": {
        "Client": "tppApi",
        "Pay": "payments/pay",
        "Commission": "payments/commission"
    },

    "AccountsCoreClientSettings": {
        "Client": "accountsCore",
        "OpenAccount": "http://***********:2500/JBOPAC"
    },

    "HttpClient": {
        "default": {
            "MaxConnectionsPerServer": 100,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:1:30",
            "UseProxy": false
        },

        "bigDataStetementsAzureApi": {
            "BaseAddress": "https://bigdataappsqa.az.nbg.gr/statements/api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "corporateApi": {
            "BaseAddress": "http://v000080121/corporatemanagement.api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "accountsApi": {
            "BaseAddress": "http://v000080121/accounts.api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "tppApi": {
            "BaseAddress": "https://tppcoreapisdev.nbg.gr/thirdpartypayments.core/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },
        "accountsCore": {
            "BaseAddress": "http://***********:2500/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        }
    },

    "ValidationsControlPolicy": {
        "ApplyPolicy": true,
        "Controls": [ "FullKnownCustomer", "LegalDoc", "FinancialProfile", "ActiveIdentity" ],
        "ValidAccountProductCodes": [ "21201", "41201", "41287", "71201BA", "71201PR", "71201EL", "41277", "41343", "41000", "71000BA" ]
    },

    "AccountingEngine": {
        "ConnectionStringName": "AccountingEngine" //must match the name given in ConnectionStrings section
    },

    // TODO : Update this
    "UrlSettings": {
        "AuthorizationUrl": "https://V000080111.nbgit.gr/myIdentityUI/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope=ewallet-api-v1&redirect_uri={{redirect_uri}}&response_type=code"
    },

    "RepositorySettings": {
        "ConnectionName": "EWallet",
        "ApplicationConsentTemplateId": "f78ee80c-c98c-4195-b630-2aff46f7267d"
    },

    "CoreApisUrls": {
        "CardsCoreApi": "http://v000080041/cards.api/",
        "AccountsCoreApi": "https://CoreAppLayerQA.nbg.gr/accounts.api/",
        "WhitelistCoreApi": "https://localhost:44374",
        "FileCoreApi": "http://v000080041/file.core.oauth2.api.v2/",
        "MassPaymentsCoreApi": "http://v000080041/massPayments.api/",
        "AuditCoreApi": "http://localhost/audit.api/"
    },

    "HostCommonConfigurationSettings": {
        "Branch": "700",
        "SubBranch": "4",
        "LegalEntityProductCode": "41201W",
        "PhysicalPersonProductCode": "41000W"
    },

    "CreateAccountSettings": {
        "enableValidations": false
    }
}
