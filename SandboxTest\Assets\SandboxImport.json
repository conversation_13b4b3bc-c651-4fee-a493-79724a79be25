{"sandboxId": "test-sandbox-001", "version": 1, "ewalletSandbox": {"statements": [{"id": "550e8400-e29b-41d4-a716-446655440010", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "transactionId": "550e8400-e29b-41d4-a716-************", "amount": 100.0, "currency": "EUR", "type": "Credit", "description": "Initial wallet load", "date": "2024-01-01T00:00:00Z", "balance": 1000.0}, {"id": "550e8400-e29b-41d4-a716-446655440011", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "transactionId": "550e8400-e29b-41d4-a716-************", "amount": -50.0, "currency": "EUR", "type": "Payment", "description": "Test payment to PAYEE001", "date": "2024-01-02T00:00:00Z", "balance": 950.0}], "availableUsers": {"CF3EDE0F-5995-44FF-9543-7375AC66B37E": [{"userid": "1183831", "alias": "Test User 1"}, {"userid": "1183832", "alias": "Test User 2"}, {"userid": "1183833", "alias": "Test User 3"}], "CF3EDE0F-5995-44FF-9543-7375AC66B38E": [{"userid": "1183834", "alias": "Test User 4"}, {"userid": "1183835", "alias": "Test User 5"}]}, "userPermissions": [{"id": "550e8400-e29b-41d4-a716-************", "admin": true, "approve": true, "balanceView": true, "submit": true, "transactionView": true, "inheritsAuthorizations": true, "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "userId": "1183831", "limits": [{"id": "550e8400-e29b-41d4-a716-************", "amount": 1000.0, "timeframe": "DAILY", "transactionType": "WalletToIBAN", "permissionId": "550e8400-e29b-41d4-a716-************"}, {"id": "550e8400-e29b-41d4-a716-************", "amount": 5000.0, "timeframe": "MONTHLY", "transactionType": "WalletToWallet", "permissionId": "550e8400-e29b-41d4-a716-************"}], "creationDate": "2024-01-01T00:00:00Z", "expirationDate": "2025-01-01T00:00:00Z"}, {"id": "550e8400-e29b-41d4-a716-************", "admin": false, "approve": false, "balanceView": true, "submit": true, "transactionView": true, "inheritsAuthorizations": false, "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "userId": "1183832", "limits": [{"id": "550e8400-e29b-41d4-a716-************", "amount": 500.0, "timeframe": "DAILY", "transactionType": "WalletToIBAN", "permissionId": "550e8400-e29b-41d4-a716-************"}], "creationDate": "2024-01-01T00:00:00Z", "expirationDate": "2025-01-01T00:00:00Z"}], "walletsPermissions": [{"id": "550e8400-e29b-41d4-a716-446655440015", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "externalWalletId": "CF3EDE0F-5995-44FF-9543-7375AC66B38E", "admin": false, "approve": true, "balanceView": true, "submit": true, "transactionView": true, "creationDate": "2024-01-01T00:00:00Z", "expirationDate": "2025-01-01T00:00:00Z"}], "walletsSubscriptions": [{"subscriptionId": "550e8400-e29b-41d4-a716-446655440003", "tier": "Premium", "amount": 2000.0, "dueAmount": 2000.0, "startDate": "2024-01-01T00:00:00Z", "endDate": "2024-07-01T00:00:00Z", "trialEndDate": "2024-01-15T00:00:00Z", "paymentDue": "2024-02-01T00:00:00Z", "status": "PendingPayment", "optOut": false, "subscriptionBundles": [{"transactionType": "Transfer", "value": 100}, {"transactionType": "Payment", "value": 50}]}, {"subscriptionId": "550e8400-e29b-41d4-a716-************", "tier": "Basic", "amount": 500.0, "dueAmount": 500.0, "startDate": "2024-01-01T00:00:00Z", "endDate": "2024-07-01T00:00:00Z", "trialEndDate": "2024-01-08T00:00:00Z", "paymentDue": "2024-02-01T00:00:00Z", "status": "Active", "optOut": false, "subscriptionBundles": [{"transactionType": "Transfer", "value": 25}, {"transactionType": "Payment", "value": 10}]}], "wallets": [{"walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "walletName": "Test Wallet", "walletAccount": "***************************", "organizationName": "Test Organization", "ownerUserId": "1183831", "registrationDate": "2024-01-01T00:00:00Z", "vatNumber": "EL601151111", "connectedIban": "***************************", "walletAccountCreatedAt": "2024-01-01T00:00:00Z"}, {"walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B38E", "walletName": "Test Wallet 2", "walletAccount": "***************************", "organizationName": "Test Organization 2", "ownerUserId": "1183834", "registrationDate": "2024-01-01T00:00:00Z", "vatNumber": "EL601152222", "connectedIban": "***************************", "walletAccountCreatedAt": "2024-01-01T00:00:00Z"}], "terms": {"id": "550e8400-e29b-41d4-a716-************", "title": "Test Terms and Conditions", "content": "These are test terms and conditions for the sandbox environment.", "version": "1.0", "effectiveDate": "2024-01-01T00:00:00Z"}, "transactions": [{"id": "550e8400-e29b-41d4-a716-************", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "amount": 100.0, "currency": "EUR", "type": "Credit", "description": "Initial wallet load", "date": "2024-01-01T00:00:00Z", "status": "Completed"}, {"id": "550e8400-e29b-41d4-a716-************", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "amount": -50.0, "currency": "EUR", "type": "Payment", "description": "Test payment to PAYEE001", "date": "2024-01-02T00:00:00Z", "status": "Completed"}, {"id": "550e8400-e29b-41d4-a716-************", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B38E", "amount": 200.0, "currency": "EUR", "type": "Credit", "description": "Initial wallet load", "date": "2024-01-01T00:00:00Z", "status": "Completed"}], "availableAccounts": [{"walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "iban": "***************************", "accountType": "Checking", "balance": 950.0, "currency": "EUR"}, {"walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B38E", "iban": "***************************", "accountType": "Checking", "balance": 200.0, "currency": "EUR"}], "limits": [{"id": "550e8400-e29b-41d4-a716-************", "amount": 1000.0, "timeframe": "DAILY", "transactionType": "WalletToIBAN", "permissionId": "550e8400-e29b-41d4-a716-************"}, {"id": "550e8400-e29b-41d4-a716-************", "amount": 5000.0, "timeframe": "MONTHLY", "transactionType": "WalletToWallet", "permissionId": "550e8400-e29b-41d4-a716-************"}, {"id": "550e8400-e29b-41d4-a716-************", "amount": 500.0, "timeframe": "DAILY", "transactionType": "WalletToIBAN", "permissionId": "550e8400-e29b-41d4-a716-************"}], "walletAuthorizationRequests": [{"id": "550e8400-e29b-41d4-a716-************", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "requestType": "Transfer", "amount": 300.0, "currency": "EUR", "description": "Authorization request for large transfer", "targetIban": "***************************", "status": "Pending", "requestDate": "2024-01-03T00:00:00Z", "requestedBy": "1183832"}], "payments": [{"id": "550e8400-e29b-41d4-a716-446655440018", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "paymentId": "PAY001", "amount": 50.0, "currency": "EUR", "payeeCode": "PAYEE001", "description": "Test payment to PAYEE001", "status": "Completed", "paymentDate": "2024-01-02T00:00:00Z"}], "transfers": [{"id": "550e8400-e29b-41d4-a716-446655440019", "walletId": "CF3EDE0F-5995-44FF-9543-7375AC66B37E", "transactionId": "TXN001", "amount": 75.0, "currency": "EUR", "targetIban": "***************************", "description": "Test IBAN transfer", "status": "Completed", "transactionDate": "2024-01-03T00:00:00Z"}]}}