﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface ILimitRepositoryService
{
    Task<List<RepoLimit>> GetByPermissionIdTypeAsync(string permissionId, LimitTransactionType transactionType);
    Task<List<RepoLimit>> GetByPermissionIdAsync(string permissionId);
    Task InsertUserLimitsAsync(IEnumerable<RepoLimit> limits);
    Task<List<RepoLimit>> RepoLimitFindAllByPermissionId(Guid permissionId);
    Task<List<RepoLimit>> RepoLimitFindAllByPermissionIdAsync(Guid permissionId);
}
