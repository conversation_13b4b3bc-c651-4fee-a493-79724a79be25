﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.NetCore.CosmosConnector.Types.Constants;
using Nbg.NetCore.Services.Cics.Http;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbusrpr;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbusupd;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraall;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraupd;
using Nbg.NetCore.Services.Cics.Http.Contract.Jiball;
using static nbg.ewallet.repository.types.JBOPACResponse;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

[Obsolete("Use IMainFrameConnector instead", true)]
public class SandboxCicsConnectorService : ICicsConnectorService
{
    public async Task<JcraallResponsePayload> GetCustomerDataAsync(string customerCode)
    {
        return new JcraallResponsePayload() { GeneralData = new GeneralData { CustomerType = CustomerTypes.LegalEntity } };
    }

    public async Task<JiballResponsePayload> GetAuthorizationLevelAsync(string userId)
    {
        return new JiballResponsePayload() { };
    }

    public Task<JbusupdResponsePayload> ConnectAccountAsync(string userId, string account)
    {
        return Task.FromResult(new JbusupdResponsePayload { Succeeded = "True" });
    }

    public Task<JbusrprResponsePayload> GetUserProfileAsync(string userId)
    {
        return Task.FromResult(new JbusrprResponsePayload { Common = new Common { TaxId = string.Empty } });
    }

    public Task<CicsJsonResponse<JcraupdResponsePayload>> SubmitCustomerCommissionAsync(string userId,
        long? indicationKey, long? indicationValue)
    {
        return Task.FromResult(new CicsJsonResponse<JcraupdResponsePayload> { Payload = new JcraupdResponsePayload { Succeeded = "True" } });
    }

    public Task<List<NetCore.Services.Cics.Http.Contract.Jcraall.Indications>> FindIndicationsAsync(string userId)
    {
        return Task.FromResult(new List<NetCore.Services.Cics.Http.Contract.Jcraall.Indications>());
    }

    public Task<JBOAccountInfo> CreateAccountAsync(string craCode, string userId, string productCode)
    {
        return Task.FromResult(new JBOAccountInfo { Account = SandBoxRandomDataHelper.GenerateIBAN(), AccountIban = SandBoxRandomDataHelper.GenerateIBAN() });
    }
}
