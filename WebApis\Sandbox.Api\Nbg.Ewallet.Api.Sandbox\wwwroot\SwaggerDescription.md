## Functionality at a glance

Welcome to the NBG E-Wallet API. In this text, you will find guidelines for the interaction with the API. 
A registered company can register its users to be able to use the service. There are API endpoints for:
1. Creating Wallets
2. Adding And Configuring Users
3. Adding Subscription Plans
4. Configuring user rights and limits
5. Loading/Unloading Wallet Account
6. Wallet Statements
7. Creating and Executing Payments
8. Executing Transfers
9. Payments Statements

## Quick Getting Started


1. **Login/Register** to the NBG Technology HUB

2. Go to **"APPS"**

3. Select your Organization and go to step 4. If you want to create a new Organization click **\"CREATE AN ORGANIZATION\"** and follow the steps below:
	1. Enter the title of your Organization
	2. Enter a short description of your Organization (optional)
	3. Click **"SUBMIT"**

4. Select the Organization of choice and click **"ADD AN APPLICATION"** 
	  1. Fill in the forms (title and short description)
	  2. Check **\"Authorization Code\" and \"Client Credentials\"** 
	  3. Enter the **OAuth Redirect and Post Logout URIs** (these are the URIs that we will redirect the user upon logging in and logging out respectively)
		  
		  You can use the following redirect URL to easily test the API through the portal: *https://developer.nbg.gr/oauth2/redoc-callback*
	  4. Click **"SUBMIT"**
	  5. Store the APPs **"Client ID"** and **"Client Secret"**
5. Go to **"API PRODUCTS"** and select the **NBG E-WALLET API**

6. Click **\"START USING THIS API\"**, choose your app and click
**"SUBSCRIBE"**

7. Get an Access Token using the Access Token Flow and the API scopes provided in the Authentication and Authorization (OAuth2) section below

8. Create a Sandbox

9. Play with the API 

### Sandbox Flow

The Sandbox Flow matches the Production Flow. The difference lies into the Data used. Instead of live
data, the Sandbox flow uses mocked data. Furthermore, sandbox provides the additional functionality of simulating transactions

### General Flow
1. Create a consent via the appropriate endpoint (/consents), using a Client Credentials Flow.

2. Once the Consent is obtained initiate an "Authorization Code Flow" in order to obtain the needed Access Token.
    
    At this point the User will need to **Log In** to the required **Consent UI Screen** using their **Internet Banking credentials**.

    If the User **grants** their **Consent** a successful Access Token is returned.

    The Authorization Endpoint is compiled by amending the generated "Consent ID", like this: https://my.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope={{scope}}&redirect_uri={{redirect_uri}}&response_type=code

3. Once the Authorization Access Token is obtained, you may register a new company

4. You can then create a wallet account

5. After wallet is created, you can proceed with payments and transfers


## Authentication and Authorization (OAuth2)

This API version uses the OAuth2 protocol for authentication and authorization, which means that a
Bearer (access token) should be acquired. An access token can be retrieved using the client_id and
client_secret of the APP that you created and subscribed in this API, and your own credentials
(username, password) that you use to sign in the NBG Technology HUB. The scopes are defined below:

**Authorization Endpoint:** 

	  https://my.nbg.gr/identity/connect/authorize


**Token Endpoint:** 

	  https://my.nbg.gr/identity/connect/token

### Authorization Code ###

**Sandbox Scopes:** 

	  sandbox-ewallet-api-v1 offline_access


**Production Scopes:** 

	  ewallet-api-v1 offline_access

### Client Credentials ###

**Sandbox Scopes:** 

	  sandbox-ewallet-api-v1


**Production Scopes:** 

	  ewallet-api-v1


See more [here](https://developer.nbg.gr/oauth-document)

## Create your Sandbox

Create a new Sandbox application by invoking the POST /sandbox. This call will generate a new Sandbox
with a unique sandbox-id.


__Important!__ Before proceeding save the sandbox id you just created.


When you create a sandbox, sandbox specific data are generated as sample data.


## Start Testing

Once you have your sandbox-id, you can start invoking the rest of the operations by providing the
mandatory http header **sandbox-id**  and the http headers described below.

## Important notes


**Request headers**


The following HTTP header parameters are required for every call:


1. Authorization. The Auth2 Token

2. sandbox-id. Your Sandbox ID

# Feedback and Questions

We would love to hear your feedback and answer your questions. Send us at
[<EMAIL>](<EMAIL>)


Check out our [Sandbox Postman Collection](https://developer.nbg.gr/partner/news/Access-to-Github-repositories-for-Partners)!


________________________________________

Created by [**NBG**](https://www.nbg.gr/).