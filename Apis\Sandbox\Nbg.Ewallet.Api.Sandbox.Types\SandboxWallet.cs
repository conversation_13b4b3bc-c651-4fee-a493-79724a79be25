﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// Represents a wallet in the eWallet system.
/// </summary>
[DataContract]
public class SandboxWallet
{
    /// <summary>
    /// The unique identifier for the wallet.
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// The IBAN of the secial purpose wallet account.
    /// </summary>
    [DataMember(Name = "walletAccount")]
    public string WalletAccount { get; set; }

    /// <summary>
    /// The date and time when the wallet account was created.
    /// </summary>
    [DataMember(Name = "walletAccountCreatedAt")]
    public DateTime? WalletAccountCreatedAt { get; set; }

    /// <summary>
    /// The name of the company associated with the wallet as it appears in IB.
    /// </summary>
    [DataMember(Name = "organizationName")]
    public string OrganizationName { get; set; }

    /// <summary>
    /// The connected IBAN, reserved for future use and should be ignored for now.
    /// </summary>
    [DataMember(Name = "connectedIban")]
    public string ConnectedIban { get; set; }

    /// <summary>
    /// The date when the wallet was registered.
    /// </summary>
    [DataMember(Name = "registrationDate")]
    public DateTime RegistrationDate { get; set; }

    /// <summary>
    /// The VAT number associated with the wallet.
    /// It can be and FP or NP VAT number.
    /// It is unique across all wallets, since only one wallet per business entity can exist.
    /// </summary>
    [DataMember(Name = "vatNumber")]
    public string VatNumber { get; set; }

    /// <summary>
    /// The user ID of the wallet owner.
    /// The owner of the wallet has always ADMIN rights.
    /// </summary>
    [DataMember(Name = "ownerUserId")]
    public string OwnerUserId { get; set; }

    /// <summary>
    /// The name of the wallet. It is set by the wallet creator during registration and it is not unqiue across wallets.
    /// </summary>
    [DataMember(Name = "walletName")]
    public string WalletName { get; set; }

    /// <summary>
    /// The customer code of the wallet owner.
    /// </summary>
    [DataMember(Name = "ownerCustomerCode")]
    public string OwnerCustomerCode { get; set; }

    /// <summary>
    /// Indicates whether the wallet owner is a corporate user.
    /// </summary>
    [DataMember(Name = "isCorporateUser")]
    public bool IsCorporateUser { get; set; }


    /// <summary>
    /// Active Subscription Id.
    /// </summary>
    [DataMember(Name = "activeSubscriptionId")]
    public Guid ActiveSubscriptionId { get; set; }
}
