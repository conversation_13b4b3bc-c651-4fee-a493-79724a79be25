﻿using System.Threading.Tasks;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Services;

public class B2BSandboxService : IB2BService
{
    private ISandBoxRepositoryService _sandboxRepositoryService;

    public B2BSandboxService(ISandBoxRepositoryService sandboxRepositoryService)
    {
        _sandboxRepositoryService = sandboxRepositoryService;
    }

    public async Task<B2BSearchResponse> Search(B2BSearchRequest request)
    {
        B2BSearchResponse mB2BSearchResponse = new();

        return mB2BSearchResponse;
    }
}
