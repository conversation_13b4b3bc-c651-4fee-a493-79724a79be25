using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nbg.ewallet.repository.dbQueries;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;

namespace Nbg.Ewallet.Repository;

public class TransactionRepositoryService : BaseRepositoryService, ITransactionRepositoryService
{
    public TransactionRepositoryService(
        IOptions<RepositorySettings> repositoryOptions,
        ILogger<TransactionRepositoryService> logger,
        IUnitOfWork unitOfWork) : base(unitOfWork, repositoryOptions, logger) { }

    public async Task<RepoTransaction?> FindOneByTransactionIdAsync(string transactionId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var transactions = await conn.QueryAsync<RepoTransaction>(
                TransactionQueries.GetTransactionById,
                new { TransactionId = transactionId },
                tx);
            return transactions.FirstOrDefault();
        }, "An error occurred while fetching the transaction.");
    }

    public async Task<List<RepoTransaction>> FindAllExecutedByWalletIdAndTransactionSubtypeAndDateFromAndDateToAsync(
        string walletId,
        TransactionSubType transactionType,
        DateTime dateFrom,
        DateTime dateTo)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var whereObject = new
            {
                WalletId = walletId,
                TransactionSubType = (DapperableEnum<TransactionSubType>)transactionType,
                Status = (DapperableEnum<TransactionStatus>)TransactionStatus.EXECUTED,
                DateFrom = dateFrom,
                DateTo = dateTo
            };
            var transactions = await conn.QueryAsync<RepoTransaction>(
                TransactionQueries.GetTransactionByWalletIdAndTransactionSubTypeAndTransactionDate,
                whereObject,
                tx);
            return transactions.ToList();
        }, "An error occurred while fetching transactions for the specified wallet, subtype, and date range.");
    }

    public async Task SaveAllAsync(List<RepoTransaction> transactions)
    {
        if (transactions == null || transactions.Count == 0) return;

        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            foreach (var transaction in transactions)
            {
                await conn.ExecuteAsync(TransactionQueries.InsertTransaction, transaction, tx);
            }
        }, "An error occurred while inserting multiple transactions.");
    }

    public async Task<bool> SaveAsync(RepoTransaction transaction)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var res = await conn.ExecuteAsync(
                TransactionQueries.UpdateTransaction,
                transaction,
                tx);
            return res == 1;
        }, "An error occurred while updating the transaction.");
    }

    public async Task<RepoTransaction?> FindOneByWalletIdAndTransactionIdAsync(string walletId, string transactionId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var res = await conn.QueryFirstOrDefaultAsync<RepoTransaction>(
                TransactionQueries.GetTransactionByWalletIdAndTransactionId,
                new { WalletId = walletId, TransactionId = transactionId },
                tx);
            return res;
        }, "An error occurred while fetching the transaction by wallet ID and transaction ID.");
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAsync(string walletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var transactions = await conn.QueryAsync<RepoTransaction>(
                TransactionQueries.GetTransactionByWalletId,
                new { WalletId = walletId },
                tx);
            return transactions.ToList();
        }, "An error occurred while fetching transactions by wallet ID.");
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndBatchIdAndTransactionStatusAsync(
        string walletId,
        Guid? batchId,
        TransactionStatus? status,
        int pageNumber = 0,
        int pageSize = 100)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var skip = pageNumber * pageSize;

            StringBuilder stringBuilder = new(TransactionQueries.GetTransactionByWalletId);

            if (batchId.HasValue) stringBuilder.Append(TransactionQueries.AndWithBatchId);
            else stringBuilder.Append(TransactionQueries.AndWithoutBatchId);

            if (status.HasValue) stringBuilder.Append(TransactionQueries.AndStatus);

            stringBuilder
            .Append(TransactionQueries.OrderByCreatedAt)
            .Append(TransactionQueries.SkipTake);

            var query = stringBuilder.ToString();
            var whereObject = new
            {
                WalletId = walletId,
                BatchId = batchId,
                Status = status.HasValue ? (DapperableEnum<TransactionStatus>)status.Value : (DapperableEnum<TransactionStatus>?)null,
                Skip = skip,
                Take = pageSize
            };
            var transactions = await conn.QueryAsync<RepoTransaction>(query, whereObject, tx);
            return transactions.ToList();
        }, "An error occurred while fetching transactions with filters.");
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndTransactionSubTypeAndSubmittedByAsync(
        string walletId,
        IEnumerable<TransactionSubType> transactionSubTypes,
        string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var whereObject = new { WalletId = walletId, transactionSubType = transactionSubTypes.Select(x => x.ToString()), UserId = userId };
            var transactions = await conn.QueryAsync<RepoTransaction>(
                TransactionQueries.GetTransactionByWalletIdAndTransactionSubTypeAndSubmittedBy,
                whereObject,
                tx);
            return transactions.ToList();
        }, "An error occurred while fetching transactions by wallet ID, transaction subtype, and submitted by user.");
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndTransactionSubTypeAndExecutedByAsync(
        string walletId,
        IEnumerable<TransactionSubType> transactionSubTypes,
        string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var whereObject = new { WalletId = walletId, transactionSubType = transactionSubTypes.Select(x => x.ToString()), UserId = userId };
            var transactions = await conn.QueryAsync<RepoTransaction>(
                TransactionQueries.GetTransactionByWalletIdAndTransactionSubTypeAndExecutedBy,
                whereObject,
                tx);
            return transactions.ToList();
        }, "An error occurred while fetching transactions by wallet ID, transaction subtype, and executed by user.");
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndSubmittedByOrExecutedByOrApprovedByAsync(string walletId, string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var whereObject = new { WalletId = walletId, UserId = userId };
            var transactions = await conn.QueryAsync<RepoTransaction>(
                TransactionQueries.GetTransactionsByWalletIdAndUserIdInApprovedExecutedSubmitted,
                whereObject,
                tx);
            return transactions.ToList();
        }, "An error occurred while fetching transactions by wallet ID and user ID.");
    }
}
