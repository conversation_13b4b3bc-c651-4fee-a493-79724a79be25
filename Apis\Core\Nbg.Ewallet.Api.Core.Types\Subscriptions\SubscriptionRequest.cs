﻿using System.Collections.Generic;
using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Types.Subscriptions;

/// <summary>
/// SubscriptionRequest definition
/// </summary>
public class CreateSubscriptionRequest
{
    /// <summary>
    /// Id of the discount applied
    /// </summary>
    [DataMember(Name = "tier")]
    public SubscriptionTier Tier { get; set; }
}


/// <summary>
/// SubscriptionRequest definition
/// </summary>
public class OptOutSubscriptionRequest
{
}
