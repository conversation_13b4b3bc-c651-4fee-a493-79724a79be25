using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.CorporateApi;
[DataContract]
public class UpdateProductsRequest : CompanyUserGenericRequest
{

    public UpdateProductsRequest()
    {
    }

    public UpdateProductsRequest(string userId, CorpUserProductDetails? productDetails, ICollection<ProductInfoResponse> linkedAccounts)
    {
        var products = linkedAccounts
            .Select(x => new UpdateProductInfo { ProductCode = Enum.Parse<ProductCodeEnum>(x.ProductCode), Values = x.Values })
            .ToList();

        if (productDetails is not null)
        {
            //TODO; Where or FirstOrDefault?
            foreach (var product in products.Where(x => x.ProductCode == ProductCodeEnum.Accounts))
            {
                product.Values ??= [];
                product.Values.Add(productDetails);

                //TODO: check if needed.
                //product.Values.ForEach(x => x.Number = AccountHelpers.IbanToAccount(x.Number));
            }
        }

        UserID = userId;
        SubjectUserId = userId;
        AuthDetails = new NbgEmployeeAuthDetails();
        Products = products;
    }

    /// <summary>Version of the data</summary>
    [DataMember(Name = "version")]
    public int Version { get; set; }

    /// <summary>Product Info</summary>
    [DataMember(Name = "products")]
    public List<UpdateProductInfo> Products { get; set; }
}

public class UpdateProductInfo
{
    /// <summary>Product type</summary>
    [DataMember(Name = "productCode")]
    public ProductCodeEnum? ProductCode { get; set; }

    /// <summary> Values </summary>
    [DataMember(Name = "values")]
    public List<CorpUserProductDetails> Values { get; set; }
}

[DataContract]
public enum ProductCodeEnum
{
    [EnumMember]
    Accounts,
    [EnumMember]
    Loans,
    [EnumMember]
    TermsAccounts,
    [EnumMember]
    Portfolios,
    [EnumMember]
    Cards
}
