﻿using System;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Nbg.NetCore.Sca.Filter;

namespace Nbg.Ewallet.Api.Sandbox.Conventions;

public class ScaFilterConvention : IActionModelConvention
{
    public void Apply(ActionModel action)
    {
        var controllerName = action.Controller.ControllerName;
        var actionName = action.ActionName;

        if (controllerName.StartsWith("Management", StringComparison.OrdinalIgnoreCase)
            && actionName.Equals("AuthorizeConsent", StringComparison.OrdinalIgnoreCase))
        {
            action.Filters.Add(new ScaFilterParamsAttribute(typeof(ConsentScaGeneratorProvider)));
        }
    }
}
