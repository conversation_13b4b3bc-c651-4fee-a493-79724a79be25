using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface ITransactionRepositoryService
{
    Task SaveAllAsync(List<RepoTransaction> transactions);
    Task<RepoTransaction?> FindOneByTransactionIdAsync(string transactionId);
    Task<List<RepoTransaction>> FindAllExecutedByWalletIdAndTransactionSubtypeAndDateFromAndDateToAsync(string walletId, TransactionSubType transactionType, DateTime dateFrom, DateTime dateTo);
    Task<List<RepoTransaction>> FindAllByWalletIdAndTransactionSubTypeAndSubmittedByAsync(string walletId, IEnumerable<TransactionSubType> transactionSubTypes, string userId);
    Task<List<RepoTransaction>> FindAllByWalletIdAndTransactionSubTypeAndExecutedByAsync(string walletId, IEnumerable<TransactionSubType> transactionSubTypes, string userId);
    Task<List<RepoTransaction>> FindAllByWalletIdAsync(string walletId);
    Task<RepoTransaction?> FindOneByWalletIdAndTransactionIdAsync(string walletId, string transactionId);
    Task<List<RepoTransaction>> FindAllByWalletIdAndBatchIdAndTransactionStatusAsync(string walletId, Guid? batchId, TransactionStatus? status, int pageNumber = 0, int pageSize = 100);
    Task<bool> SaveAsync(RepoTransaction transaction);
    Task<List<RepoTransaction>> FindAllByWalletIdAndSubmittedByOrExecutedByOrApprovedByAsync(string walletId, string userId);
}
