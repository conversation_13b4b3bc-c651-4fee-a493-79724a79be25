﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Records;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Implementation;

public class TransferRequestProvider : PayTransBase, ITransferRequestProvider
{
    private readonly IWalletRepositoryService _walletRepository;
    private readonly ITransactionRepositoryService _transactionRepository;
    private readonly ICustomerCommissionService _customerCommissionService;
    private readonly IAccountsApiClientService _accountsApiClientService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly IIbanAccountUtility _ibanAccountUtility;
    private readonly IMapper _mapper;

    public TransferRequestProvider(
        IWalletRepositoryService walletRepository,
        IHttpContextRepositoryService httpContextService,
        IAuthContextService authContextService,
        ITransactionRepositoryService transactionRepository,
        ICustomerCommissionService customerCommissionService,
        IAccountsApiClientService accountsApiClientService,
        IWalletRepositoryService walletRepositoryService,
        IIbanAccountUtility ibanAccountUtility,
        IMapper mapper
        ) : base(httpContextService, authContextService)
    {
        _walletRepository = walletRepository;
        _transactionRepository = transactionRepository;
        _customerCommissionService = customerCommissionService;
        _accountsApiClientService = accountsApiClientService;
        _walletRepositoryService = walletRepositoryService;
        _ibanAccountUtility = ibanAccountUtility;
        _mapper = mapper;
    }

    public TransfersResponse EnrichResponse(TransfersRequest request, List<RepoTransaction> transactions)
    {
        var transferResponse = request.EnrichResponse(new TransfersResponse()
        {
            RequiresApproval = transactions.Select(x => x.Status.Value == TransactionStatus.PENDING_APPROVAL).FirstOrDefault(),
            TransactionId = transactions.Select(x => x.TransactionId).FirstOrDefault()
        });
        return transferResponse;
    }

    public async Task<CalculateFeesResponse> GetCommissionAsync(TransfersRequest request, Guid walletId)
    {
        (_, var customerCode, var executedAs) = GetUserAndCustomerCode();

        var response = new CalculateFeesResponse();
        var transactionSubType = await GetTransactionSubTypeAsync(request.ReceiverAcc);
        response.TransactionType = transactionSubType;
        if (AccountHelpers.IbanIsNbg(request.ReceiverAcc)) return response;

        var transfersCommissionRequest = request.ToTransferExpensesCommissionsRequest(executedAs);
        transfersCommissionRequest.UserID = executedAs;

        await _customerCommissionService.UpdateCustomerCommissionAsync(walletId, transactionSubType, executedAs, customerCode);
        var res = await _accountsApiClientService.CalculateTransferExpensesCommissionsAsync(transfersCommissionRequest);
        response = _mapper.Map<CalculateFeesResponse>(res);
        response.TransactionType = transactionSubType;
        return response;
    }

    public TotalAmountWithCommission GetTotalAmountWithCommission(TransfersRequest request, CalculateFeesResponse commissionResponse)
    {
        return new TotalAmountWithCommission(request.Currency ?? "EUR", request.Amount + commissionResponse.SumCommission, commissionResponse.SumCommission);
    }

    public async Task<TransfersResponse> HandleTransactionExecutionsAsync(TransfersRequest request, List<RepoTransaction> transactions, RepoWallet wallet)
    {
        (var userId, var customerCode, var executedAs) = GetUserAndCustomerCode();
        var transaction = transactions.First();

        await _customerCommissionService.UpdateCustomerCommissionAsync(wallet.WalletId, transaction.TransactionSubType, userId, customerCode);

        request.SYDIPEL = wallet.OwnerCustomerCode;
        request.IsCorporateUser = wallet.IsCorporateUser;
        request.UserID = executedAs;
        var response = await _accountsApiClientService.ExecuteTransactionAsync(request);

        transaction.ExecutedBy = userId;
        transaction.ExecutedAs = executedAs;
        transaction.Commission = response.Payload?.SumComissionOut;

        await response.HandleResultAsync(transaction, _transactionRepository);

        var transfersResponse = response.Payload.ToTransfersResponse();
        transfersResponse.TransactionId = transaction.TransactionId;
        return transfersResponse;
    }

    public async Task<TransfersResponse> TransactionForecastExecutionsAsync(TransfersRequest request, RepoWallet wallet)
    {
        (var userId, _, _) = GetUserAndCustomerCode();
        var validationResult = await request.ValidateAccountAvailableToUser(wallet, userId, _accountsApiClientService);

        var accountBalance = await _accountsApiClientService.GetAccountBalanceAsync(new GetAccountBalanceRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(validationResult.IBAN),
            UserId = userId,
            Currency = "070"
        });

        var balance = accountBalance.AvailableBalance;
        balance -= request.Amount;
        if (balance < 0)
        {
            throw new InsufficientBalanceException();
        }

        var transfersCommissionRequest = request.ToTransferExpensesCommissionsRequest(userId);
        transfersCommissionRequest.UserID = userId;
        var commisionResult = await _accountsApiClientService.CalculateTransferExpensesCommissionsAsync(transfersCommissionRequest);

        balance -= commisionResult.SumCommission;
        if (balance < 0)
        {
            throw new InsufficientBalanceException();
        }

        return new() { TransactionId = request.TransactionId };
    }

    public async Task<List<RepoTransaction>> InitiateRepoTransactions(TransfersRequest request, RepoWallet wallet, string userId)
    {
        var transactionSubType = await request.GetTransactionSubType(_walletRepository);
        return [request.ToPendingRepoTransaction(wallet, userId, transactionSubType)];
    }

    public async Task<ValidateRequestResult> ValidateRequest(TransfersRequest request, RepoWallet wallet)
    {
        (var userId, _, _) = GetUserAndCustomerCode();
        request.ValidateAccountBICTransferType();
        TransferValidations.ValidateBeneficiaryAndReason(request.ReceiverGR, request.Reason);
        return await request.ValidateAccountAvailableToUser(wallet, userId, _accountsApiClientService);
    }

    private async Task<TransactionSubType> GetTransactionSubTypeAsync(string receiverAccount)
    {
        var wallet = await _walletRepositoryService.FindOneByWalletAccountAsync(receiverAccount);
        if (wallet != null) return TransactionSubType.WalletToWallet;
        if (AccountHelpers.IbanIsNbg(receiverAccount)) return TransactionSubType.WalletToNBG;
        else return TransactionSubType.WalletToIBAN;
    }

    public void SetNewDebitAccount(TransfersRequest request, string ibanAccount)
    {
        request.DebitAccount = ibanAccount;
    }
}
