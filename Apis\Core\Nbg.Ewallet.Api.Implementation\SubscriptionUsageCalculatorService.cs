﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Implementation;

// TODO remove this when we find out what api to call
public class SubscriptionUsageCalculatorService : ISubscriptionUsageCalculatorService
{
    private readonly ISubscriptionApiClientService _subscriptionApiClientService;
    private readonly ITransactionRepositoryService _transactionRepository;


    public SubscriptionUsageCalculatorService(ISubscriptionApiClientService subscriptionApiClientService,
        ITransactionRepositoryService transactionRepository)
    {
        _subscriptionApiClientService = subscriptionApiClientService;
        _transactionRepository = transactionRepository;
    }

    public async Task<SubscriptionUsage> CalculateSubscriptionUsageAsync(Guid walletId)
    {
        //RepoWalletSubscription subscription = await _subscriptionApiClientService.FindOneActiveByWalletIdAsync(walletId);

        //if (subscription == null)
        //{
        //    throw new InactiveSubscriptionException();
        //}

        //List<RepoSubscriptionBundle> subscriptionBundles = await _subscriptionRepositoryService.RepoSubscriptionBundleFindAllBySubscriptionId(subscription.SubscriptionId);

        //var subscriptionUsage = new SubscriptionUsage
        //{
        //    SubscriptionId = subscription.SubscriptionId,
        //    Usage = subscriptionBundles.Select(x => new FreeTransaction { TransactionType = x.TransactionType, Value = x.Value, }).ToList(),
        //};
        //foreach (var usage in subscriptionUsage.Usage)
        //{
        //    var (dateFrom, dateTo) = subscription.StartDate.GetCurrentMonth(subscription.ExpirationDate);
        //    var transactions =
        //        await _transactionRepository.FindAllExecutedByWalletIdAndTransactionSubtypeAndDateFromAndDateToAsync(walletId.ToString(),
        //            usage.TransactionType.ToTransactionSubType(), dateFrom, dateTo);
        //    usage.Value -= transactions.Count;
        //    usage.Value = Math.Max(usage.Value, 0);
        //}

        return new SubscriptionUsage
        {
            SubscriptionId = Guid.NewGuid(),
            Usage = [new FreeTransaction() { TransactionType = SubscriptionTransactionType.Transfer, Value = 5 }, new FreeTransaction() { TransactionType = SubscriptionTransactionType.Payment, Value = 7 }]
        };
    }
}
