﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Represents a request to retrieve wallet statements within a specified date range and with pagination options.
/// </summary>
[DataContract]
public class WalletStatementsRequest
{
    /// <summary>
    /// The start date for filtering the statements.
    /// </summary>
    [DataMember(Name = "dateFrom")]
    public DateTime? DateFrom { get; set; }

    /// <summary>
    /// The end date for filtering the statements.
    /// </summary>
    [DataMember(Name = "dateTo")]
    public DateTime? DateTo { get; set; }

    /// <summary>
    /// A token used to paginate through the results.
    /// Used in the statements response.
    /// If it is assigned a value, it means there are more data to be fetched/
    /// </summary>
    [DataMember(Name = "paginationToken")]
    public string PaginationToken { get; set; }

    /// <summary>
    /// The maximum number of statement entries to return in the response.
    /// </summary>
    [DataMember(Name = "limit")]
    public int? Limit { get; set; }
}
