﻿using System.Runtime.Serialization;

namespace Nbg.EWallet.Repository.Types.coreApis
{
    [DataContract]
    public class OpenAccountRequest
    {
        [DataMember(Name = "userId")]
        public string UserID { get; set; }

        [DataMember(Name = "customerBranch")]
        public string CustomerBranch { get; set; }

        [DataMember(Name = "accountType")]
        public string AccountType { get; set; }

        [DataMember(Name = "userCra")]
        public string UserCra { get; set; }

        [DataMember(Name = "currency")]
        public string Currency { get; set; }

        [DataMember(Name = "tanNumber")]
        public string TanNumber { get; set; }

        [DataMember(Name = "isSmsOtp")]
        public bool? IsSmsOtp { get; set; }

        [DataMember(Name = "termsAccepted")]
        public bool TermsAccepted { get; set; }

        [DataMember(Name = "serviceBranch")]
        public string ServiceBranch { get; set; }

        [DataMember(Name = "alternativeName")]
        public string AlternativeName { get; set; }

        [DataMember(Name = "beneficialName")]
        public string BeneficialName { get; set; }

        [DataMember(Name = "accountDescription")]
        public string AccountDescription { get; set; }

        [DataMember(Name = "category")]
        public string Category { get; set; }

        [DataMember(Name = "isLoyaltyMember")]
        public bool? IsLoyaltyMember { get; set; }

        [DataMember(Name = "isCorporateUser")]
        public bool? IsCorporateUser { get; set; }
    }
}
