﻿using System.Text.Json.Serialization;

namespace Nbg.Ewallet.Repository.Types;

/// <summary>
/// Defined TransactionTypes
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum TransactionSubType
{
    /// <summary>
    /// Wallet To Wallet Transaction
    /// </summary>
    WalletToWallet,
    /// <summary>
    /// Wallet To IBAN Transaction
    /// </summary>
    WalletToIBAN,
    /// <summary>
    /// Wallet To NBG Transaction
    /// </summary>
    WalletToNBG,

    WalletLoad,
    WalletWithdraw,
    /// <summary>
    /// Generic Payment
    /// </summary>
    GenericPayment
}
