﻿using Nbg.Ewallet.Repository.Types.Configuration;

namespace Nbg.Ewallet.Repository.Types;

public class AccountsApiClientSettings : ClientSettingsBase
{
    public string CallGenericTransfer { get; set; }
    public string GetAccountDetails { get; set; }
    public string OpenAccount { get; set; }
    public string CalculateTransferExpensesCommissions { get; set; }
    public string Accounts { get; set; }
    public string AccountsFull { get; set; }
    public string GetStatementsPdfExport { get; set; }
}
