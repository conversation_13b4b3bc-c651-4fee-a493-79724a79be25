﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.dbQueries;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;

namespace Nbg.Ewallet.Repository;

public class AuthorizationRepositoryService : BaseRepositoryService, IAuthorizationRepositoryService
{
    public AuthorizationRepositoryService(
        IOptions<RepositorySettings> repositoryOptions,
        ILogger<AuthorizationRepositoryService> logger,
        IUnitOfWork unitOfWork) : base(unitOfWork, repositoryOptions, logger) { }

    public async Task RepoAuthorizationRequestSaveAsync(RepoAuthorizationRequest authRequest)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(AuthRequestQueries.InsertAuthorizationRequest, authRequest, tx);
        }, "A database error occurred while adding request authorization.");
    }

    public async Task<List<RepoAuthorizationRequest>> RepoAuthorizationRequestFindAllByWalletIdAndRequestStatusAsync(Guid walletId, RequestStatus? status)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            IEnumerable<RepoAuthorizationRequest> authRequests;
            if (status.HasValue)
                authRequests = await conn.QueryAsync<RepoAuthorizationRequest>(
                    AuthRequestQueries.GetAuthorizationRequestsByWalletIdAndStatus,
                    new { walletId, status = (DapperableEnum<RequestStatus>)status.Value },
                    tx);
            else
                authRequests = await conn.QueryAsync<RepoAuthorizationRequest>(
                    AuthRequestQueries.GetAuthorizationRequestsByWalletId,
                    new { walletId },
                    tx);

            return authRequests.ToList();
        }, "A database error occurred while fetching request authorizations by requestor wallet ID and request status.");
    }

    public async Task<RepoAuthorizationRequest?> RepoAuthorizationRequestFindOneByWalletIdAndAuthorizationRequestId(Guid walletId, Guid requestId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var response = await conn.QueryFirstOrDefaultAsync<RepoAuthorizationRequest>(
                AuthRequestQueries.GetWalletAuthorizationRequestByRequestId,
                new { walletId, requestId },
                tx);
            return response;
        }, "A database error occurred while fetching request authorization.");
    }

    public async Task RepoAuthorizationRequestUpdateAuthorizationRequestAsync(RepoAuthorizationRequest request)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(AuthRequestQueries.UpdateAuthorizationRequest, request, tx);
        }, "A database error occurred while updating the authorization request.");
    }

    public async Task<List<RepoAuthorizationRequest>> RepoAuthorizationRequestFindAllByRequestorWalletIdAndTargetWalletIdAndStatusAsync(Guid requestorWalletId, Guid targetWalletId,
         RequestStatus status)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var whereObject = new { RequestorWalletId = requestorWalletId, TargetWalletId = targetWalletId, Status = (DapperableEnum<RequestStatus>)status };
            var permissions = await conn.QueryAsync<RepoAuthorizationRequest>(
                AuthRequestQueries.GetRequestAuthorizationsByRequestorWalletIdAndTargetWalletIdAndRequestStatus,
                whereObject,
                tx);
            return permissions.ToList();
        }, "A database error occurred while fetching request authorizations by requestor wallet ID and target wallet ID and request status.");
    }
}
