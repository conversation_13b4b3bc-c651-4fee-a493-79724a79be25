﻿using System.Threading.Tasks;
using Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.CosmosConnector.Types.Constants;
using Nbg.NetCore.CosmosConnector.Types.Customer;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;
using Nbg.NetCore.CosmosConnector.Types.OpenSBAccount;
using Nbg.NetCore.CosmosConnector.Types.UpdIndicator;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxConnector : IMainFrameConnector
{
    public Task<NetCore.CosmosConnector.Types.Customer.Customer> GetCustomerDataAsync(string customerCode)
    {
        var result = new NetCore.CosmosConnector.Types.Customer.Customer
        {
            Type = CustomerTypes.LegalEntity,
            BasicInfo = new BasicInfo { TaxIdentificationNumber = "*********", CorporateName = "Babis and Sons", },
        };

        return Task.FromResult(result);
    }

    public Task<CustomerAuthorizationLevel> GetAuthorizationLevelAsync(string userId)
    {
        return Task.FromResult(new CustomerAuthorizationLevel { Category = "A", Approvals = 0 });
    }

    public Task<string> GetTaxIdFromUserProfileAsync(string userId, string customerCode)
    {
        return Task.FromResult("*********");
    }

    public Task<UpdIndicator> SubmitCustomerCommissionAsync(string userId, string customerCode, long? indicationKey, long? indicationValue)
    {
        return Task.FromResult(new UpdIndicator());
    }

    public Task<OpenSBAccount> CreateAccountAsync(string craCode, string userId, string productCode)
    {
        return Task.FromResult(new OpenSBAccount { AccountDetails = new AccountDetails { AccountInfo = new AccountInfo(), Iban = SandBoxRandomDataHelper.GenerateIBAN(), } });
    }

    public Task<CustomerProductDetails> GetCustomerProductDetailsAsync(string customerCode)
    {
        return Task.FromResult(new CustomerProductDetails());
    }

    public Task<string> GetAccountFromIBAN(string iban)
    {
        return Task.FromResult(AccountHelpers.IbanToAccount(iban));
    }

    public Task<string> GetIBANFromAccount(string account)
    {
        return Task.FromResult(AccountHelpers.IbanFromAccount(account, true));
    }
}
