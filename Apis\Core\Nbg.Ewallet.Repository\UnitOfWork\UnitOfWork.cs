﻿using System;
using System.Collections.Concurrent;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.NetCore.Data;

namespace Nbg.Ewallet.Repository.UnitOfWork;

public class UnitOfWork : IUnitOfWork
{
    private readonly IDbConnectorFactory _dbConnectorFactory;
    private readonly ILogger<UnitOfWork> _logger;
    private readonly ConcurrentDictionary<string, (IDbConnection conn, IDbTransaction? tx)> _dbContexts = new();
    private readonly Guid _uowId = Guid.NewGuid();
    private readonly UnitOfWorkOptions _options;
    public Guid Id => _uowId;

    public UnitOfWork(
        IDbConnectorFactory dbConnectorFactory,
        IOptions<UnitOfWorkOptions> options,
        ILogger<UnitOfWork> logger)
    {
        _dbConnectorFactory = dbConnectorFactory;
        _logger = logger;
        _options = options.Value ?? throw new ArgumentNullException(nameof(options), "UnitOfWorkOptions cannot be null");
        _logger.LogDebug("UnitOfWork created with ID {UowId} and DisposeBehavior {DisposeBehavior}", _uowId, _options.DisposeBehavior);
    }

    public IDbConnection GetConnection(string dbKey)
    {
        EnsureConnection(dbKey);
        return _dbContexts[dbKey].conn;
    }

    public IDbTransaction GetTransaction(string dbKey)
    {
        EnsureConnection(dbKey);
        if (_dbContexts[dbKey].tx == null)
        {
            var tx = _dbContexts[dbKey].conn.BeginTransaction();
            _dbContexts[dbKey] = (_dbContexts[dbKey].conn, tx);
            _logger.LogDebug("Transaction started for DB '{DbKey}' in UoW {UowId}", dbKey, _uowId);
        }
        return _dbContexts[dbKey].tx!;
    }

    private void EnsureConnection(string dbKey)
    {
        if (!_dbContexts.ContainsKey(dbKey))
        {
            var connector = _dbConnectorFactory.Get(dbKey);
            var connection = connector.Open();
            _dbContexts[dbKey] = (connection, null);
            _logger.LogDebug("Connection opened for DB '{DbKey}' in UoW {UowId}", dbKey, _uowId);
        }
    }

    public Task CommitPartialAsync() => CommitAsync(restartTransaction: true);
    public Task CommitFinalAsync() => CommitAsync(restartTransaction: false);

    public Task RollbackPartialAsync() => RollbackAsync(restartTransaction: true);
    public Task RollbackFinalAsync() => RollbackAsync(restartTransaction: false);

    private async Task CommitAsync(bool restartTransaction)
    {
        _logger.LogDebug("Committing all transactions in UnitOfWork {UowId}", Id);
        foreach (var dbKey in _dbContexts.Keys.ToList())
        {
            var (conn, tx) = _dbContexts[dbKey];
            if (tx == null)
            {
                throw new InvalidOperationException($"No active transaction found for DB '{dbKey}' in UoW {Id}. Call BeginNewTransactionAsync() first.");
            }

            tx.Commit();
            tx.Dispose();

            // Remove transaction from context after final commit
            _dbContexts[dbKey] = (conn, null);
        }

        if (!restartTransaction)
        {
            return;
        }

        foreach (var dbKey in _dbContexts.Keys.ToArray())
            await BeginNewTransactionAsync(dbKey);
    }

    private async Task RollbackAsync(bool restartTransaction)
    {
        _logger.LogDebug("Rolling back all transactions in UnitOfWork {UowId}", Id);
        foreach (var dbKey in _dbContexts.Keys.ToList())
        {
            var (conn, tx) = _dbContexts[dbKey];
            if (tx == null)
            {
                throw new InvalidOperationException($"No active transaction found for DB '{dbKey}' in UoW {Id}. Call BeginNewTransactionAsync() first.");
            }

            tx.Rollback();
            tx.Dispose();

            // Remove transaction from context after final commit
            _dbContexts[dbKey] = (conn, null);
        }

        if (!restartTransaction)
        {
            return;
        }

        foreach (var dbKey in _dbContexts.Keys.ToArray())
            await BeginNewTransactionAsync(dbKey);
    }

    public async Task BeginNewTransactionAsync(string dbKey)
    {
        EnsureConnection(dbKey);
        var current = _dbContexts[dbKey];
        current.tx?.Dispose();
        var newTx = current.conn.BeginTransaction();
        _dbContexts[dbKey] = (current.conn, newTx);
        _logger.LogDebug("New transaction started for DB '{DbKey}' in UoW {UowId}", dbKey, _uowId);
        await Task.CompletedTask;
    }

    public Task ExecuteInTransactionAsync(string dbKey, Func<IDbConnection, IDbTransaction, Task> action, string errorMessage)
    {
        return ExceptionHandlingHelper.ExecuteAsync(
            async () =>
            {
                var conn = GetConnection(dbKey);
                var tx = GetTransaction(dbKey);
                await action(conn, tx);
            },
            _logger,
            errorMessage);
    }

    public Task<TResult> ExecuteInTransactionAsync<TResult>(string dbKey, Func<IDbConnection, IDbTransaction, Task<TResult>> action, string errorMessage)
    {
        return ExceptionHandlingHelper.ExecuteAsync(
            async () =>
            {
                var conn = GetConnection(dbKey);
                var tx = GetTransaction(dbKey);
                return await action(conn, tx);
            },
            _logger,
            errorMessage);
    }

    public void Dispose()
    {
        ProcessTransactions();

        foreach (var (conn, tx) in _dbContexts.Values)
        {
            tx?.Dispose();
            conn.Dispose();
        }

        _dbContexts.Clear();
        _logger.LogDebug("UnitOfWork {UowId} disposed", _uowId);
    }

    private void ProcessTransactions()
    {
        var transactions = _dbContexts.Values
           .Select(x => x.tx)
           .Where(tx => tx != null)
           .ToList();

        if (transactions.Count <= 0)
        {
            _logger.LogWarning("UnitOfWork {UowId} there is no pending transactions", _uowId);
            return;
        }

        switch (_options.DisposeBehavior)
        {
            case DisposeBehavior.Rollback:
                _logger.LogWarning("UnitOfWork {UowId} rolling back automatically on dispose", _uowId);
                foreach (var tx in transactions)
                {
                    try { tx.Rollback(); }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Rollback failed during UnitOfWork.Dispose() for UoW {UowId}", _uowId);
                    }
                }
                break;
            case DisposeBehavior.Commit:
                _logger.LogWarning("UnitOfWork {UowId} committing automatically on dispose", _uowId);
                foreach (var tx in transactions)
                {
                    try { tx.Commit(); }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Commit failed during UnitOfWork.Dispose() for UoW {UowId}", _uowId);
                    }
                }
                break;
        }
    }
}
