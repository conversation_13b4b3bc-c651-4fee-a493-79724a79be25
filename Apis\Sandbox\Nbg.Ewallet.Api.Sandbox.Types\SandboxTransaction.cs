﻿using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Types;

public class SandboxTransaction
{
    [DataMember(Name = "transactionId")]
    public Guid TransactionId { get; set; }

    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    [DataMember(Name = "status")]
    public Repository.Types.TransactionStatus? Status { get; set; }

    [DataMember(Name = "transactionSubType")]
    public TransactionSubType? TransactionSubType { get; set; }

    [DataMember(Name = "transactionType")]
    public TransactionType? TransactionType { get; set; }

    [DataMember(Name = "userId")]
    public string UserId { get; set; }

    [DataMember(Name = "paymentCode")]
    public string PaymentCode { get; set; }

    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    [DataMember(Name = "creditIban")]
    public string CreditIban { get; set; }

    [DataMember(Name = "creditName")]
    public string CreditName { get; set; }

    [DataMember(Name = "creditBankBic")]
    public string CreditBankBic { get; set; }

    [DataMember(Name = "debitIban")]
    public string DebitIban { get; set; }

    [DataMember(Name = "debitName")]
    public string DebitName { get; set; }

    [DataMember(Name = "timestamp")]
    public DateTime Timestamp { get; set; }

    [DataMember(Name = "instant")]
    public bool? Instant { get; set; }

    [DataMember(Name = "priority")]
    public int Priority { get; set; }

    [DataMember(Name = "serialNum")]
    public string SerialNum { get; set; }

    [DataMember(Name = "transactionDate")]
    public DateTime? TransactionDate { get; set; }

    [DataMember(Name = "branch")]
    public string Branch { get; set; }

    [DataMember(Name = "trans")]
    public string Trans { get; set; }

    [DataMember(Name = "transDescription")]
    public string TransDescription { get; set; }

    [DataMember(Name = "amountEquivalent")]
    public decimal AmountEquivalent { get; set; }

    [DataMember(Name = "creditDebit")]
    public string CreditDebit { get; set; }

    [DataMember(Name = "valeur")]
    public DateTime? Valeur { get; set; }

    [DataMember(Name = "description")]
    public string Description { get; set; }

    [DataMember(Name = "accountingBalance")]
    public decimal AccountingBalance { get; set; }

    [DataMember(Name = "reference")]
    public string Reference { get; set; }

    [DataMember(Name = "externalSystem")]
    public string ExternalSystem { get; set; }

    [DataMember(Name = "relatedAccount")]
    public string RelatedAccount { get; set; }

    [DataMember(Name = "relatedName")]
    public string RelatedName { get; set; }

    [DataMember(Name = "account")]
    public string Account { get; set; }

    [DataMember(Name = "counterpartyName")]
    public string CounterpartyName { get; set; }

    [DataMember(Name = "customerId")]
    public string CustomerId { get; set; }

    [DataMember(Name = "customerInfo")]
    public string CustomerInfo { get; set; }

    [DataMember(Name = "operation")]
    public string Operation { get; set; }

    [DataMember(Name = "operationDesc")]
    public string OperationDesc { get; set; }

    [DataMember(Name = "reasonInfo")]
    public string ReasonInfo { get; set; }

    [DataMember(Name = "counterpartyBank")]
    public string CounterpartyBank { get; set; }

    [DataMember(Name = "additionalInfo")]
    public string AdditionalInfo { get; set; }

    [DataMember(Name = "batchId")]
    public Guid? BatchId { get; set; }

    [DataMember(Name = "batchDescription")]
    public string BatchDescription { get; set; }

    public string SubmittedBy { get; set; }

    public string ApprovedBy { get; set; }

    public string ExecutedBy { get; set; }
    public string RejectedBy { get; set; }

    public string Result { get; set; }

    public string ResultReason { get; set; }

    [DataMember(Name = "commission")]
    public decimal? Commission { get; set; }

    [DataMember(Name = "requestJson")]
    public string RequestJson { get; set; }
}
