﻿using Nbg.Ewallet.Repository.Types;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Consent data object
/// </summary>
[DataContract]
public class EWalletConsentData
{
    /// <summary>
    /// Wallet Default IBAN
    /// </summary>
    [DataMember(Name = "defaultIban")]
    public string DefaultIban { get; set; }

    /// <summary>
    /// Wallet Name
    /// </summary>
    [DataMember(Name = "walletName")]
    public string WalletName { get; set; }

    /// <summary>
    /// Id of the discount applied
    /// </summary>
    [DataMember(Name = "planid")]
    public SubscriptionTier PlanId { get; set; }
}
