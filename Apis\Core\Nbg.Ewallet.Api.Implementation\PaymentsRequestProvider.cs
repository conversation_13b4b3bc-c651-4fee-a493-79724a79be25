﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using ibank.ThirdParty.Types;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Payments;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Records;
using TransactionStatus = Nbg.Ewallet.Repository.Types.TransactionStatus;

namespace Nbg.Ewallet.Api.Implementation;

public class PaymentsRequestProvider : PayTransBase, IPaymentsRequestProvider
{
    private readonly ITransactionRepositoryService _transactionRepositoryService;
    private readonly ITPPApiClientService _tppApiClientService;
    private readonly ICustomerCommissionService _customerCommissionService;
    private readonly IAccountsApiClientService _accountsApiClientService;
    private readonly IIbanAccountUtility _ibanAccountUtility;
    private readonly IMapper _mapper;

    public PaymentsRequestProvider(
        IMapper mapper,
        ITransactionRepositoryService transactionRepositoryService,
        ITPPApiClientService tppApiClientService,
        IHttpContextRepositoryService httpContextRepositoryService,
        ICustomerCommissionService customerCommissionService,
        IAccountsApiClientService accountsApiClientService,
        IIbanAccountUtility ibanAccountUtility,
        IAuthContextService authContextService)
        : base(httpContextRepositoryService, authContextService)
    {
        _transactionRepositoryService = transactionRepositoryService;
        _tppApiClientService = tppApiClientService;
        _customerCommissionService = customerCommissionService;
        _accountsApiClientService = accountsApiClientService;
        _ibanAccountUtility = ibanAccountUtility;
        _mapper = mapper;
    }

    public ExtendedPaymentResponse EnrichResponse(ExtendedPaymentsRequest request, List<RepoTransaction> transactions)
    {
        var transferResponse = request.EnrichResponse(new ExtendedPaymentResponse()
        {
            RequiresApproval = transactions.Select(x => x.Status.Value == TransactionStatus.PENDING_APPROVAL).FirstOrDefault(),
            TransactionId = transactions.Select(x => x.TransactionId).FirstOrDefault()
        });
        return transferResponse;
    }

    public async Task<CommissionResponse> GetCommissionAsync(ExtendedPaymentsRequest request, Guid walletId)
    {
        (var userId, var customerCode, var executedAs) = GetUserAndCustomerCode();
        request.UserID = executedAs;

        await _customerCommissionService.UpdateCustomerCommissionAsync(walletId, TransactionSubType.GenericPayment, userId, customerCode);
        var response = await _tppApiClientService.GetCommissionAsync(request);
        return response;
    }

    public TotalAmountWithCommission GetTotalAmountWithCommission(ExtendedPaymentsRequest request, CommissionResponse commissionResponse)
    {
        var totalAmount = request.SettlementInfo.Amount + commissionResponse.CommissionInfo.Total;
        return new TotalAmountWithCommission(request.SettlementInfo.CCY ?? "EUR", totalAmount, commissionResponse.CommissionInfo.Total);
    }

    public async Task<ExtendedPaymentResponse> HandleTransactionExecutionsAsync(ExtendedPaymentsRequest request, List<RepoTransaction> transactions, RepoWallet wallet)
    {
        (var userId, var customerCode, var executedAs) = GetUserAndCustomerCode();

        request.UserID = executedAs;

        await _customerCommissionService.UpdateCustomerCommissionAsync(wallet.WalletId, TransactionSubType.GenericPayment, userId, customerCode);

        var response = await _tppApiClientService.PayAsync(request);
        var transaction = transactions.FirstOrDefault(x => x.TransactionId == request.TransactionId);
        transaction.ExecutedAs = executedAs;
        transaction.ExecutedBy = userId;
        await response.HandleResultAsync(transaction, _transactionRepositoryService);

        var paymentsResponse = _mapper.Map<ExtendedPaymentResponse>(response.Payload);
        paymentsResponse.TransactionId = transaction.TransactionId;
        return paymentsResponse;
    }

    public Task<List<RepoTransaction>> InitiateRepoTransactions(ExtendedPaymentsRequest request, RepoWallet wallet, string userId)
    {
        var transaction = request.ToPendingRepoTransaction(wallet, userId);
        return Task.FromResult(new List<RepoTransaction> { transaction });
    }

    public void SetNewDebitAccount(ExtendedPaymentsRequest request, string ibanAccount)
    {
        request.Debtor.DebtorAccount.IBAN = ibanAccount;
    }

    public async Task<ExtendedPaymentResponse> TransactionForecastExecutionsAsync(ExtendedPaymentsRequest request, RepoWallet wallet)
    {
        (var userId, _, var executedAs) = GetUserAndCustomerCode();

        var validationResult = await request.ValidateAccountAvailableToUser(wallet, userId, _accountsApiClientService);

        var accountBalance = await _accountsApiClientService.GetAccountBalanceAsync(new GetAccountBalanceRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(validationResult.IBAN),
            UserId = userId,
            Currency = "070"
        });

        var amount = request.SettlementInfo.Amount;
        var balance = accountBalance.AvailableBalance;
        balance -= amount;
        if (balance < 0)
        {
            throw new InsufficientBalanceException();
        }

        request.UserID = executedAs;
        var commision = await _tppApiClientService.GetCommissionAsync(request);
        balance -= commision.CommissionInfo.Total;
        if (balance < 0)
        {
            throw new InsufficientBalanceException();
        }

        return new() { TransactionId = request.TransactionId };
    }

    public async Task<ValidateRequestResult> ValidateRequest(ExtendedPaymentsRequest request, RepoWallet wallet)
    {
        (var userId, _, _) = GetUserAndCustomerCode();
        return await request.ValidateAccountAvailableToUser(wallet, userId, _accountsApiClientService);
    }
}
