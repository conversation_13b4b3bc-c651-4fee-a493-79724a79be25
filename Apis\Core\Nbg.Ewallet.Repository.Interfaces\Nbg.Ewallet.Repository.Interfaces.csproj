﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>true</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Configurations>Debug;Release;Sandbox</Configurations>
        <LangVersion>latest</LangVersion>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="ibank.ThirdParty.Core.Types" Version="1.0.0.11" />
        <PackageReference Include="ibank.ThirdParty.Types" Version="2.0.11" />
        <PackageReference Include="ibank.ThirdPartyPayment.Types" Version="2.0.0.7" />
        <PackageReference Include="ibank.ThirdPartyPayment.Types.Core" Version="1.0.0.8" />
        <PackageReference Include="Nbg.NetCore.Common.Types" Version="8.0.1" />
        <PackageReference Include="ibank.ThirdParty.Types" Version="2.0.11" />
        <PackageReference Include="ibank.ThirdParty.Core.Types" Version="1.0.0.11" />
        <PackageReference Include="nbg.netcore.consent.repository.types" Version="1.0.2" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.Customer" Version="1.0.28" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails" Version="1.0.3" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.GetCustomerDetailsAdvanceSearch" Version="1.0.0" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.OpenSbAccount" Version="1.0.3" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.UpdIndicator" Version="1.0.3" />
        <PackageReference Include="Nbg.NetCore.Services.Cics.Http" Version="9.0.4" />
        <PackageReference Include="Nbg.NetCore.Utilities" Version="8.0.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Nbg.Ewallet.Repository.Types\Nbg.Ewallet.Repository.Types.csproj" />
    </ItemGroup>

</Project>
