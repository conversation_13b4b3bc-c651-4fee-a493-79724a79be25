﻿namespace Nbg.Ewallet.Repository.dbQueries;

internal class WalletUserPermissionsQueries
{
    internal const string InsertUserPermission =
        "INSERT INTO [EWallet].[dbo].[UserPermissions] " +
        "(Id,UserId,WalletId,Submit,Approve,BalanceView,TransactionView,Admin,InheritsAuthorizations,ExpirationDate,CreatedAt,AssignedBy) " +
        "VALUES " +
        "(@id,@userId,@walletId,@submit,@approve,@balanceView,@transactionView,@admin,@inheritsAuthorizations,@expirationDate,@createdAt,@assignedBy)";

    internal const string UpdateUserPermission =
        @"UPDATE [EWallet].[dbo].[UserPermissions]
                SET Approve = @Approve,
                BalanceView = @BalanceView,
                TransactionView = @TransactionView,
                InheritsAuthorizations = @InheritsAuthorizations,
                ExpirationDate = @ExpirationDate,
                RevokedBy = @RevokedBy
            WHERE Id = @Id";

    internal const string ExpireUserPermission =
        "UPDATE [EWallet].[dbo].[UserPermissions] " +
        "SET ExpirationDate = @ExpirationDate " +
        "where Id = @ID";

    internal const string GetActiveExistingUserPermissions =
        "SELECT Id, UserId, WalletId, Submit, BalanceView, TransactionView, Admin, ExpirationDate, CreatedAt, AssignedBy, RevokedBy, Approve, InheritsAuthorizations " +
        "FROM [EWallet].[dbo].[UserPermissions] " +
        "WHERE UserId = @userid " +
        "AND WalletId = @walletid " +
        "AND ExpirationDate > GETDATE() " +
        "ORDER BY CreatedAt DESC";

    internal const string GetActiveUserPermissions =
        "SELECT Id, UserId, WalletId, Submit, BalanceView, TransactionView, Admin, ExpirationDate, CreatedAt, AssignedBy, RevokedBy, Approve, InheritsAuthorizations " +
        "FROM [EWallet].[dbo].[UserPermissions] " +
        "WHERE WalletId = @walletId " +
        "AND ExpirationDate > GETDATE() " +
        "AND RevokedBy IS NULL";

    internal const string GetAllExpiredUserPermissions =
        "SELECT Id, UserId, WalletId, Submit, BalanceView, TransactionView, Admin, ExpirationDate, CreatedAt, AssignedBy, RevokedBy, Approve, InheritsAuthorizations " +
        "FROM [EWallet].[dbo].[UserPermissions] " +
        "WHERE WalletId = @walletId " +
        "AND ExpirationDate < GETDATE()";

    internal const string GetActiveUserPermissionsForUser = GetActiveUserPermissions + " AND UserId = @userId";
    internal const string GetAllExpiredUserPermissionsForUser = GetAllExpiredUserPermissions + " AND UserId = @userId";

    internal const string GetCurrentActiveUserPermission =
        "SELECT Id, UserId, WalletId, Submit, BalanceView, TransactionView, Admin, ExpirationDate, CreatedAt, AssignedBy, RevokedBy, Approve, InheritsAuthorizations " +
        "FROM [EWallet].[dbo].[UserPermissions] " +
        "WHERE UserId = @userid " +
        "AND WalletId = @walletid " +
        "AND RevokedBy IS NULL " +
        "AND ExpirationDate > @CurrentDate " +
        "ORDER BY CreatedAt DESC";
}
