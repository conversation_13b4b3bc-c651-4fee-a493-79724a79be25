﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

public class TransfersForecatsResponse
{
    /// <summary>
    /// Transaction Id
    /// </summary>
    [DataMember(Name = "transactionId")]
    public Guid TransactionId { get; set; }

    public static TransfersForecatsResponse ForecatsResponse(TransfersResponse response)
    {
        var forecastResponse = new TransfersForecatsResponse
        {
            TransactionId = response.TransactionId
        };

        return forecastResponse;
    }
}
