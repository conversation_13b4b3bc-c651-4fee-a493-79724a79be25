﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Implementation;
public class LimitCalculatorService : ILimitCalculatorService
{
    private readonly ITransactionRepositoryService _transactionRepository;
    private readonly ILimitRepositoryService _limitRepositoryService;
    private readonly IAuthContextService _authContextService;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;

    public LimitCalculatorService(ITransactionRepositoryService transactionRepository,
        ILimitRepositoryService limitRepositoryService,
        IAuthContextService authContextService,
        IHttpContextRepositoryService httpContextRepositoryService)
    {
        _transactionRepository = transactionRepository;
        _limitRepositoryService = limitRepositoryService;
        _authContextService = authContextService;
        _httpContextRepositoryService = httpContextRepositoryService;
    }

    public async Task<bool> HasApproveTransferPermissionAsync(LimitCalculatorContext context)
    {
        if (context == null) return false;
        var permission = _authContextService.GetContext().ActivePermission ?? throw new PermissionNotFoundException();
        if (permission.Approve) return await IsWithinApprovalLimitAsync(context, permission);
        return false;
    }

    public async Task<bool> HasSubmitTransferPermissionAsync(LimitCalculatorContext context)
    {
        if (context == null) return false;
        var permission = _authContextService.GetContext().ActivePermission ?? throw new PermissionNotFoundException();
        if (permission.Approve) return true;
        if (permission.Submit) return await IsWithinSubmitLimitAsync(context, permission);
        return false;
    }

    #region [ Private Helpers ]

    private async Task<bool> IsWithinApprovalLimitAsync(LimitCalculatorContext context, IPermission permission)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var transactions = await _transactionRepository.FindAllByWalletIdAndTransactionSubTypeAndExecutedByAsync(context.WalletId.ToString(), context.TransactionSubTypeGroups.Select(x => x.TransactionSubType), userId);
        transactions = transactions.Where(x => x.Status == TransactionStatus.EXECUTED).ToList();
        return await IsWithinLimitAsync(transactions, permission, context);
    }

    private async Task<bool> IsWithinSubmitLimitAsync(LimitCalculatorContext context, IPermission permission)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var transactions = await _transactionRepository.FindAllByWalletIdAndTransactionSubTypeAndSubmittedByAsync(context.WalletId.ToString(), context.TransactionSubTypeGroups.Select(x => x.TransactionSubType), userId);
        transactions = transactions.Where(x => x.Status != TransactionStatus.EXECUTED).ToList();
        return await IsWithinLimitAsync(transactions, permission, context);
    }

    private async Task<bool> IsWithinLimitAsync(IEnumerable<RepoTransaction> transactions, IPermission permission, LimitCalculatorContext context)
    {
        foreach (var transactionSubTypeGroup in context.TransactionSubTypeGroups)
        {
            var isWithinLimit = await IsWithinLimitPerTransactionSubTypeAsync(transactions, permission, transactionSubTypeGroup);
            if (isWithinLimit == false) return false;
        }
        return true;
    }

    private async Task<bool> IsWithinLimitPerTransactionSubTypeAsync(IEnumerable<RepoTransaction> transactions, IPermission permission, TransactionSubTypeGroup transactionSubTypeGroup)
    {
        var limits = await _limitRepositoryService.GetByPermissionIdTypeAsync(permission.Id.ToString(), transactionSubTypeGroup.TransactionSubType.ToLimitTransactionType());
        foreach (var limit in limits)
        {
            var amountTotal = limit.TimeFrame.Value switch
            {
                TimeFrameType.DAILY => transactions.Where(x => x.CreatedAt.Date == DateTime.UtcNow.Date).Sum(x => x.Amount),
                TimeFrameType.MONTHLY => transactions.Where(x => x.CreatedAt.Month == DateTime.UtcNow.Month).Sum(x => x.Amount),
                TimeFrameType.YEARLY => transactions.Where(x => x.CreatedAt.Year == DateTime.UtcNow.Year).Sum(x => x.Amount),
                _ => 0.0m,
            };
            return limit.Amount >= amountTotal + transactionSubTypeGroup.Amount;
        }

        //no limits found and ApproveType is Unlimited
        return true;
    }

    #endregion
}
