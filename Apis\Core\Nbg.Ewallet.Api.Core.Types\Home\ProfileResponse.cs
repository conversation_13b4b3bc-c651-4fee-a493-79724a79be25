﻿using System.Collections.Generic;
using System.Runtime.Serialization;
using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// User Profile information.
/// </summary>
public class ProfileResponse
{
    /// <summary>
    /// The User Id used to log in to IB.
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserId { get; set; }

    /// <summary>
    /// The Wallet Id that this User Id is connected to.
    /// </summary>
    [DataMember(Name = "walletId")]
    public string WalletId { get; set; }

    /// <summary>
    /// The User Permissions assigned to this User Id for the connected wallet.
    /// If no active permissions are found, null is returned.
    /// </summary>
    [DataMember(Name = "permissions")]
    public UserPermission Permissions { get; set; }

    /// <summary>
    /// Access granted to other wallets along with the specific permissions and limits.
    /// </summary>
    [DataMember(Name = "authorizations")]
    public List<WalletPermission> Authorizations { get; set; }
}
