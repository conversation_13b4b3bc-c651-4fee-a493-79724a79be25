﻿using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Nbg.Ewallet.Api.Types.TimeConverters;

public class SystemTextJsonDateTimeConverter : JsonConverter<DateTime>
{
    private const string _format = "yyyy-MM-ddTHH:mm:ss.fffZ";

    public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return DateTime.Parse(reader.GetString());
    }

    public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToUniversalTime().ToString(_format));
    }
}
