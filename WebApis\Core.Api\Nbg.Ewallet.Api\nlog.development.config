﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      throwExceptions="false"
      throwConfigExceptions="true"
      internalLogLevel="Debug"
      internalLogFile="c:\logs\nlog-internal.log"
      globalThreshold="Trace">
    <extensions>
        <add assembly="Nlog.WindowsEventLog"/>
        <add assembly="Nlog.Web.AspNetCore"/>
    </extensions>
    <variable name="logRootFolder"
	          value="c:\logs\ewallet"/>
    <variable name="applicationName"
	          value="ewallet"/>
    <targets>
        <target xsi:type="EventLog"
		        name="eventLog"
		        log="${applicationName}"
		        source="${applicationName}-proxy"
		        layout="${callsite:className=true:includeNamespace=true:includeSourcePath=true:methodName=true} ${message}${newline}${exception:format=ToString}"/>
        <target xsi:type="File"
		        name="filelogger"
		        fileName="${logRootFolder}\logs-${shortdate}.log"
		        createDirs="true"
		        layout="${longdate}|${aspnet-traceidentifier}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}"/>
        <target xsi:type="EventLog"
		        name="eventLogBigData"
		        log="BD-BigData"
		        source="${applicationName}-BD-BigData"
		        layout="${applicationName}|${threadid:padding=3}|${logger}|${message}${newline}${exception:format=ToString}"/>
        <target xsi:type="EventLog"
		        name="eventLogBigDataClientTime"
		        log="BD-Time"
		        source="${applicationName}-BD-Time"
		        layout="${threadid:padding=3}|${message}"/>
        <target xsi:type="EventLog"
		        name="eventLogBigDataClientFailedCalls"
		        log="BD-FailedCalls"
		        source="${applicationName}-BD-FailedCalls"
		        layout="${threadid:padding=3}|${message}"/>
        <target xsi:type="Debugger"
		        name="debugger"
		        layout="${longdate} ${logger} ${uppercase:${level}} - ${message}"/>
    </targets>
    <rules>
        <logger name="BigDataClientFailedCalls"
		        minlevel="Debug"
		        writeTo="eventLogBigDataClientFailedCalls"
		        final="true"/>
        <logger name="BigDataClientCalls"
		        minlevel="Debug"
		        writeTo="eventLogBigData"
		        final="true"/>
        <logger name="BigDataClientTime"
		        minlevel="Debug"
		        writeTo="eventLogBigDataClientTime"
		        final="true"/>
        <logger name="NBG.BigData.*"
		        minlevel="Debug"
		        writeTo="eventLogBigData"
		        final="true"/>
        <!-- NEW: capture ALL logs Debug+ to file -->
        <logger name="*"
		        minlevel="Debug"
		        writeTo="filelogger"/>
        <!-- EventLog only for Warn+ -->
        <logger name="*"
		        minlevel="Warn"
		        writeTo="eventLog"/>
    </rules>
</nlog>
