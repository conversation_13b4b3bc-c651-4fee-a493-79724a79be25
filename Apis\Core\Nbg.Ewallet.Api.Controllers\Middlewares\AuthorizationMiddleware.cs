﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

//TODO: maybe move on implementation project
namespace Nbg.Ewallet.Api.Middlewares;

public class AuthorizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuthorizationMiddleware> _logger;

    public AuthorizationMiddleware(
        RequestDelegate next,
        ILogger<AuthorizationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task Invoke(HttpContext context, IAuthContextService authContextService)
    {

        var httpContextRepositoryService = context.RequestServices.GetRequiredService<IHttpContextRepositoryService>();
        var userId = httpContextRepositoryService.GetUserId();
        if (ShouldSkipAuthorization(context.Request.Path) || userId == null)
        {
            _logger.LogDebug("Skipping eWallet Authorization for path: {Path}", context.Request.Path);
            await _next(context);
            return;
        }

        var authContext = new AuthorizationContext();
        context.Items["AuthContext"] = authContext;

        var userPermissionsRepositoryService = context.RequestServices.GetRequiredService<IUserPermissionsRepositoryService>();
        var userPermissions = await userPermissionsRepositoryService.FindOneByActiveAndUserIdAsync(userId);
        if (userPermissions == null)
        {
            if (GetWalletIdFromRoute(context) != null)
            {
                _logger.LogWarning("No User Permissions found for user with ID {UserId}", userId);
                throw new PermissionNotFoundException();
            }

            await _next(context);
            return;
        }

        var walletRepositoryService = context.RequestServices.GetRequiredService<IWalletRepositoryService>();
        var userWallet = await walletRepositoryService.FindOneByIdAsync(userPermissions.WalletId.ToString());
        var walletId = GetWalletIdFromRoute(context);

        var walletPermissionsRepositoryService = context.RequestServices.GetRequiredService<IWalletPermissionsRepositoryService>();
        var walletPermissions = await walletPermissionsRepositoryService
            .FindAllByWalletIdAndTargetWalletIdAsync(walletId.GetValueOrDefault(), userWallet.WalletId);
        authContext.ActivePermission = await GetActivePermission(context, walletRepositoryService, userPermissions, walletPermissions);

        authContextService.SetContext(authContext);
        await _next(context);
    }

    private static bool ShouldSkipAuthorization(PathString path)
    {
        return path == "/health"
               || path.Value.Contains("consents")
               || path.Value.Contains("index.html")
               || path.Value.Contains("swagger")
               || path.Value.Contains("sandbox")
               || path.Value.Contains("/management")
               || path.Value.Contains("/wallet/register")
               || path.Value.Contains("/wallet/search-wallets");
    }

    private async Task<IPermission> GetActivePermission(HttpContext context, IWalletRepositoryService walletRepositoryService, RepoUserPermission userPermission, List<RepoWalletPermission> walletPermissions)
    {
        var walletId = GetWalletIdFromRoute(context);

        //Check if should default to user permissions because Wallet Id is not present in route.
        if (!walletId.HasValue)
        {
            _logger.LogDebug("No Wallet ID in route, default to user permissions: {UserPermission}", userPermission.ToLogString());
            return userPermission;
        }

        //Verify that target wallet exists
        var targetWallet = await walletRepositoryService.FindOneByIdAsync(walletId.ToString());
        if (targetWallet == null)
        {
            _logger.LogWarning("No wallet found with ID {WalletId}", walletId);
            throw new WalletNotFoundException();
        }

        //Check if target wallet is the wallet connected to user
        //If not, will attempt to retrieve wallet permissions
        if (userPermission.WalletId == walletId)
        {
            _logger.LogDebug("Active permissions found: {UserPermission}", userPermission.ToLogString());
            return userPermission;
        }

        //Check if the user has permission to access external wallets
        if (!userPermission.InheritsAuthorizations)
        {
            _logger.LogWarning("No Active permissions found");
            throw new PermissionNotFoundException();
        }

        //If user can access external wallets, assign the wallet permissions to active permissions
        IPermission permission = walletPermissions
            .Where(x => x.ExpirationDate > DateTime.UtcNow)
            .FirstOrDefault();

        if (permission == null)
        {
            _logger.LogWarning("No valid wallet permission found for wallet with ID {WalletId}", walletId);
            throw new PermissionNotFoundException();
        }

        _logger.LogDebug("Active permissions found: {UserPermission}", permission.ToLogString());

        return permission;
    }

    private Guid? GetWalletIdFromRoute(HttpContext context)
    {
        if (!context.Request.RouteValues.TryGetValue("walletId", out var walletIdRoute))
        {
            return null;
        }

        if (walletIdRoute is Guid walletId)
        {
            return walletId;
        }

        if (walletIdRoute is string walletIdString && Guid.TryParse(walletIdString, out var walletIdParsed))
        {
            return walletIdParsed;
        }

        return null;

        //var walletIdAsAString = (context.Request.RouteValues["walletId"] ?? string.Empty).ToString();
        //if (string.IsNullOrWhiteSpace(walletIdAsAString) || !Guid.TryParse(walletIdAsAString, out var walletId)) return null;
        //return walletId;
    }
}

public static class AuthorizationMiddlewareExtensions
{
    public static IApplicationBuilder UseEWalletAuthorization(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<AuthorizationMiddleware>();
    }
}
