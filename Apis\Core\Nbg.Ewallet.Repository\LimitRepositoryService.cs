﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.dbQueries;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;

namespace Nbg.Ewallet.Repository;

public class LimitRepositoryService : BaseRepositoryService, ILimitRepositoryService
{
    public LimitRepositoryService(
        IOptions<RepositorySettings> repositoryOptions,
        ILogger<LimitRepositoryService> logger,
        IUnitOfWork unitOfWork) : base(unitOfWork, repositoryOptions, logger) { }

    public async Task<List<RepoLimit>> GetByPermissionIdTypeAsync(string permissionId, LimitTransactionType transactionType)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var wallets = await conn.QueryAsync<RepoLimit>(
                LimitQueries.GetLimitByPermissionIdType,
                new { PermissionId = permissionId, TransactionType = (DapperableEnum<LimitTransactionType>)transactionType },
                tx);
            return wallets.ToList();
        }, "An error occurred while fetching limits by permission ID and transaction type.");
    }

    public async Task<List<RepoLimit>> GetByPermissionIdAsync(string permissionId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var repoLimitsList = await conn.QueryAsync<RepoLimit>(
                LimitQueries.GetLimitByPermissionId,
                new { PermissionId = permissionId },
                tx);
            return repoLimitsList.ToList();
        }, "An error occurred while fetching limits by permission ID.");
    }

    public async Task<List<RepoLimit>> RepoLimitFindAllByPermissionIdAsync(Guid permissionId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var response = await conn.QueryAsync<RepoLimit>(
                LimitQueries.GetUserLimits,
                new { PermissionId = permissionId },
                tx);
            return response.ToList();
        }, "A database error occurred while fetching user wallet limits.");
    }

    public async Task<List<RepoLimit>> RepoLimitFindAllByPermissionId(Guid permissionId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var permissions = await conn.QueryAsync<RepoLimit>(
                LimitQueries.GetUserLimits,
                new { permissionId },
                tx);
            return permissions.ToList();
        }, "A database error occurred while fetching user limits for permission ID.");
    }

    public async Task InsertUserLimitsAsync(IEnumerable<RepoLimit> limits)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(
                LimitQueries.InsertUserLimits,
                limits,
                tx);
        }, "An error occurred while inserting user limits.");
    }
}
