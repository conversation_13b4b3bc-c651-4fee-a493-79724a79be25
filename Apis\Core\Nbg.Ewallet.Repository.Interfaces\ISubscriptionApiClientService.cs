﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface ISubscriptionApiClientService
{
    Task<SubscriptionApiResponse> CreateSubscriptionAsync(CreateSubscriptionRequest request);
    Task<SubscriptionApiResponse> GetSubscriptionAsync(Guid subscriptionId);
    Task<SubscriptionApiResponse> UpdateSubscriptionAsync(UpdateSubscriptionRequest request);
    Task<SubscriptionApiResponse> OptOutSubscriptionAsync(OptOutSubscriptionRequest request);
    Task<AvailableTiersApiResponse> GetAvailableSubscriptionTiersAsync();
}
