﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// Controller providing Transfers functionality
/// </summary>
[Produces("application/json")]
[Consumes("application/json")]
[ApiExplorerSettings(GroupName = "Transfers")]
public class TransfersController : BaseController
{
    private readonly ITransfersService _transfersService;

    public TransfersController(ITransfersService transfersService,
        ILogger<TransfersController> logger) : base(logger)
    {
        _transfersService = transfersService;
    }

    /// <summary>
    /// Transfer from a wallet to a wallet, or from a wallet to an NBG account
    /// </summary>
    [HttpPost]
    [Route("{walletId}/transfer", Name = "Transfer")]
    public async Task<ActionResult<TransfersResponse>> Transfer(TransfersRequest request, [FromRoute] string walletId)
    {
        return await _transfersService.TransferAsync(request, walletId);
    }

    /// <summary>
    /// Forecasts a single transfer transaction to validate if sufficient balance is available, without executing the actual transfer.
    /// </summary>
    [HttpPost]
    [Route("{walletId}/transfer-forecast", Name = "Transfer Forecast")]
    public async Task<ActionResult<TransfersForecatsResponse>> TransferForecast(TransfersRequest request, [FromRoute] string walletId)
    {
        return await _transfersService.TransferForecastAsync(request, walletId);
    }

    /// <summary>
    /// Transfer from a wallet to many, or from a wallet to many NBG accounts
    /// </summary>
    [HttpPost]
    [Route("{walletId}/batch", Name = "Batch")]
    public async Task<ActionResult<BatchTransfersResponse>> Batch(BatchTransfersRequest request, [FromRoute] string walletId)
    {
        return await _transfersService.BatchAsync(request, walletId);
    }

    /// <summary>
    /// Forecasts a batch of transfer transactions to validate if sufficient balance is available for each, without executing the actual transfers.
    /// </summary>
    [HttpPost]
    [Route("{walletId}/batch-forecast", Name = "Batch Forecast")]
    public async Task<ActionResult<BatchTransfersForecastResponse>> BatchForecast(BatchTransfersRequest request, [FromRoute] string walletId)
    {
        return await _transfersService.BatchForecastAsync(request, walletId);
    }

    /// <summary>
    /// Calculates the fees applied to the transaction
    /// </summary>
    [HttpPost]
    [Route("{walletId}/calculateFees", Name = "CalculateFees")]
    public async Task<ActionResult<TransferExpensesCommissionsResponse>> CalculateFees(TransfersRequestBase request, [FromRoute] Guid walletId)
    {
        return await _transfersService.CalculateFeesAsync(request, walletId);
    }

    /// <summary>
    /// Calculates the fees applied to the transaction
    /// </summary>
    [HttpPost]
    [Route("{walletId}/batch/calculateFees", Name = "BatchCalculateFees")]
    public async Task<ActionResult<BatchTransferExpensesCommissionsResponse>> BatchCalculateFees(BatchTransfersRequest request, [FromRoute] Guid walletId)
    {
        return await _transfersService.BatchCalculateFeesAsync(request, walletId);
    }
}
