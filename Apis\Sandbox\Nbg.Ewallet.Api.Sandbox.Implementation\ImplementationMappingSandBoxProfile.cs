﻿using AutoMapper;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;

namespace Nbg.Ewallet.Api.Sandbox.Implementation;

public class ImplementationMappingSandBoxProfile : Profile
{
    public ImplementationMappingSandBoxProfile()
    {
        CreateMap<RepoLimit, SandboxLimit>();
        CreateMap<SandboxLimit, RepoLimit>();

        CreateMap<RepoUserPermission, SandboxUserPermission>()
            .ForMember(x => x.CreationDate, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(rw => rw.WalletId, opt => opt.MapFrom(src => src.WalletId))
            .ForMember(rw => rw.Id, opt => opt.MapFrom(src => src.Id));

        CreateMap<SandboxUserPermission, RepoUserPermission>()
             .ForMember(x => x.CreatedAt, opt => opt.MapFrom(src => src.CreationDate))
             .ForMember(x => x.Id, opt => opt.MapFrom(src => src.Id));

        CreateMap<SandboxWallet, RepoWallet>()
            .ForMember(rw => rw.WalletId, opt => opt.MapFrom(src => src.WalletId));
        CreateMap<RepoWallet, SandboxWallet>();

        CreateMap<SandboxWalletPermission, RepoWalletPermission>()
           .ForMember(x => x.CreatedAt, opt => opt.MapFrom(src => src.CreationDate));
        CreateMap<RepoWalletPermission, SandboxWalletPermission>()
            .ForMember(x => x.CreationDate, opt => opt.MapFrom(src => src.CreatedAt));

        CreateMap<SubscriptionPlan, SandboxSubscriptionPlan>();
        CreateMap<SandboxSubscriptionPlan, SubscriptionPlan>();

        CreateMap<Terms, SandboxTerms>();
        CreateMap<SandboxTerms, Terms>();

        CreateMap<SandboxSubscriptionBundle, SubscriptionBundle>();
        CreateMap<SubscriptionBundle, SandboxSubscriptionBundle>();

        CreateMap<SandboxSubscription, SubscriptionApiResponse>();
        CreateMap<SubscriptionApiResponse, SandboxSubscription>();
    }
}
