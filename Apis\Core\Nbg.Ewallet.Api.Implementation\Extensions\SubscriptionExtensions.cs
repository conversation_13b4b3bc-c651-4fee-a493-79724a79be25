﻿using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Nbg.Ewallet.Api.Implementation.Extensions;
public static class SubscriptionExtensions
{
    public static SubscriptionResponse ToSubscriptionResponse(this SubscriptionApiResponse apiResponse)
    {
        if (apiResponse == null) return new SubscriptionResponse();
        var response = new SubscriptionResponse()
        {
            SubscriptionId = apiResponse.SubscriptionId,
            Status = apiResponse.Status,
            Amount = apiResponse.Amount,
            StartDate = apiResponse.StartDate,
            EndDate = apiResponse.EndDate,
            Tier = apiResponse.Tier,
            DueAmount = apiResponse.DueAmount,
            OptOut = apiResponse.OptOut,
            PaymentDue = apiResponse.PaymentDue,
            SubscriptionBundles = apiResponse.SubscriptionBundles,
        };
        return response;
    }
}
