﻿namespace Nbg.Ewallet.Repository.dbQueries;

internal static class AuthRequestQueries
{
    internal const string InsertAuthorizationRequest =
        "INSERT INTO [EWallet].[dbo].[AuthorizationRequests] (Id,TargetWalletId,RequestorWalletId,Status,ExpiresAt,CreatedAt) " +
        "VALUES (@id,@targetWalletId,@RequestorWalletId,@status,@expiresAt,@createdAt)";

    internal const string GetAuthRequestByWallets =
        "SELECT Id, RequestorWalletId, TargetWalletId, CreatedAt, ExpiresAt, UpdatedAt, Status " +
        "FROM [EWallet].[dbo].[AuthorizationRequests] " +
        "WHERE RequestorWalletId = @RequestorWalletId " +
        "OR TargetWalletId = @targetWalletId " +
        "ORDER BY CreatedAt asc";

    internal const string GetAuthorizationRequestsByWalletIdAndStatus =
        "SELECT Id, RequestorWalletId, TargetWalletId, CreatedAt, ExpiresAt, UpdatedAt, Status " +
        "FROM [EWallet].[dbo].[AuthorizationRequests] " +
        "WHERE (TargetWalletId = @walletId OR RequestorWalletId = @walletId) " +
        "AND Status = @status " +
        "ORDER BY CreatedAt asc";

    internal const string GetAuthorizationRequestsByWalletId =
        "SELECT Id, RequestorWalletId, TargetWalletId, CreatedAt, ExpiresAt, UpdatedAt, Status " +
        "FROM [EWallet].[dbo].[AuthorizationRequests] " +
        "WHERE TargetWalletId = @walletId " +
        "OR RequestorWalletId = @walletId " +
        "ORDER BY CreatedAt asc";

    internal const string GetWalletAuthorizationRequestByRequestId =
        "SELECT Id, RequestorWalletId, TargetWalletId, CreatedAt, ExpiresAt, UpdatedAt, Status " +
        "FROM [EWallet].[dbo].[AuthorizationRequests] " +
        "WHERE Id = @requestId AND TargetWalletId = @walletId";

    internal const string GetAuthorizationRequests =
        "SELECT Id, RequestorWalletId, TargetWalletId, CreatedAt, ExpiresAt, UpdatedAt, Status " +
        "FROM [EWallet].[dbo].[AuthorizationRequests] " +
        "WHERE (RequestorWalletId = @RequestorWalletId OR TargetWalletId = @targetWalletId ) " +
        "AND Status = @status " +
        "ORDER BY CreatedAt asc";

    internal const string GetAuthorizationRequestsByStatus =
        "SELECT Id, RequestorWalletId, TargetWalletId, CreatedAt, ExpiresAt, UpdatedAt, Status " +
        "FROM [EWallet].[dbo].[AuthorizationRequests] " +
        "WHERE (RequestorWalletId = @RequestorWalletId OR TargetWalletId = @targetWalletId) " +
        "AND Status = @status " +
        "ORDER BY CreatedAt asc";


    internal const string UpdateAuthorizationRequest =
        "UPDATE [EWallet].[dbo].[AuthorizationRequests] " +
        "SET Status = @Status, UpdatedAt = @UpdatedAt, ExpiresAt = @ExpiresAt " +
        "WHERE Id = @Id";

    internal const string GetRequestAuthorizationsByRequestorWalletIdAndTargetWalletIdAndRequestStatus =
        "SELECT Id, RequestorWalletId, TargetWalletId, CreatedAt, ExpiresAt, UpdatedAt, Status " +
        "FROM [EWallet].[dbo].[AuthorizationRequests] " +
        "WHERE RequestorWalletId = @RequestorWalletId " +
        "AND TargetWalletId = @TargetWalletId " +
        "AND Status = @Status";
}
