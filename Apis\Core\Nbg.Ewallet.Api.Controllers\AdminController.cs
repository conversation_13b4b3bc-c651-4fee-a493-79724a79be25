﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Admin;

namespace Nbg.Ewallet.Api.Controllers
{
    /// <summary>
    /// This controller is not part of the public api.
    /// It is used by internal calls to register a new network.
    /// Thus there is no reason to be exported with swagger.
    /// </summary>
    [Produces("application/json")]
    [Consumes("application/json")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public class AdminController : BaseController
    {
        private readonly IAdminService _adminService;
        public AdminController(IAdminService adminService, ILogger<AdminController> logger) : base(logger)
        {
            _adminService = adminService;
        }

        [HttpPost]
        [Route("configureB2BRules", Name = "ConfigureB2BRules")]
        public async Task<ActionResult<ConfigureB2bRulesResponse>> ConfigureB2BRules(ConfigureB2bRulesRequest request)
        {
            //return await HandleResult(this, _adminService, service => service.ConfigureB2BRules(request));
            return await _adminService.ConfigureB2BRules(request);
        }

        [HttpPost]
        [Route("configureB2bChannels", Name = "ConfigureB2BChannels")]
        public async Task<ActionResult<ConfigureB2ChannelsRequest>> ConfigureB2BChannels(ConfigureB2ChannelsRequest request)
        {
            //return await HandleResult(this, _adminService, service => service.ConfigureB2Channels(request));
            return await _adminService.ConfigureB2Channels(request);
        }

        [HttpPost]
        [Route("uploadTerms", Name = "uploadTerms")]
        public async Task<ActionResult<CommonSuccessResult<object>>> UploadTerms(Terms request)
        {
            //return await HandleResult(this, _adminService, service => service.UploadTerms(request));
            return await _adminService.UploadTerms(request);
        }
    }
}
