﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nbg.ewallet.repository.dbQueries;
using Nbg.Ewallet.Repository;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace nbg.ewallet.repository;

public class WalletRepositoryService : BaseRepositoryService, IWalletRepositoryService
{
    public WalletRepositoryService(
        IOptions<RepositorySettings> repositoryOptions,
        ILogger<WalletRepositoryService> logger,
        IUnitOfWork unitOfWork) : base(unitOfWork, repositoryOptions, logger) { }

    public async Task SaveAsync(RepoWallet wallet)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(WalletQueries.InsertWallet, wallet, tx);
        }, "A database error occurred while inserting a wallet.");
    }

    public async Task<RepoWallet> FindOneByIdAsync(string walletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var wallet = await conn.QueryFirstOrDefaultAsync<RepoWallet>(
                WalletQueries.GetWalletById,
                new { Id = walletId },
                tx);
            return wallet ?? throw new WalletNotFoundException();
        }, "A database error occurred while retrieving the wallet.");
    }

    public async Task<bool> ExistsByOwnerUserIdAsync(string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var wallet = await conn.QueryFirstOrDefaultAsync<RepoWallet>(
                WalletQueries.GetWalletByOwnerUserId,
                new { OwnerUserId = userId },
                tx);
            return wallet != null;
        }, "A database error occurred while checking if the wallet exists by user ID.");
    }

    public async Task<RepoWallet> FindOneByOwnerUserIdAsync(string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var wallet = await conn.QueryFirstOrDefaultAsync<RepoWallet>(
                WalletQueries.GetWalletByOwnerUserId,
                new { OwnerUserId = userId },
                tx);
            return wallet ?? throw new WalletNotFoundException();
        }, "A database error occurred while retrieving the wallet by owner user ID.");
    }

    public async Task UpdateWalletAccountByWalletIdAsync(string walletId, string walletAccount)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(
                WalletQueries.UpdateWalletAccount,
                new { walletaccount = walletAccount, walletCreationTime = DateTime.UtcNow, walletid = walletId },
                tx);
        }, "A database error occurred while updating the wallet account.");
    }

    public async Task UpdateWalletSubscriptionByWalletIdAsync(Guid walletid, Guid subscriptionId)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(
                WalletQueries.UpdateWalletSubscription,
                new { subscriptionId , walletid },
                tx);
        }, "A database error occurred while updating the wallet account.");
    }

    public async Task<List<RepoWallet>> FindAllByParamsAsync(
        string vatNumber,
        string walletAccount,
        string walletName,
        string organizationName)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var queryBuilder = new StringBuilder(WalletQueries.SearchsWallet);
            if (!string.IsNullOrWhiteSpace(vatNumber)) queryBuilder.Append(WalletQueries.WithVatNumber);
            if (!string.IsNullOrWhiteSpace(walletAccount)) queryBuilder.Append(WalletQueries.WithWalletAccount);
            if (!string.IsNullOrWhiteSpace(walletName)) queryBuilder.Append(WalletQueries.WithWalletName);
            if (!string.IsNullOrWhiteSpace(organizationName)) queryBuilder.Append(WalletQueries.WithOrganizationName);

            var query = queryBuilder.ToString();
            var whereObject = new { Account = walletAccount, WalletName = walletName, VatNumber = vatNumber, OrganizationName = organizationName };
            var wallets = await conn.QueryAsync<RepoWallet>(query, whereObject, tx);
            return wallets.ToList();
        }, "A database error occurred while searching for wallets.");
    }

    public async Task<RepoWallet?> FindOneByWalletAccountAsync(string account)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            return await conn.QueryFirstOrDefaultAsync<RepoWallet>(WalletQueries.GetWalletByAccount, new { WalletAccount = account }, tx);
        }, "A database error occurred while retrieving the wallet by account.");
    }

    public async Task<RepoWallet> UpdateWalletNameByWalletIdAsync(Guid walletId, string walletName)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(WalletQueries.EditWalletName, new { WalletName = walletName, WalletId = walletId }, tx);
            return await conn.QuerySingleAsync<RepoWallet>(WalletQueries.GetWalletById, new { Id = walletId }, tx);
        }, "A database error occurred while editing the wallet name.");
    }
}
