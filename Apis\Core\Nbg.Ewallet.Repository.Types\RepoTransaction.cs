﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types;
public partial class RepoTransaction
{

    /// <summary>
    /// Unique entry’s identifier to the table
    /// </summary>
    public Guid TransactionId { get; set; }

    /// <summary>
    /// Exact timestamp of the entry’s insertion to the database
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Exact timestamp of the entry’s update to the database
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Sepa Instant execution indicator flag
    /// </summary>
    public bool? IsInstant { get; set; }

    /// <summary>
    /// Amount
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Transaction Type
    /// </summary>
    public DapperableEnum<TransactionType> TransactionType { get; set; }

    /// <summary>
    /// Transaction Sub Type
    /// </summary>
    public DapperableEnum<TransactionSubType> TransactionSubType { get; set; }

    /// <summary>
    /// Currency, only EUR initially
    /// </summary>
    public string Currency { get; set; }

    /// <summary>
    /// Debtor Iban
    /// </summary>
    public string DebtorIban { get; set; }

    /// <summary>
    /// Debtor Name
    /// </summary>
    public string DebtorName { get; set; }

    /// <summary>
    /// Reference number (to match Statement reference number)
    /// </summary>
    public string Reference { get; set; }

    /// <summary>
    /// Debtor Iban
    /// </summary>
    public string CreditorIban { get; set; }

    /// <summary>
    /// Creditor Name
    /// </summary>
    public string CreditorName { get; set; }

    /// <summary>
    /// Exact timestamp of the transaction
    /// </summary>
    public DateTime? TransactionDate { get; set; }

    /// <summary>
    /// Exact timestamp of the transaction
    /// </summary>
    public DateTime? Valeur { get; set; }

    /// <summary>
    /// Transaction Reason
    /// </summary>
    public string Reason { get; set; }

    /// <summary>
    /// Pending Approval, Sent
    /// </summary>
    public DapperableEnum<TransactionStatus> Status { get; set; }

    /// <summary>
    /// Success, Failed
    /// </summary>
    public string Result { get; set; }

    /// <summary>
    /// Message from CICS
    /// </summary>
    public string ResultReason { get; set; }

    /// <summary>
    /// Batch ID when transaction is part of a batch
    /// </summary>
    public Guid? BatchId { get; set; }

    /// <summary>
    /// Transfer wallet Id
    /// </summary>
    public Guid WalletId { get; set; }

    /// <summary>
    /// UserId of the user that submitted  or executed the transaction
    /// </summary>
    public string SubmittedBy { get; set; }

    /// <summary>
    /// UserId of the user that approved the transaction
    /// </summary>
    public string ApprovedBy { get; set; }

    /// <summary>
    /// UserId of the user that executed or approved the transaction
    /// </summary>
    public string ExecutedBy { get; set; }

    /// <summary>
    /// UserId of the user that rejected the transaction
    /// </summary>
    public string RejectedBy { get; set; }

    /// <summary>
    /// UserId of the user that executed transaction.
    /// Internal User: Current UserId
    /// External User: UserPermission.AssignedBy
    /// </summary>
    public string ExecutedAs { get; set; }

    /// <summary>
    /// TransfersRequest or PaymentsRequest based on TransactionType.
    /// </summary>
    public string RequestJson { get; set; }

    /// <summary>
    /// Transaction's Commission
    /// </summary>
    [DataMember(Name = "commission")]
    public decimal? Commission { get; set; }
}
