﻿using System;

namespace Nbg.Ewallet.Repository.Types;

public class RepoAuthorizationRequest
{
    public Guid Id { get; set; }

    public Guid RequestorWalletId { get; set; }
    public Guid TargetWalletId { get; set; }

    public DapperableEnum<RequestStatus> Status { get; set; }

    public DateTime ExpiresAt { get; set; }

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
