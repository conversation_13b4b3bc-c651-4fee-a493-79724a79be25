﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.HttpExceptions;

namespace Nbg.Ewallet.Api.Implementation;

public class WalletPermissionsService : IWalletPermissionsService
{
    private readonly ILogger<WalletPermissionsService> _logger;
    private readonly IMapper _mapper;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly IWalletPermissionsRepositoryService _walletPermissionsRepositoryService;
    private readonly ILimitRepositoryService _limitRepositoryService;
    private readonly IAuthorizationRepositoryService _authorizationRepositoryService;
    private readonly IUnitOfWork _unitOfWork;

    public WalletPermissionsService(
        ILogger<WalletPermissionsService> logger,
        IMapper mapper,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletRepositoryService walletRepositoryService,
        IWalletPermissionsRepositoryService walletPermissionsRepositoryService,
        ILimitRepositoryService limitRepositoryService,
        IAuthorizationRepositoryService authorizationRepositoryService,
        IUnitOfWork unitOfWork
    )
    {
        _logger = logger;
        _mapper = mapper;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletRepositoryService = walletRepositoryService;
        _walletPermissionsRepositoryService = walletPermissionsRepositoryService;
        _limitRepositoryService = limitRepositoryService;
        _authorizationRepositoryService = authorizationRepositoryService;
        _unitOfWork = unitOfWork;
    }

    public async Task<List<WalletPermission>> GetWalletPermissionsForOtherWallets(Guid walletId, bool showAll)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        //var customerCode = _httpContextRepositoryService.GetCustomerCode();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        if (myWallet.WalletId != walletId) throw new InsufficientUserRightsException();
        var permissions = await _walletPermissionsRepositoryService.FindAllByExpiredAndWalletIdAndExternalWalletId(walletId, Guid.Empty);

        if (!showAll)
        {
            permissions = await _walletPermissionsRepositoryService.FindAllActiveByWalletIdAndExternalWalletIdAsync(walletId, Guid.Empty);
        }

        var walletPermissions = _mapper.Map<List<WalletPermission>>(permissions);
        foreach (var permission in walletPermissions)
        {
            //var repoPermLimits = await _walletPermissionsRepositoryService.GetLimitsForWalletIdAsync(walletId, externalwalletId);
            var repoPermLimits = await _limitRepositoryService.GetByPermissionIdAsync(permission.Id.ToString());
            permission.Limits = _mapper.Map<List<Limit>>(repoPermLimits);
        }

        return walletPermissions;
    }

    public async Task<WalletPermissionResponse> SetWalletPermissionsForOtherWallets(WalletPermissionsRequest request)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        // check if different permissions are applied to the same user
        // this should not be allowed
        var duplicateUserPermissions = request.Permissions.GroupBy(x => x.TargetWalletId).Where(y => y.Count() > 1).FirstOrDefault();
        if (duplicateUserPermissions != null)
        {
            throw new OnlyOnePermissionPerUserIsAllowedException();
        }

        var repoWalletPermissions = _mapper.Map<List<RepoWalletPermission>>(request.Permissions);
        var walletPermissionResponse = new WalletPermissionResponse();
        List<RepoLimit> repoLimits = [];
        try
        {
            foreach (var permission in repoWalletPermissions)
            {
                if (myWallet.WalletId == permission.TargetWalletId)
                {
                    throw new PermissionToOwnWalletException();
                }

                permission.WalletId = myWallet.WalletId;
                permission.AssignedBy = userId;
                permission.ExpirationDate = DateTime.UtcNow.AddDays(180).Date.AddDays(1).AddTicks(-1);
                permission.CreatedAt = DateTime.UtcNow;

                var authRequests = await _authorizationRepositoryService.RepoAuthorizationRequestFindAllByRequestorWalletIdAndTargetWalletIdAndStatusAsync(
                    permission.TargetWalletId,
                    myWallet.WalletId,
                    RequestStatus.Pending);

                _logger.LogInformation("Found auth requests : {AuthCount}", authRequests.Count);
                authRequests.ForEach(async ar =>
                {
                    ar.Status = RequestStatus.Completed;
                    ar.UpdatedAt = DateTime.UtcNow;
                    await _authorizationRepositoryService.RepoAuthorizationRequestUpdateAuthorizationRequestAsync(ar);
                });

                permission.CreatedAt = DateTime.UtcNow;
            }

            foreach (var permission in request.Permissions)
            {
                //var permissionWalletId = permission.TargetWalletId;
                var limitsforuser = permission.Limits;
                if (limitsforuser == null) continue;

                var duplicateLimits = limitsforuser.GroupBy(x => x.TransactionType).Where(y => y.Count() > 1).FirstOrDefault();
                if (duplicateLimits != null)
                {
                    throw new OnlyOneTransactionTypeInLimitsIsAllowed();
                }

                foreach (var limit in limitsforuser)
                {
                    repoLimits.Add(new RepoLimit
                    {
                        Amount = limit.Amount,
                        TimeFrame = limit.Timeframe,
                        TransactionType = limit.TransactionType,
                        Id = limit.Id,
                        PermissionId = permission.Id
                    });
                }
            }

            await _walletPermissionsRepositoryService.ExpireExistingPermissionsyWalletIdAsync(myWallet.WalletId, repoWalletPermissions);
            await _walletPermissionsRepositoryService.SaveAllAsync(repoWalletPermissions);
            await _limitRepositoryService.InsertUserLimitsAsync(repoLimits);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while setting wallet permissions for other wallets, Rollback");
            // Rollback any changes made during the transaction
            await _unitOfWork.RollbackPartialAsync();
            throw;
        }

        var permissions = await _walletPermissionsRepositoryService.FindAllActiveByWalletIdAndExternalWalletIdAsync(myWallet.WalletId, Guid.Empty);
        walletPermissionResponse.Permissions = _mapper.Map<List<WalletPermission>>(permissions);

        foreach (var p in walletPermissionResponse.Permissions)
        {
            var walletLimits = repoLimits.Where(x => x.PermissionId == p.Id).ToList();
            p.Limits = _mapper.Map<List<Limit>>(walletLimits);
        }

        //TODO: return only user permissions sent in the request (e.g, 3 user permissions sent, respond with 3 permissions)

        var walletList = request.Permissions.Select(x => x.TargetWalletId);

        walletPermissionResponse.Permissions = walletPermissionResponse.Permissions.Where(wp => walletList.Contains(wp.TargetWalletId)).ToList();

        return walletPermissionResponse;
    }

    public async Task<List<WalletPermission>> GetWalletPermissionsForExternalWallet(Guid walletId, Guid externalwalletId, bool showAll)
    {
        // var userId = _httpContextRepositoryService.GetUserId();
        // var customerCode = _httpContextRepositoryService.GetCustomerCode();
        // var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        var permissions = await _walletPermissionsRepositoryService.FindAllByExpiredAndWalletIdAndExternalWalletId(walletId, externalwalletId);
        if (!showAll)
        {
            permissions = await _walletPermissionsRepositoryService.FindAllActiveByWalletIdAndExternalWalletIdAsync(walletId, externalwalletId);
        }

        var walletPermissions = _mapper.Map<List<WalletPermission>>(permissions);

        foreach (var permission in walletPermissions)
        {
            var repoPermLimits = await _limitRepositoryService.GetByPermissionIdAsync(permission.Id.ToString());
            permission.Limits = _mapper.Map<List<Limit>>(repoPermLimits);
        }

        return walletPermissions;
    }

    public async Task<WalletPermission> RevokeExternalWalletPermissions(Guid walletId, Guid targetWalletId)
    {
        var myUserId = _httpContextRepositoryService.GetUserId();
        var walletPermissions = await _walletPermissionsRepositoryService.FindAllActiveByWalletIdAndTargetWalletIdAsync(walletId, targetWalletId);
        if (walletPermissions == null || walletPermissions.Count == 0) throw new BadRequestException("Wallet has no permissions");

        foreach (RepoWalletPermission walletPermission in walletPermissions)
        {
            if (walletPermission.ExpirationDate < DateTime.UtcNow) continue;
            walletPermission.ExpirationDate = DateTime.UtcNow;
            walletPermission.RevokedBy = myUserId;
            await _walletPermissionsRepositoryService.SaveAsync(walletPermission);

            var permission = _mapper.Map<WalletPermission>(walletPermission);

            var repoPermLimits = await _limitRepositoryService.GetByPermissionIdAsync(permission.Id.ToString());
            permission.Limits = _mapper.Map<List<Limit>>(repoPermLimits);
            return permission;
        }

        _logger.LogWarning("No active permissions found for walletId: {WalletId} and targetWalletId: {TargetWalletId}", walletId, targetWalletId);
        throw new BadRequestException("User has no permissions");
    }
}
