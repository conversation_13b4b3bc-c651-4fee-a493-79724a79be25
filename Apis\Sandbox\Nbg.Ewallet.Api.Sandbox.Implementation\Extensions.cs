﻿using System.Threading.Tasks;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation;

public static class Extensions
{
    public static async Task<EwalletDataModel> CheckModel(this Task<EwalletDataModel> eModel)
    {
        var model = await eModel;
        model.EwalletSandbox.Statements ??= [];
        model.EwalletSandbox.AvailableUsers ??= [];
        model.EwalletSandbox.UserPermissions ??= [];
        model.EwalletSandbox.WalletsSubscriptions ??= [];
        model.EwalletSandbox.WalletsPermissions ??= [];
        model.EwalletSandbox.Wallets ??= [];
        model.EwalletSandbox.AvailableAccounts ??= [];
        model.EwalletSandbox.Transactions ??= [];
        return model;
    }

    public static SandboxTransaction MapToSandboxTransaction(this RepoTransaction repoTransaction)
    {
        var transaction = new SandboxTransaction
        {
            TransactionId = repoTransaction.TransactionId,
            WalletId = repoTransaction.WalletId,
            Amount = repoTransaction.Amount,
            DebitIban = repoTransaction.DebtorIban,
            DebitName = repoTransaction.DebtorName,
            CreditIban = repoTransaction.CreditorIban,
            CreditName = repoTransaction.CreditorName,
            TransactionDate = repoTransaction.TransactionDate,
            Timestamp = repoTransaction.CreatedAt,
            Valeur = repoTransaction.Valeur,
            Currency = repoTransaction.Currency,
            Reference = repoTransaction.Reference,
            ReasonInfo = repoTransaction.Reason,
            Description = repoTransaction.Reason,
            BatchId = repoTransaction.BatchId,
            //BatchDescription = repoTransaction.BatchDescription,
            SubmittedBy = repoTransaction.SubmittedBy,
            Result = repoTransaction.Result,
            ResultReason = repoTransaction.ResultReason,
            TransactionSubType = repoTransaction.TransactionSubType,
            Status = repoTransaction.Status,
            TransactionType = repoTransaction.TransactionType,
            Instant = repoTransaction.IsInstant,
            ApprovedBy = repoTransaction.ApprovedBy,
            ExecutedBy = repoTransaction.ExecutedBy,
            RejectedBy = repoTransaction.RejectedBy,
            RequestJson = repoTransaction.RequestJson,
            Commission = repoTransaction.Commission,
            RelatedName = repoTransaction.CreditorName,
            RelatedAccount = repoTransaction.CreditorIban,
        };
        return transaction;
    }

    public static RepoTransaction MapToRepoTransaction(this SandboxTransaction transaction)
    {
        var result = new RepoTransaction
        {
            TransactionId = transaction.TransactionId,
            WalletId = transaction.WalletId,
            Amount = transaction.Amount,
            DebtorIban = transaction.DebitIban,
            DebtorName = transaction.DebitName,
            CreditorIban = transaction.CreditIban,
            CreditorName = transaction.CreditName,
            TransactionDate = transaction.TransactionDate,
            CreatedAt = transaction.Timestamp,
            TransactionType = transaction.TransactionType.GetValueOrDefault(),
            TransactionSubType = transaction.TransactionSubType.GetValueOrDefault(),
            Currency = transaction.Currency,
            Reference = transaction.Reference,
            Reason = transaction.ReasonInfo,
            Status = transaction.Status.GetValueOrDefault(),
            Result = transaction.Result,
            ResultReason = transaction.ResultReason,
            BatchId = transaction.BatchId,
            SubmittedBy = transaction.SubmittedBy,
            ApprovedBy = transaction.ApprovedBy,
            ExecutedBy = transaction.ExecutedBy,
            RejectedBy = transaction.RejectedBy,
            IsInstant = transaction.Instant,
            Valeur = transaction.Valeur,
            RequestJson = transaction.RequestJson,
            Commission = transaction.Commission
        };
        return result;
    }

}
