﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Consent information request
/// </summary>
[DataContract]
public class RetrieveConsentRequest
{
    /// <summary>
    /// User id
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserID { get; set; }

    /// <summary>
    /// Consent id
    /// </summary>
    [DataMember(Name = "consentId")]
    public string ConsentId { get; set; }
}
