﻿<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>true</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Description>
        </Description>
        <Authors>NBG</Authors>
        <RepositoryUrl>https://github.com/NBGpartners/Nbg.AspNetCore.Common.git</RepositoryUrl>
        <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
        <UserSecretsId>7dda4245-8429-4674-8351-3759f38c3b00</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <_WebToolingArtifacts Remove="Properties\PublishProfiles\f5plex.pubxml" />
        <_WebToolingArtifacts Remove="Properties\PublishProfiles\QA.pubxml" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector" Version="1.0.53" />
        <PackageReference Include="Nbg.NetCore.Data" Version="8.0.3" />
        <PackageReference Include="Nbg.NetCore.Healthchecks" Version="8.0.3" />
        <PackageReference Include="Nbg.NetCore.Services.Cics.Http" Version="9.0.4" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
        <PackageReference Include="NLog.WindowsEventLog" Version="5.3.4" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Api.Controllers\Nbg.Ewallet.Api.Controllers.csproj" />
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Api.Interfaces\Nbg.Ewallet.Api.Interfaces.csproj" />
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Repository\Nbg.Ewallet.Repository.csproj" />
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Api.Implementation\Nbg.Ewallet.Api.Implementation.csproj" />
    </ItemGroup>

</Project>