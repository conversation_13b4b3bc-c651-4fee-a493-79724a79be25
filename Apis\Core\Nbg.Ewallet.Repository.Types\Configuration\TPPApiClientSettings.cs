﻿using System;

namespace Nbg.Ewallet.Repository.Types.Configuration;

public class TPPApiClientSettings : ClientSettingsBase
{
    public string Pay { get; set; }
    public string Commission { get; set; }
    public Origin Origin { get; set; }
}

public class Origin
{
    public Guid NetworkId { get; set; }
    public string TransactionTerminalMachineId { get; set; }
    public Guid TransactionTerminalId { get; set; }
    public Guid TransactionTerminalSpotId { get; set; }
    public string UserId { get; set; }
}
