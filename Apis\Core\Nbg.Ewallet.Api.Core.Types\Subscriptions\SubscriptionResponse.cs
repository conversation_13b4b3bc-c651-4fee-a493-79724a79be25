﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;

namespace Nbg.Ewallet.Api.Types.Subscriptions;

/// <summary>
/// CreateSubscriptionResponse definition
/// </summary>
public class SubscriptionResponse
{
    /// <summary>
    /// Subscription Id
    /// </summary>
    [DataMember(Name = "subscriptionId")]
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// Start Date
    /// </summary>
    [DataMember(Name = "startDate")]
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Expiration date of the subscription.
    /// </summary>
    [DataMember(Name = "endDate")]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Monetary amount paid by the End User to the partner for subscribing to the wallet service.
    /// This is only for consolidation reasons and it is not used in any other way.
    /// </summary>
    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    /// <summary>
    /// Due Payment date of the subscription
    /// </summary>
    [DataMember(Name = "paymentDue")]
    public DateTime PaymentDue { get; set; }

    /// <summary>
    /// Due Amount to be paid by the End User to the partner for subscribing to the wallet service.
    /// </summary>
    [DataMember(Name = "dueAmount")]
    public decimal? DueAmount { get; set; }

    /// <summary>
    /// The transasction bundle with applied discounts for the transaction types included in the list.
    /// It includes pairs of transaction type and value, indicating the number of discounted transactions per transaction type.
    /// </summary>
    [DataMember(Name = "SubscriptionBundles")]
    public List<SubscriptionBundle> SubscriptionBundles { get; set; }

    /// <summary>
    /// Id of the discount applied
    /// </summary>
    [DataMember(Name = "planId")]
    public SubscriptionTier Tier { get; set; }

    /// <summary>
    /// Opt Out
    /// </summary>
    [DataMember(Name = "optOut")]
    public bool OptOut { get; set; }

    /// <summary>
    /// Subscription Status
    /// </summary>
    [DataMember(Name = "status")]
    public SubscriptionStatus Status { get; set; }
}

