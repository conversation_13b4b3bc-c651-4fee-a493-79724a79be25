﻿using System;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Repository.CoreApis;

public class CoreGenericException : Exception
{
    private readonly ErrorSeverity _errorSeverity;

    private readonly ErrorCategory _errorCategory;

    private readonly int _statusCode;

    public string Code { get; set; }

    public CoreGenericException() : base() { }
    public CoreGenericException(string msg) : base(msg) { }
    public CoreGenericException(string code, string msg) : base(msg)
    {
        Code = code;
    }
    public CoreGenericException(string code, string msg, ErrorSeverity errorSeverity, ErrorCategory errorCategory) : base(msg)
    {
        _errorSeverity = errorSeverity;
        _errorCategory = errorCategory;
        _statusCode = 200;
        Code = code;
    }
    public CoreGenericException(string code, string msg, Exception innerException) : base(msg, innerException)
    {
        Code = code;
    }

    public static readonly CoreGenericException GenericError = new CoreGenericException("108", "Error server_error: Unknown Error", ErrorSeverity.Error, ErrorCategory.Technical);

}
