﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.CorporateApi;
[DataContract]
public class UpdateProductsResponse
{
    /// <summary>Success indicator</summary>
    [DataMember(Name = "success")]
    public bool Success { get; set; }

    /// <summary>The new version of the user in database</summary>
    [DataMember(Name = "version")]
    public int Version { get; set; }
}
