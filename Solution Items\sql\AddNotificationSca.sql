INSERT INTO [Notifications].[dbo].[NotificationTextRule] (
    [ID],
    [Timestamp],
    [SourceSystem],
    [NotificationType],
    [NotificationSubType],
    [NotificationChannel],
    [Language],
    [NotificationTextTemplate],
    [NotificationPart],
    [TargetApplication]
)
VALUES (
    'FF501F09-7591-459A-8388-2285FB8347C6',
    GETDATE(),
    NULL,
    '3E1CE6D9-B61A-4F91-BD0E-22454D689544',
    'F31D45CC-734C-4814-9220-8BCF5D831FA2',
    'F77BB17A-3040-48F2-992C-46037F029704',
    'el',
    N'I-CODE {{Token}} ΓΙΑ ΕΠΙΒΕΒΑΙΩΣΗ ΑΠΟΔΟΣΗΣ ΔΙΚΑΙΩΜΑΤΩΝ ΣΕ WALLET {{WalletName}}. ΑΡ. ΕΠΙΒΕΒΑΙΩΣΗΣ {{TokenCheck}}',
    NULL,
    NULL
);

INSERT INTO [Notifications].[dbo].[NotificationSubType] (
    [ID],
    [NotificationType],
    [Name]
)
VALUES (
    'F31D45CC-734C-4814-9220-8BCF5D831FA2',
    '3E1CE6D9-B61A-4F91-BD0E-22454D689544',
    'SCA - EWallet'
);