﻿using System;
using System.Collections.Generic;
using System.Linq;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static List<SandboxLimit> GenerateLimits(List<SandboxWalletPermission> permissions, List<SandboxUserPermission> userPermissions)
    {
        var permissionIds = permissions.Select(p => p.Id).ToList();
        permissionIds.AddRange(userPermissions.Select(p => p.Id));
        return permissionIds.SelectMany(pId => new List<SandboxLimit>()
        {
            new SandboxLimit
            {
                Id = Guid.NewGuid(),
                PermissionId = pId,
                Amount = 10000.0m,
                Timeframe = TimeFrameType.MONTHLY,
                TransactionType = LimitTransactionType.WalletToWallet
            },
            new SandboxLimit
            {
                Id = Guid.NewGuid(),
                PermissionId = pId,
                Amount = 15000.0m,
                Timeframe = TimeFrameType.MONTHLY,
                TransactionType = LimitTransactionType.WalletToNBG
            },
            new SandboxLimit
            {
                Id = Guid.NewGuid(),
                PermissionId = pId,
                Amount = 5000.0m,
                Timeframe = TimeFrameType.MONTHLY,
                TransactionType = LimitTransactionType.WalletToIBAN
            }
        }).ToList();
    }
}
