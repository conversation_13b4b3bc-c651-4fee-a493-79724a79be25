sequenceDiagram
	autonumber
	Client->>EWallet.Proxy: /wallet/load POST
	EWallet.Proxy->>EWallet.Core: /wallet/load POST
	EWallet.Core->>Accounts.Core: /transfers/genericTransferV4 POST
	Accounts.Core->>EWallet.Core: {availableBalance}
	EWallet.Core->>EWallet.DB: INSERT
	note over EWallet.DB: DB Table:  WalletTransactions
	EWallet.DB->>EWallet.Core: {success, balance}
	EWallet.Core->>EWallet.Proxy: {success, balance}
	EWallet.Proxy->>Client: {success, balance}