﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface IWalletRepositoryService
{
    Task SaveAsync(RepoWallet wallet);

    Task<RepoWallet> FindOneByIdAsync(string walletId);
    Task<RepoWallet> UpdateWalletNameByWalletIdAsync(Guid walletId, string walletName);

    Task<bool> ExistsByOwnerUserIdAsync(string walletId);

    Task<RepoWallet?> FindOneByWalletAccountAsync(string account);

    Task<RepoWallet> FindOneByOwnerUserIdAsync(string userId);
    Task UpdateWalletAccountByWalletIdAsync(string walletid, string walletAccount);

    Task UpdateWalletSubscriptionByWalletIdAsync(Guid walletid, Guid subscriptionId);

    Task<List<RepoWallet>> FindAllByParamsAsync(string vatNumber, string walletAccount, string walletName, string organizationName);
}
