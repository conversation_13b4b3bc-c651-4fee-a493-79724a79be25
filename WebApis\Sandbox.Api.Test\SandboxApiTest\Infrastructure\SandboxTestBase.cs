using System.Text.Json;
using Xunit;

namespace SandboxTest.Infrastructure
{
    public abstract class SandboxTestBase : IAsyncLifetime
    {
        protected readonly SandboxApiFactory Factory;
        protected readonly HttpClient Client;
        protected readonly JsonSerializerOptions JsonOptions;

        protected SandboxTestBase()
        {
            Factory = new SandboxApiFactory();
            Client = Factory.CreateClient();
            Client.DefaultRequestHeaders.Add("sandboxId", Factory.SandboxId);

            JsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public virtual async Task InitializeAsync()
        {
            await Factory.InitializeSandboxAsync();
        }

        public virtual Task DisposeAsync()
        {
            Client.Dispose();
            Factory.Dispose();
            return Task.CompletedTask;
        }

        protected async Task<T?> DeserializeResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<T>(content, JsonOptions);
        }
    }
}
