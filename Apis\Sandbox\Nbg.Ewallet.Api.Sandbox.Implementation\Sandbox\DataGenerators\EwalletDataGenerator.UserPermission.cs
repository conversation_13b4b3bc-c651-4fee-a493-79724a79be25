﻿using System;
using System.Collections.Generic;
using System.Linq;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static List<SandboxUserPermission> GenerateUserPermissions(List<SandboxWallet> wallets)
    {
        return wallets.Select(x =>
             new SandboxUserPermission
             {
                 Id = Guid.NewGuid(),
                 Admin = true,
                 Approve = true,
                 BalanceView = true,
                 Submit = true,
                 TransactionView = true,
                 InheritsAuthorizations = true,
                 WalletId = x.WalletId,
                 UserId = x.OwnerUserId,
                 Limits = new List<SandboxLimit> {
                     new SandboxLimit {
                         Amount = 1000,
                         Id = Guid.NewGuid(),
                         Timeframe = TimeFrameType.DAILY,
                         TransactionType = LimitTransactionType.WalletToIBAN,
                         PermissionId = Guid.NewGuid()
                     }
                 },
                 CreationDate = DateTime.UtcNow,
                 ExpirationDate = DateTime.UtcNow.AddYears(1)
             }).ToList();
    }
}
