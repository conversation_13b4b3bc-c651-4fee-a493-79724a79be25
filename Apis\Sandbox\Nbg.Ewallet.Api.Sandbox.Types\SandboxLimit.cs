﻿using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// Represents a limit set on wallet transactions.
/// </summary>
public class SandboxLimit
{
    /// <summary>
    /// The unique identifier for the limit.
    /// </summary>
    [DataMember(Name = "id")]
    public Guid Id { get; set; }

    /// <summary>
    /// The identifier of the permission to which this limit is associated.
    /// </summary>
    [DataMember(Name = "permissionId")]
    public Guid PermissionId { get; set; }

    /// <summary>
    /// The type of transaction that the limit applies to.
    /// </summary>
    [DataMember(Name = "transactionType")]
    public LimitTransactionType TransactionType { get; set; }

    /// <summary>
    /// The maximum amount allowed for the transaction type within the specified timeframe.
    /// </summary>
    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    /// <summary>
    /// The period over which the limit applies.
    /// </summary>
    [DataMember(Name = "timeframe")]
    public TimeFrameType Timeframe { get; set; }
}
