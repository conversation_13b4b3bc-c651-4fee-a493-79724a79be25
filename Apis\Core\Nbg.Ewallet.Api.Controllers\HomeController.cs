﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;

namespace Nbg.Ewallet.Api.Controllers
{
    /// <summary>
    /// Just to get the profile and any other need to be routed to /home
    /// </summary>
    [Produces("application/json")]
    [Consumes("application/json")]
    [ApiExplorerSettings(GroupName = "User Profile")]
    public class HomeController : BaseController
    {
        private readonly IProfileService _profileService;
        /// <summary>
        /// This Controller Provides the Users Profile
        /// </summary>
        public HomeController(IProfileService profileService, ILogger<HomeController> logger) : base(logger)
        {
            _profileService = profileService;
        }

        /// <summary>
        /// Retrieves the current user profile.
        /// </summary>
        /// <remarks>
        /// The user profile is consisted of user specific information, user permissions on own wallet and access to other wallets.
        /// </remarks>
        [HttpGet]
        [Route("/profile", Name = "profile")]
        public async Task<ActionResult<ProfileResponse>> GetProfile()
        {
            return await _profileService.GetProfile();
        }
    }
}
