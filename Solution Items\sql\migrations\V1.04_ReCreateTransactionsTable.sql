DROP TABLE [EWallet].[dbo].[Transactions]

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transactions](
	TransactionId uniqueidentifier NOT NULL,
	CreatedAt datetime2(7) NOT NULL,
	UpdatedAt datetime2(7) NOT NULL,
	Amount decimal(10, 2) NOT NULL,
	TransferType nvarchar(25) NULL,
	TransactionType nvarchar(25) NULL,
	<PERSON><PERSON><PERSON>cy nvarchar(3) NOT NULL,
	DebtorIban nvarchar(50) NOT NULL,
	DebtorName nvarchar(50) NOT NULL,
	Reference nvarchar(50) NULL,
	CreditorIban nvarchar(50) NOT NULL,
	CreditorName nvarchar(50) NOT NULL,
	TransactionDate datetime2(7) NULL,
	Reason nvarchar(25) NOT NULL,
	[Status] nvarchar(25) NOT NULL,
	Result nvarchar(25) NULL,
	ResultReason nvarchar(100) NULL,
	BatchId uniqueidentifier NULL,
	WalletId uniqueidentifier NOT NULL,
	SubmittedBy nvarchar(25) NOT NULL,
	ApprovedBy nvarchar(25) NULL,
	ExecutedBy nvarchar(25) NULL,
 CONSTRAINT [PK_Transactions] PRIMARY KEY CLUSTERED 
(
	[TransactionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO