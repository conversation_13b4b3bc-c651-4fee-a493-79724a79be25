﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.AspNetCore.ProxyMiddleware;
using Nbg.AspNetCore.ProxyMiddleware.Plugins.Sca;

namespace nbg.ewallet.proxy.api;

public class ScaGeneratorProvider : IScaGeneratorProvider
{
    private static readonly Guid userRegistryId = Guid.Parse("43F9ED21-216E-4445-9912-4C783768A229");

    private static readonly Guid setExternalPermissionsNotificationId = Guid.Parse("F31D45CC-734C-4814-9220-8BCF5D831FA2");

    private static readonly List<string> _scaFreeHttpVerbs = ["GET", "DELETE"];

    public async Task<ScaRequest> GetScaRequestAsync(ProxyContext context)
    {
        var httpVerb = context.HttpContext.Request.Method;
        var scaRequest = new ScaRequest
        {
            IsScaFree = _scaFreeHttpVerbs.Contains(httpVerb),
            UserRegistryId = userRegistryId,
            NotificationSubTypeId = context.ScaContext.RouteOptions.NotificationSubTypeId,
            SenderName = "I-BANK",
            NotificationData = await GetNotificationPart(context),
            UserId = context.HttpContext.User.FindFirst(x => x.Type == "preferred_username").Value
        };

        return await Task.FromResult(scaRequest);
    }

    private async Task<ScaNotificationPart[]> GetNotificationPart(ProxyContext context)
    {
        //todo : check if the following line is needed
        //var request = await context.ReadRequestBodyAsAsync<JObject>();
        var notificationSubTypeId = context.ScaContext.RouteOptions.NotificationSubTypeId;

        if (notificationSubTypeId != setExternalPermissionsNotificationId)
        {
            return [];
        }

        return [
            new ScaNotificationPart{
                Key = "WalletName",
                Value = "Test"
            }
        ];
    }
}
