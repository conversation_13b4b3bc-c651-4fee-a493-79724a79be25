﻿using System.Collections.Generic;

namespace nbg.ewallet.repository.types;

public class CustomerData
{
    public bool? IsActive { get; set; }

    public bool? HasMissingInformation { get; set; }
    public bool? isFPbutNotBusiness { get; set; }
    public bool? isCorporate { get; set; }

    public string VatNumber { get; set; }

    public string Name { get; set; }

    public List<CustomerAccount> Accounts { get; set; }

    public List<string> ValidationControls { get; set; }
}
