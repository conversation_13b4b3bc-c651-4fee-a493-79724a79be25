<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Nbg.Ewallet.Api.Controllers</name>
    </assembly>
    <members>
        <member name="T:Nbg.Ewallet.Api.Controllers.AdminController">
            <summary>
            This controller is not part of the public api.
            It is used by internal calls to register a new network.
            Thus there is no reason to be exported with swagger.
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.B2BController">
            <summary>
            Retrieves if a specific vatNumber or company Name is registered in Wallet, or if a specific wallet account iban exists inside wallet
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.B2BController.Search(Nbg.Ewallet.Api.Types.B2BSearchRequest)">
            <summary>
            Search
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.ConsentsController.CreateConsent(Nbg.Ewallet.Api.Types.Wallet.CreateConsentRequest)">
             <summary>
             Create a new consent
             </summary>
             <remarks>Create a new customer access consent</remarks>
            
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.ConsentsController.GetConsent(System.Guid)">
            <summary>
            Retrieve an access consent
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.HomeController">
            <summary>
            Just to get the profile and any other need to be routed to /home
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.HomeController.#ctor(Nbg.Ewallet.Api.Interfaces.IProfileService,Microsoft.Extensions.Logging.ILogger{Nbg.Ewallet.Api.Controllers.HomeController})">
            <summary>
            This Controller Provides the Users Profile
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.HomeController.GetProfile">
            <summary>
            Retrieves the current user profile.
            </summary>
            <remarks>
            The user profile is consisted of user specific information, user permissions on own wallet and access to other wallets.
            </remarks>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.ManagementController">
            <summary>
            This controller is not part of the public api.
            It is used by NBG UI project to manage a consent.
            Thus there is no reason to be exported with swagger.
            UI actions are all POST and expect an IRequest object
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.ManagementController.GetAccounts(Nbg.NetCore.Common.Types.Request{Nbg.Ewallet.Api.Types.Wallet.AccountsRequest})">
            <summary>
            Retrieve accounts
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.ManagementController.GetConsent(Nbg.NetCore.Common.Types.Request{Nbg.Ewallet.Api.Types.Wallet.RetrieveConsentRequest})">
            <summary>
            Retrieve consent information
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.ManagementController.AuthorizeConsent(Nbg.NetCore.Common.Types.Request{Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest})">
            <summary>
            Authorize consent
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.ManagementController.RejectConsent(Nbg.NetCore.Common.Types.Request{Nbg.Ewallet.Api.Types.Wallet.UpdateConsentRequest})">
            <summary>
            Reject consent
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.ManagementController.GetProfile(Nbg.NetCore.Common.Types.Request{Nbg.NetCore.Common.Types.EmptyRequest})">
            <summary>
            Retrieves the current user profile.
            </summary>
            <remarks>
            The user profile is consisted of user specific information, user permissions on own wallet and access to other wallets.
            </remarks>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.PaymentsController">
            <summary>
            This Controller provides functionality for EWallet Payments
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.PaymentsController.Execute(Nbg.Ewallet.Api.Types.BatchPaymentsRequest,System.String)">
            <summary>
            Execute Massive Payment
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.PaymentsController.Pay(Nbg.Ewallet.Api.Types.ExtendedPaymentsRequest,System.String)">
            <summary>
            Bill Payment from a wallet to an organization
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.PaymentsController.ExecuteForecast(Nbg.Ewallet.Api.Types.BatchPaymentsRequest,System.String)">
            <summary>
            Forecasts a batch of payment transactions to validate if sufficient balance is available for each, without executing the actual payments.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.PaymentsController.PayForecast(Nbg.Ewallet.Api.Types.ExtendedPaymentsRequest,System.String)">
            <summary>
            Forecasts a single bill payment transaction to validate if sufficient balance is available, without executing the actual payment.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.PaymentsController.Commission(Nbg.Ewallet.Api.Types.ExtendedPaymentsRequest,System.Guid)">
            <summary>
            Bill Payment commissions
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.PaymentsController.BatchCommission(Nbg.Ewallet.Api.Types.BatchPaymentsCommissionRequest,System.Guid)">
            <summary>
            Execute Massive Payment
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.SubscriptionsController">
            <summary>
            This Controller provides functionality for EWallet Payments
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.SubscriptionsController.GetSubscriptionPlans">
            <summary>
            get subscription plans
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.TermsController">
            <summary>
            This Controller provides functionality for EWallet Payments
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.TermsController.GetTerms">
            <summary>
            get terms and conditions
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.TransfersController">
            <summary>
            Controller providing Transfers functionality
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.TransfersController.Transfer(Nbg.Ewallet.Api.Types.Transfers.TransfersRequest,System.String)">
            <summary>
            Transfer from a wallet to a wallet, or from a wallet to an NBG account
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.TransfersController.TransferForecast(Nbg.Ewallet.Api.Types.Transfers.TransfersRequest,System.String)">
            <summary>
            Forecasts a single transfer transaction to validate if sufficient balance is available, without executing the actual transfer.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.TransfersController.Batch(Nbg.Ewallet.Api.Types.Transfers.BatchTransfersRequest,System.String)">
            <summary>
            Transfer from a wallet to many, or from a wallet to many NBG accounts
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.TransfersController.BatchForecast(Nbg.Ewallet.Api.Types.Transfers.BatchTransfersRequest,System.String)">
            <summary>
            Forecasts a batch of transfer transactions to validate if sufficient balance is available for each, without executing the actual transfers.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.TransfersController.CalculateFees(Nbg.Ewallet.Repository.Types.AccountApi.TransfersRequestBase,System.Guid)">
            <summary>
            Calculates the fees applied to the transaction
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.TransfersController.BatchCalculateFees(Nbg.Ewallet.Api.Types.Transfers.BatchTransfersRequest,System.Guid)">
            <summary>
            Calculates the fees applied to the transaction
            </summary>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.WalletAuthorizationController">
            <summary>
            This Controller Provides Wallet Authorization functionality.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletAuthorizationController.AuthorizationRequest(System.Guid,Nbg.Ewallet.Api.Types.Wallet.WalletAuthorizationRequest)">
            <summary>
            Requests authorization for a wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="request">The request containing authorization details.</param>
            <returns>A response containing authorization request details.</returns>
            <response code="200">Authorization request created successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletAuthorizationController.GetAuthorizationRequests(System.Guid,System.Nullable{Nbg.Ewallet.Repository.Types.RequestStatus})">
            <summary>
            Retrieves authorization requests for a wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="status">The status of the authorization requests to filter by.</param>
            <returns>A list of authorization requests for the specified wallet.</returns>
            <response code="200">Authorization requests retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletAuthorizationController.RejectAuthorizationRequest(System.Guid,System.Guid)">
            <summary>
            Rejects an authorization request for a wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="authRequestId">The authorization request identifier.</param>
            <returns>The rejected authorization request.</returns>
            <response code="200">Authorization request rejected successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.WalletPermissionsController">
            <summary>
            This Controller Provides Wallet Permissions functionality.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletPermissionsController.SetWalletPermissionsForOtherWallets(Nbg.Ewallet.Api.Types.Wallet.WalletPermissionsRequest)">
            <summary>
            Grants access to other wallets.
            </summary>
            <param name="request">The request containing permissions details for other wallets.</param>
            <returns>A response indicating the result of the operation.</returns>
            <response code="200">Permissions granted successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletPermissionsController.GetWalletPermissionsForOtherWallets(System.Guid,System.Boolean)">
            <summary>
            Retrieves permissions for other wallets.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="showAll">Whether to show all permissions.</param>
            <returns>A list of permissions for other wallets.</returns>
            <response code="200">Permissions retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletPermissionsController.GetWalletPermissionsForOtherWallet(System.Guid,System.Guid,System.Boolean)">
            <summary>
            Retrieves permissions for a specific external wallet.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="externalWalletId">The external wallet identifier.</param>
            <param name="showAll">Whether to show all permissions.</param>
            <returns>A list of permissions for the specified external wallet.</returns>
            <response code="200">Permissions retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletPermissionsController.RevokeExternalWalletPermissions(System.Guid,System.Guid)">
            <summary>
            Revokes permissions for a specific external wallet.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="targetWalletId">The target wallet identifier whose permissions are to be revoked.</param>
            <returns>A list of permissions after revocation.</returns>
            <response code="200">Permissions revoked successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.WalletServiceController">
            <summary>
            This Controller Provides Wallet Services functionality.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.GetWalletInformation(System.Guid)">
            <summary>
            Retrieves information about a specific wallet.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <returns>The wallet information.</returns>
            <response code="200">Returns the wallet information.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="404">If the wallet is not found.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.GetWalletBalance(System.Guid)">
            <summary>
            Retrieves the current balance of a subscription plan.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <returns>The current balance of the subscription plan.</returns>
            <response code="200">Returns the current balance.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="404">If the wallet or balance is not found.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.EditWalletName(System.Guid,Nbg.Ewallet.Api.Types.Wallet.EditWalletNameRequest)">
            <summary>
            Retrieves the current balance of a subscription plan.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <returns>The current balance of the subscription plan.</returns>
            <response code="200">Returns the current balance.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="404">If the wallet or balance is not found.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.GetMyTransactions(System.Guid)">
            <summary>
            Retrieves transactions for a wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="request">The request containing transaction details.</param>
            <returns>A list of transactions for the specified wallet.</returns>
            <response code="200">Transactions retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.GetWalletStatementPdfExport(System.Guid,Nbg.Ewallet.Repository.Types.AccountApi.StatementRequestQueryParams)">
            <summary>
            Retrieves wallet account statements for the specified wallet and returns them as a PDF file.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="request">The request containing statement query parameters.</param>
            <returns>Statement in a PDF format.</returns>
            <response code="200">Authorization request rejected successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.GetUserAccounts(System.Guid)">
            <summary>
            Retrieves the list of available accounts associated with the specified wallet and user logged in.
            </summary>
            <remarks>This endpoint requires the wallet to be active and the caller to be authorized. The
            response may include various HTTP status codes: <list type="bullet"> <item><description>200: The request was
            successful, and the accounts are returned.</description></item> <item><description>400: The request is
            invalid, such as if <paramref name="walletId"/> is malformed.</description></item> <item><description>401:
            The caller is not authenticated.</description></item> <item><description>403: The caller does not have
            permission to access the wallet.</description></item> <item><description>500: An internal server error
            occurred.</description></item> </list></remarks>
            <param name="walletId">The unique identifier of the wallet for which the accounts are being requested.</param>
            <returns>An <see cref="T:Nbg.Ewallet.Api.Types.Wallet.AvailableAccountsResponse"/> containing the list of accounts associated with the wallet. If no
            accounts are available, the response will contain an empty list.</returns>
            <response code="200">The request was successful, and the accounts are returned.</response>
            <response code="400">The request is invalid, such as if walletId is malformed.</response>
            <response code="401">The caller is not authenticated.</response>
            <response code="403">The caller does not have permission to access the wallet.</response>
            <response code="500">An internal server error occurred.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.SearchWallets(System.String,System.String,System.String,System.String)">
            <summary>
            Search for other wallets.
            </summary>
            <remarks>
            Search for other registered wallets within the eWallet community.
            All filters are applied with a %like% operator.
            At least one filter must be present, otherwise a 4XX error will be returned.
            </remarks>
            <param name="vatNumber">The VAT number to search for.</param>
            <param name="walletName">The wallet name to search for.</param>
            <param name="iban">The IBAN to search for.</param>
            <param name="organizationName">The OrganizationName to search for.</param>
            <returns>A list of wallets that match the search criteria.</returns>
            <response code="200">Wallets retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.GetWalletStatements(System.Guid,Nbg.Ewallet.Api.Types.WalletStatementsRequest)">
            <summary>
            Retrieves statements for a wallet account.
            </summary>
            <remarks>
            The data returned will be the same as returned from the corresponding functionality from IB.
            It includes transactions that have happened outside of the eWallet ecosystem.
            </remarks>
            <param name="walletId">The wallet identifier.</param>
            <param name="request">The request containing statement details.</param>
            <returns>A response containing wallet statements.</returns>
            <response code="200">Statements retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.GetWalletTransactions(System.Guid,Nbg.Ewallet.Repository.Types.TransactionRequestQueryParams)">
            <summary>
            Retrieves transactions for a wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="request">The request containing transaction details.</param>
            <returns>A list of transactions for the specified wallet.</returns>
            <response code="200">Transactions retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.Load(System.Guid,Nbg.Ewallet.Api.Types.WalletLoadRequest)">
            <summary>
            Loads funds into the wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="request">The request containing load details.</param>
            <returns>The response after loading the wallet.</returns>
            <response code="200">Funds loaded successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletServiceController.UnLoad(System.Guid,Nbg.Ewallet.Api.Types.WalletLoadRequest)">
            <summary>
            Unloads funds from the wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="request">The request containing unload details.</param>
            <returns>The response after unloading the wallet.</returns>
            <response code="200">Funds unloaded successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.WalletSubscriptionController">
            <summary>
            This Controller Provides Wallet Subscription functionality.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletSubscriptionController.CreateSubscription(Nbg.Ewallet.Api.Types.Subscriptions.CreateSubscriptionRequest)">
            <summary>
            Updates an existing subscription.
            </summary>
            <remarks>
            Updates an existing subsciption.
            The new subscription can be of a different Plan Id than the current one.
            </remarks>
            <param name="request">The subscription update request.</param>
            <returns>The response of the subscription.</returns>
            <response code="200">Returns the created subscription response.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletSubscriptionController.OptOutSubscription">
            <summary>
            Subscription opt out
            </summary>
            <remarks>
            Subscription opt out.
            If the user decides not to continue with the subscription plan.
            </remarks>
            <param name="request">The subscription opt out request.</param>
            <returns>The response of the subscription.</returns>
            <response code="200">Returns the created subscription response.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletSubscriptionController.GetSubscription(System.Guid)">
            <summary>
            Returns the active subscription for the wallet.
            Will return an error message in case that no active subscription is found.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <returns>The subscription details.</returns>
            <response code="200">Returns the subscription details.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="404">If the subscription is not found.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletSubscriptionController.GetSubscriptionUsage(System.Guid)">
            <summary>
            Returns the current subscription usage, in terms of discount transactions consumption.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <returns>The subscription usage details.</returns>
            <response code="200">Returns the subscription usage details.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="404">If the subscription usage is not found.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.WalletTransactionController">
            <summary>
            This Controller Provides Wallet Transaction functionality.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletTransactionController.ApproveTransaction(System.Guid,System.String)">
            <summary>
            Approves a transaction for a wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="transactionId">The transaction identifier.</param>
            <returns>The approved transaction.</returns>
            <response code="200">Transaction approved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletTransactionController.RejectTransaction(System.Guid,System.String)">
            <summary>
            Rejects a transaction for a wallet account.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="transactionId">The transaction identifier.</param>
            <returns>The rejected transaction.</returns>
            <response code="200">Transaction rejected successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="T:Nbg.Ewallet.Api.Controllers.WalletUserPermissionsController">
            <summary>
            This Controller Provides Wallet User Permissions functionality.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletUserPermissionsController.SetWalletUserPermissions(Nbg.Ewallet.Api.Types.Wallet.SetWalletUserPermissionsRequest)">
            <summary>
            Grants access to internal company users.
            </summary>
            <param name="request">The request containing user permissions details.</param>
            <returns>A response indicating the result of the operation.</returns>
            <response code="200">Permissions granted successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletUserPermissionsController.GetWalletUserPermissions(System.Guid,System.Boolean)">
            <summary>
            Retrieves all internal company users' permissions.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="showAll">Whether to show all permissions.</param>
            <returns>A list of user permissions.</returns>
            <response code="200">Permissions retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletUserPermissionsController.GetWalletPermissionForUser(System.Guid,System.String,System.Boolean)">
            <summary>
            Retrieves access rights for a specific internal wallet user.
            </summary>
            <param name="walletId">The wallet identifier.</param>
            <param name="userid">The user identifier.</param>
            <param name="showAll">Whether to show all permissions, even if the permission is expired.</param>
            <returns>A list of permissions for the specified user.</returns>
            <response code="200">Permissions retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletUserPermissionsController.RevokeUserPermissionsForUser(System.String)">
            <summary>
            Revokes access of a specific internal user.
            </summary>
            <param name="userid">The user identifier.</param>
            <returns>The revoked user permission.</returns>
            <response code="200">Permission revoked successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="M:Nbg.Ewallet.Api.Controllers.WalletUserPermissionsController.GetOrganizationUsers(System.Guid)">
            <summary>
            Retrieves available user to assign user permissions to.
            </summary>
            <remarks>
            Retrieves all company users that are available to be connected to the wallet.
            This will only return data for corporate IB accounts.
            </remarks>
            <param name="walletId">The wallet identifier.</param>
            <returns>A response containing available users.</returns>
            <response code="200">Users retrieved successfully.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is unauthorized.</response>
            <response code="403">If the user is forbidden from accessing this resource.</response>
            <response code="500">If there is an internal server error.</response>
        </member>
        <member name="T:Nbg.Ewallet.Api.Middlewares.ErrorResponse">
            <summary>
            Represents an error response returned by the API.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Middlewares.ErrorResponse.Code">
            <summary>
            Gets or sets the error code or title of the error.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Middlewares.ErrorResponse.Status">
            <summary>
            Gets or sets the HTTP status code of the response.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Middlewares.ErrorResponse.TraceId">
            <summary>
            Gets or sets the trace identifier for the request, useful for debugging.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Middlewares.ErrorResponse.Errors">
            <summary>
            Gets or sets the details of the error.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Middlewares.ErrorResponse.#ctor(Microsoft.AspNetCore.Http.HttpContext,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Nbg.Ewallet.Api.Middlewares.ErrorResponse"/> class using the specified HTTP context and exception.
            </summary>
            <param name="context">The HTTP context for the current request.</param>
            <param name="exception">The exception that triggered the error response.</param>
        </member>
        <member name="T:Nbg.Ewallet.Api.Middlewares.ErrorDetails">
            <summary>
            Represents the details of an error.
            </summary>
        </member>
        <member name="P:Nbg.Ewallet.Api.Middlewares.ErrorDetails.Message">
            <summary>
            Gets or sets the list of error messages.
            </summary>
        </member>
        <member name="M:Nbg.Ewallet.Api.Middlewares.ErrorDetails.#ctor(System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Nbg.Ewallet.Api.Middlewares.ErrorDetails"/> class.
            </summary>
            <param name="message">The list of error messages.</param>
        </member>
        <member name="M:BaseAuthorizationRule.CheckAuthorizationContext(Nbg.Ewallet.Api.Types.AuthorizationContext)">
            <summary>
            Validates that the provided <paramref name="authContext"/> is not null and contains an active permission.
            Throws a <see cref="T:Nbg.Ewallet.Repository.Types.Exceptions.PermissionNotFoundException"/> if the validation fails.
            </summary>
            <param name="authContext">The authorization context to validate.</param>
            <exception cref="T:Nbg.Ewallet.Repository.Types.Exceptions.PermissionNotFoundException">Thrown when the authorization context or active permission is null.</exception>
        </member>
        <member name="M:BaseAuthorizationRule.CheckWalletId(Nbg.Ewallet.Api.Types.AuthorizationContext,Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)">
            <summary>
            Validates that the wallet ID in the route data matches the wallet ID in the active permission of the <paramref name="authContext"/>.
            Throws a <see cref="T:Nbg.Ewallet.Repository.Types.Exceptions.PermissionNotFoundException"/> if the validation fails.
            </summary>
            <param name="authContext">The authorization context containing the active permission.</param>
            <param name="context">The filter context containing the route data.</param>
            <exception cref="T:Nbg.Ewallet.Repository.Types.Exceptions.PermissionNotFoundException">
            Thrown when the route data is null, the wallet ID is missing or invalid, or the wallet IDs do not match.
            </exception>
        </member>
    </members>
</doc>
