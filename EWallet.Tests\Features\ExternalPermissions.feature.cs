﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Spec<PERSON>low (https://www.specflow.org/).
//      SpecFlow Version:3.9.0.0
//      SpecFlow Generator Version:3.9.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
namespace EWallet.Tests.Features
{
    using TechTalk.SpecFlow;
    using System;
    using System.Linq;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public partial class ExternalWalletPermissionFeature : object, Xunit.IClassFixture<ExternalWalletPermissionFeature.FixtureData>, System.IDisposable
    {
        
        private static TechTalk.SpecFlow.ITestRunner testRunner;
        
        private static string[] featureTags = ((string[])(null));
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "ExternalPermissions.feature"
#line hidden
        
        public ExternalWalletPermissionFeature(ExternalWalletPermissionFeature.FixtureData fixtureData, EWallet_Tests_XUnitAssemblyFixture assemblyFixture, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
            this.TestInitialize();
        }
        
        public static void FeatureSetup()
        {
            testRunner = TechTalk.SpecFlow.TestRunnerManager.GetTestRunner();
            TechTalk.SpecFlow.FeatureInfo featureInfo = new TechTalk.SpecFlow.FeatureInfo(new System.Globalization.CultureInfo("en-US"), "Features", "External Wallet Permission", null, ProgrammingLanguage.CSharp, featureTags);
            testRunner.OnFeatureStart(featureInfo);
        }
        
        public static void FeatureTearDown()
        {
            testRunner.OnFeatureEnd();
            testRunner = null;
        }
        
        public void TestInitialize()
        {
        }
        
        public void TestTearDown()
        {
            testRunner.OnScenarioEnd();
        }
        
        public void ScenarioInitialize(TechTalk.SpecFlow.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public void ScenarioStart()
        {
            testRunner.OnScenarioStart();
        }
        
        public void ScenarioCleanup()
        {
            testRunner.CollectScenarioErrors();
        }
        
        void System.IDisposable.Dispose()
        {
            this.TestTearDown();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="As an IB user, I want to give permissionss to an external wallet")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "As an IB user, I want to give permissionss to an external wallet")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void AsAnIBUserIWantToGivePermissionssToAnExternalWallet(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("As an IB user, I want to give permissionss to an external wallet", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 2
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 3
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 4
    testRunner.When(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 5
    testRunner.Then(string.Format("the external wallet must have {0} and {1} and {2} and {3} and {4} and {5} and {6}" +
                            " and {7}", admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="As an IB user, I want to revoke permissionss of an external user")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "As an IB user, I want to revoke permissionss of an external user")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void AsAnIBUserIWantToRevokePermissionssOfAnExternalUser(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("As an IB user, I want to revoke permissionss of an external user", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 12
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 13
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 15
    testRunner.When(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 16
    testRunner.And(string.Format("I revoke permissions from {0} to {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 17
    testRunner.Then(string.Format("the permission is revoked from {0} to {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Fail to assign permissions to own wallet")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "Fail to assign permissions to own wallet")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void FailToAssignPermissionsToOwnWallet(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Fail to assign permissions to own wallet", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 24
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 25
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 28
    testRunner.When(string.Format("I give permissions from {0} to the same wallet with {1} and {2} and {3} and {4} a" +
                            "nd {5} and {6} and {7} and {8}", userId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 29
    testRunner.Then(string.Format("no persissions are assigned the for {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Fail to revoke permissions to own wallet")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "Fail to revoke permissions to own wallet")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void FailToRevokePermissionsToOwnWallet(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Fail to revoke permissions to own wallet", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 36
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 37
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 40
    testRunner.When(string.Format("I give permissions from {0} to the same wallet with {1} and {2} and {3} and {4} a" +
                            "nd {5} and {6} and {7} and {8}", userId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 41
    testRunner.Then(string.Format("I cannot revoke the permissions as {0} for {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Succesfully retrieve only active permissions when showAll flag is false for all e" +
            "xternal wallets")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "Succesfully retrieve only active permissions when showAll flag is false for all e" +
            "xternal wallets")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void SuccesfullyRetrieveOnlyActivePermissionsWhenShowAllFlagIsFalseForAllExternalWallets(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Succesfully retrieve only active permissions when showAll flag is false for all e" +
                    "xternal wallets", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 49
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 50
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 52
    testRunner.When(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 54
    testRunner.And(string.Format("I revoke permissions from {0} to {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 56
    testRunner.And(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 57
    testRunner.Then(string.Format("Only the Active permissions are retrieved for all wallets for {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Succesfully retrieve all active permissions when showAll flag is false for all ex" +
            "ternal wallets")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "Succesfully retrieve all active permissions when showAll flag is false for all ex" +
            "ternal wallets")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void SuccesfullyRetrieveAllActivePermissionsWhenShowAllFlagIsFalseForAllExternalWallets(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Succesfully retrieve all active permissions when showAll flag is false for all ex" +
                    "ternal wallets", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 65
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 66
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 69
    testRunner.When(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 70
    testRunner.And(string.Format("I revoke permissions from {0} to {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 71
    testRunner.And(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 72
    testRunner.And(string.Format("I revoke permissions from {0} to {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Succesfully retrieve only active permissions when showAll flag is false for a spe" +
            "cific wallet")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "Succesfully retrieve only active permissions when showAll flag is false for a spe" +
            "cific wallet")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void SuccesfullyRetrieveOnlyActivePermissionsWhenShowAllFlagIsFalseForASpecificWallet(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Succesfully retrieve only active permissions when showAll flag is false for a spe" +
                    "cific wallet", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 79
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 80
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 83
    testRunner.When(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 84
    testRunner.And(string.Format("I revoke permissions from {0} to {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 85
    testRunner.And(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 86
    testRunner.Then(string.Format("Only the Active permissions are retrieved for {0} and {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Succesfully all permissions when showAll flag is false for a specific wallet")]
        [Xunit.TraitAttribute("FeatureTitle", "External Wallet Permission")]
        [Xunit.TraitAttribute("Description", "Succesfully all permissions when showAll flag is false for a specific wallet")]
        [Xunit.InlineDataAttribute("0", "1", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void SuccesfullyAllPermissionsWhenShowAllFlagIsFalseForASpecificWallet(string userId, string externalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("externalUserId", externalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Succesfully all permissions when showAll flag is false for a specific wallet", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 93
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 94
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 97
    testRunner.When(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 98
    testRunner.And(string.Format("I revoke permissions from {0} to {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 99
    testRunner.And(string.Format("I give permissions to {0}  with {1} and {2} and {3} and {4} and {5} and {6} and {" +
                            "7} and {8}", externalUserId, admin, approve, balanceView, submit, transactionView, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 100
    testRunner.Then(string.Format("All permissions are retrieved for {0} and {1}", userId, externalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
        [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : System.IDisposable
        {
            
            public FixtureData()
            {
                ExternalWalletPermissionFeature.FeatureSetup();
            }
            
            void System.IDisposable.Dispose()
            {
                ExternalWalletPermissionFeature.FeatureTearDown();
            }
        }
    }
}
#pragma warning restore
#endregion
