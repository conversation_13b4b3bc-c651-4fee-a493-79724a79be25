﻿namespace Nbg.Ewallet.Repository.dbQueries;

internal static class ServiceAuditQueries
{
    internal const string Insert =
        "INSERT INTO [dbo].[ServiceAudit] " +
        "([Id],[Timestamp],[Host],[ServiceName],[Application],[ClientRequestPath],[ClientSession],[RequestTextData],[ResponseTextData]," +
        "[ErrorData],[ExecutionTime],[EndTime],[ExtraTextData]) " +
        "VALUES " +
        "(@Id,@Timestamp,@Host,@ServiceName,@Application,@ClientRequestPath,@ClientSession,@RequestTextData,@ResponseTextData," +
        "@ErrorData,@ExecutionTime,@EndTime,@ExtraTextData)";

    internal const string UpdateById =
        @"UPDATE [dbo].[ServiceAudit]
               SET [Timestamp] = @Timestamp
                  ,[Host] = @Host
                  ,[ServiceName] = @ServiceName
                  ,[Application] = @Application
                  ,[ClientRequestPath] = @ClientRequestPath
                  ,[ClientSession] = @ClientSession
                  ,[RequestTextData] = @RequestTextData
                  ,[ResponseTextData] = @ResponseTextData
                  ,[ErrorData] = @ErrorData
                  ,[ExecutionTime] = @ExecutionTime
                  ,[EndTime] = @EndTime
                  ,[ExtraTextData] = @ExtraTextData
             WHERE Id = @Id";
}
