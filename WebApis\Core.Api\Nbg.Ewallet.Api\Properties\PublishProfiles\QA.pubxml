﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>FileSystem</WebPublishMethod>
    <PublishProvider>FileSystem</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish />
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <TargetFramework>net8.0</TargetFramework>
    <ProjectGuid>14A05138-CBED-4EB6-8D4C-E81B4A4607D2</ProjectGuid>
    <SelfContained>false</SelfContained>
    <PublishUrl>C:\publish\qa\nbg.ewallet.core.api</PublishUrl>
    <DeleteExistingFiles>True</DeleteExistingFiles>
    <EnvironmentName>QA</EnvironmentName>
  </PropertyGroup>

  <ItemGroup>
    <Content Update="appSettings.*.json" CopyToPublishDirectory="Never" />
    <Content Update="nlog.*.config" CopyToPublishDirectory="Never" />

    <ResolvedFileToPublish Include="appsettings.QA.json">
      <RelativePath>appsettings.QA.json</RelativePath>
    </ResolvedFileToPublish>

    <ResolvedFileToPublish Include="nlog.QA.config">
      <RelativePath>nlog.QA.config</RelativePath>
    </ResolvedFileToPublish>
  </ItemGroup>
</Project>
