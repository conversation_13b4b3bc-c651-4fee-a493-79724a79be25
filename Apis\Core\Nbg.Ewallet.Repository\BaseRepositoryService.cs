﻿using System;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.NetCore.Data;

namespace Nbg.Ewallet.Repository;

public abstract class BaseRepositoryService
{
    private readonly ILogger _logger;
    private readonly IDbConnector? _dbConnector;
    private readonly IUnitOfWork? _unitOfWork;
    private readonly string? _connectionName;

    protected BaseRepositoryService(
        IDbConnectorFactory dbConnectorFactory,
        IOptions<RepositorySettings> repositoryOptions,
        ILogger logger)
    {
        ArgumentNullException.ThrowIfNull(dbConnectorFactory, nameof(dbConnectorFactory));
        ArgumentNullException.ThrowIfNull(repositoryOptions, nameof(repositoryOptions));
        ArgumentNullException.ThrowIfNull(repositoryOptions.Value.ConnectionName, nameof(repositoryOptions.Value.ConnectionName));
        ArgumentNullException.ThrowIfNull(logger, nameof(logger));
        _dbConnector = dbConnectorFactory.Get(repositoryOptions.Value.ConnectionName);
        _logger = logger;
    }

    protected BaseRepositoryService(
    IUnitOfWork unitOfWork,
    IOptions<RepositorySettings> repositoryOptions,
    ILogger logger)
    {
        ArgumentNullException.ThrowIfNull(unitOfWork, nameof(unitOfWork));
        ArgumentNullException.ThrowIfNull(repositoryOptions, nameof(repositoryOptions));
        ArgumentNullException.ThrowIfNull(repositoryOptions.Value.ConnectionName, nameof(repositoryOptions.Value.ConnectionName));
        ArgumentNullException.ThrowIfNull(logger, nameof(logger));
        _unitOfWork = unitOfWork;
        _connectionName = repositoryOptions.Value.ConnectionName;
        _logger = logger;
    }

    protected IDbConnection GetConnection()
    {
        ArgumentNullException.ThrowIfNull(_dbConnector, nameof(_dbConnector));
        return _dbConnector.Open();
    }

    // Helper methods for consistent exception handling
    protected async Task<T> ExecuteAsync<T>(Func<Task<T>> func, string errorMessage)
    {
        ArgumentNullException.ThrowIfNull(_dbConnector, nameof(_dbConnector));
        try
        {
            return await func();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            throw new Exception(errorMessage, ex);
        }
    }

    protected async Task ExecuteAsync(Func<Task> func, string errorMessage)
    {
        ArgumentNullException.ThrowIfNull(_dbConnector, nameof(_dbConnector));
        try
        {
            await func();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            throw new Exception(errorMessage, ex);
        }
    }

    protected async Task ExecuteInTransactionAsync(Func<IDbConnection, IDbTransaction, Task> action, string errorMessage)
    {
        ArgumentNullException.ThrowIfNull(_unitOfWork, nameof(_unitOfWork));
        await _unitOfWork.ExecuteInTransactionAsync(
             _connectionName,
             async (conn, tx) =>
             {
                 await action(conn, tx);
             },
             errorMessage
         );
    }

    protected async Task<TResult> ExecuteInTransactionAsync<TResult>(Func<IDbConnection, IDbTransaction, Task<TResult>> action, string errorMessage)
    {
        ArgumentNullException.ThrowIfNull(_unitOfWork, nameof(_unitOfWork));
        return await _unitOfWork.ExecuteInTransactionAsync(
                _connectionName,
                async (conn, tx) =>
                {
                    return await action(conn, tx);
                },
                errorMessage
            );
    }
}
