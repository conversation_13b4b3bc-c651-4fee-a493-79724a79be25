sequenceDiagram
	autonumber
	Client->>EWallet.Proxy: /company/wallet POST
	EWallet.Proxy->>EWallet.Core: /company/wallet POST
	EWallet.Core->>Accounts.Core: /accounts/openAccount POST
	Accounts.Core->>EWallet.Core: {walletAccount}
	EWallet.Core->>EWallet.DB: INSERT
	note over EWallet.DB: DB Table:  Wallets
	EWallet.DB->>EWallet.Core: {companyId, walletAccount}
	EWallet.Core->>EWallet.Proxy: {companyId, walletAccount}
	EWallet.Proxy->>Client: {companyId, walletAccount}