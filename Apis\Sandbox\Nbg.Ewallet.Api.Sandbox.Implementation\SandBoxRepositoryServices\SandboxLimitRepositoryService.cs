﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxLimitRepositoryService : ILimitRepositoryService
{
    private readonly IMapper _mapper;
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;

    public SandboxLimitRepositoryService(IMapper mapper, ISandBoxRepositoryService sandBoxRepositoryService)
    {
        _mapper = mapper;
        _sandBoxRepositoryService = sandBoxRepositoryService;
    }

    public async Task<List<RepoLimit>> GetByPermissionIdAsync(string permissionId)
    {
        if (!Guid.TryParse(permissionId, out var permissionID)) return [];
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        var limits = model.UserPermissions
            .Where(x => x.Id == permissionID)
            .Select(x => x.Limits)
            .FirstOrDefault();

        if (limits == null)
        {
            limits = model.WalletsPermissions
                .Where(x => x.Id == permissionID)
                .Select(x => x.Limits)
                .FirstOrDefault();
        }

        if (limits == null) { return []; }

        return limits.Select(_mapper.Map<RepoLimit>).ToList();
    }

    public async Task<List<RepoLimit>> GetByPermissionIdTypeAsync(string permissionId, LimitTransactionType transactionType)
    {
        //if (!Guid.TryParse(permissionId, out var permissionID)) return new List<RepoLimit>();
        var limits = await GetByPermissionIdAsync(permissionId);
        if (limits.Count == 0) return limits;

        limits = limits.Where(l => l.TransactionType == transactionType).ToList();
        return limits;
    }

    public async Task InsertUserLimitsAsync(IEnumerable<RepoLimit> limits)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();

        foreach (var userPermission in model.UserPermissions)
        {
            userPermission.Limits ??= new List<SandboxLimit>();
            var repoLimits = limits.Where(x => x.PermissionId == userPermission.Id).ToList();
            if (repoLimits.Count != 0) userPermission.Limits.AddRange(_mapper.Map<List<SandboxLimit>>(repoLimits));
        }

        foreach (var walletPermission in model.WalletsPermissions)
        {
            walletPermission.Limits ??= new List<SandboxLimit>();
            var repoLimits = limits.Where(x => x.PermissionId == walletPermission.Id).ToList();
            if (repoLimits.Count != 0) walletPermission.Limits.AddRange(_mapper.Map<List<SandboxLimit>>(repoLimits));
        }

        await _sandBoxRepositoryService.UpdateSandboxData(model);
    }

    public async Task<List<RepoLimit>> RepoLimitFindAllByPermissionId(Guid permissionId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        var permissions = mEwalletSandbox.UserPermissions;
        var limits = permissions.SelectMany(x => x.Limits);
        var ActiveLimits = limits.Where(x => x.PermissionId == permissionId);
        var repolimits = _mapper.Map<List<RepoLimit>>(ActiveLimits);
        return repolimits;
    }

    public async Task<List<RepoLimit>> RepoLimitFindAllByPermissionIdAsync(Guid permissionId)
    {
        var mEwalletSandbox = await _sandBoxRepositoryService.GetSandBoxModel();
        var permissions = mEwalletSandbox.UserPermissions.Where(x => x.Id == permissionId && x.ExpirationDate > DateTime.UtcNow).ToList();
        var limits = permissions.SelectMany(x => x.Limits);
        var repolimits = _mapper.Map<List<RepoLimit>>(limits);
        return repolimits;
    }
}
