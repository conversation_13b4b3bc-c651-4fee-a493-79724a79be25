﻿using System;
using System.Threading.Tasks;
using ibank.ThirdParty.Types;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Payments;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IPaymentsService
{
    Task<PaymentsResponse> ExecuteAsync(BatchPaymentsRequest request, string walletId);
    Task<ExtendedPaymentResponse> PayAsync(ExtendedPaymentsRequest request, string walletId);
    Task<CommissionResponse> CommissionAsync(ExtendedPaymentsRequest request, Guid walletId);
    Task<BatchCommissionResponse> BatchCommissionAsync(BatchPaymentsCommissionRequest request, Guid walletId);
    Task<PaymentsForecastResponse> ExecuteForecastAsync(BatchPaymentsRequest request, string walletId);
    Task<ExtendedPaymentForecastResponse> PayForecastAsync(ExtendedPaymentsRequest request, string walletId);
}
