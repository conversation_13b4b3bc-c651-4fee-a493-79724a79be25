﻿using System;
using System.Text;

namespace Nbg.Ewallet.Repository.Types;

public class RepoUserPermission : IPermission
{
    public Guid Id { get; set; }

    public string UserId { get; set; }

    public Guid WalletId { get; set; }

    public bool Submit { get; set; }

    public bool Approve { get; set; }

    public bool BalanceView { get; set; }

    public bool TransactionView { get; set; }

    public bool Admin { get; set; }

    public bool InheritsAuthorizations { get; set; }

    public DateTime ExpirationDate { get; set; }

    public DateTime CreatedAt { get; set; }

    public string AssignedBy { get; set; }

    public string RevokedBy { get; set; }
    PermissionType IPermission.PermissionType => PermissionType.USER;

    public string ToLogString()
    {
        var sb = new StringBuilder();

        AppendLine(ref sb, "Id", Id);
        AppendLine(ref sb, "UserId", UserId);
        AppendLine(ref sb, "WalletId", WalletId);
        AppendLine(ref sb, "Submit", Submit);
        AppendLine(ref sb, "Approve", Approve);
        AppendLine(ref sb, "BalanceView", BalanceView);
        AppendLine(ref sb, "TransactionView", TransactionView);
        AppendLine(ref sb, "Admin", Admin);
        AppendLine(ref sb, "InheritsAuthorizations", InheritsAuthorizations);
        AppendLine(ref sb, "ExpirationDate", ExpirationDate);
        AppendLine(ref sb, "CreatedAt", CreatedAt);
        AppendLine(ref sb, "AssignedBy", AssignedBy);
        AppendLine(ref sb, "RevokedBy", RevokedBy);

        return sb.ToString();
    }

    private static void AppendLine<T>(ref StringBuilder sb, ReadOnlySpan<char> label, T value)
    {
        sb.Append(label).Append(": ").Append(value).AppendLine();
    }

}
