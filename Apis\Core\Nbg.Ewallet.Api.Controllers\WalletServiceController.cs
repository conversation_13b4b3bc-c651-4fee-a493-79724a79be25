using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// This Controller Provides Wallet Services functionality.
/// </summary>
[ApiController]
[ApiExplorerSettings(GroupName = "Wallet")]
[Route("wallet")]
[Produces("application/json")]
[Consumes("application/json")]
public class WalletServiceController : ControllerBase
{
    private readonly IWalletService _walletService;

    public WalletServiceController(IWalletService walletService)
    {
        _walletService = walletService;
    }

    /// <summary>
    /// Retrieves information about a specific wallet.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <returns>The wallet information.</returns>
    /// <response code="200">Returns the wallet information.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="404">If the wallet is not found.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(Wallet), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 404)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/info", Name = "GetWalletInformation")]
    [EwalletAuthorize(AuthorizationTypes.Active)]
    public async Task<ActionResult<Wallet>> GetWalletInformation(Guid walletId)
    {
        return await _walletService.GetWalletInformation(walletId);
    }

    /// <summary>
    /// Retrieves the current balance of a subscription plan.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <returns>The current balance of the subscription plan.</returns>
    /// <response code="200">Returns the current balance.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="404">If the wallet or balance is not found.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(GetWalletBalanceResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 404)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/balance", Name = "balance")]
    [EwalletAuthorize(AuthorizationTypes.BalanceView, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<GetWalletBalanceResponse>> GetWalletBalance(Guid walletId)
    {
        return await _walletService.GetWalletBalance(walletId);
    }

    /// <summary>
    /// Retrieves the current balance of a subscription plan.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <returns>The current balance of the subscription plan.</returns>
    /// <response code="200">Returns the current balance.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="404">If the wallet or balance is not found.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(Wallet), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 404)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPut]
    [Route("{walletId}/edit", Name = "Edit")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<Wallet>> EditWalletName([FromRoute] Guid walletId, EditWalletNameRequest request)
    {
        return await _walletService.EditWalletName(walletId, request);
    }

    /// <summary>
    /// Retrieves transactions for a wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="request">The request containing transaction details.</param>
    /// <returns>A list of transactions for the specified wallet.</returns>
    /// <response code="200">Transactions retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<Transaction>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/my-transactions", Name = "getmytransactions")]
    [EwalletAuthorize(AuthorizationTypes.Active)]
    public async Task<ActionResult<List<Transaction>>> GetMyTransactions([FromRoute] Guid walletId)
    {
        return await _walletService.GetMyTransactionsAsync(walletId);
    }

    /// <summary>
    /// Retrieves wallet account statements for the specified wallet and returns them as a PDF file.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="request">The request containing statement query parameters.</param>
    /// <returns>Statement in a PDF format.</returns>
    /// <response code="200">Authorization request rejected successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/statements-pdf-export", Name = "statementspdfexport")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<IActionResult> GetWalletStatementPdfExport([FromRoute] Guid walletId, [FromQuery] StatementRequestQueryParams request)
    {
        var res = await _walletService.GetWalletStatementPdfExport(walletId, request);
        return File(res, "application/pdf");
    }

    /// <summary>
    /// Retrieves the list of available accounts associated with the specified wallet and user logged in.
    /// </summary>
    /// <remarks>This endpoint requires the wallet to be active and the caller to be authorized. The
    /// response may include various HTTP status codes: <list type="bullet"> <item><description>200: The request was
    /// successful, and the accounts are returned.</description></item> <item><description>400: The request is
    /// invalid, such as if <paramref name="walletId"/> is malformed.</description></item> <item><description>401:
    /// The caller is not authenticated.</description></item> <item><description>403: The caller does not have
    /// permission to access the wallet.</description></item> <item><description>500: An internal server error
    /// occurred.</description></item> </list></remarks>
    /// <param name="walletId">The unique identifier of the wallet for which the accounts are being requested.</param>
    /// <returns>An <see cref="AvailableAccountsResponse"/> containing the list of accounts associated with the wallet. If no
    /// accounts are available, the response will contain an empty list.</returns>
    /// <response code="200">The request was successful, and the accounts are returned.</response>
    /// <response code="400">The request is invalid, such as if walletId is malformed.</response>
    /// <response code="401">The caller is not authenticated.</response>
    /// <response code="403">The caller does not have permission to access the wallet.</response>
    /// <response code="500">An internal server error occurred.</response>
    [ProducesResponseType(typeof(AvailableAccountsResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/accounts", Name = "GetUserAccounts")]
    [EwalletAuthorize(AuthorizationTypes.Active)]
    public async Task<ActionResult<AvailableAccountsResponse>> GetUserAccounts(Guid walletId)
    {
        return await _walletService.GetUserAccountsAsync(walletId);
    }

    /// <summary>
    /// Search for other wallets.
    /// </summary>
    /// <remarks>
    /// Search for other registered wallets within the eWallet community.
    /// All filters are applied with a %like% operator.
    /// At least one filter must be present, otherwise a 4XX error will be returned.
    /// </remarks>
    /// <param name="vatNumber">The VAT number to search for.</param>
    /// <param name="walletName">The wallet name to search for.</param>
    /// <param name="iban">The IBAN to search for.</param>
    /// <param name="organizationName">The OrganizationName to search for.</param>
    /// <returns>A list of wallets that match the search criteria.</returns>
    /// <response code="200">Wallets retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<WalletLite>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("search-wallets", Name = "SearchWallets")]
    public async Task<ActionResult<List<WalletLite>>> SearchWallets([FromQuery] string vatNumber, [FromQuery] string walletName, [FromQuery] string iban,
        [FromQuery] string organizationName)
    {
        return await _walletService.SearchWallets(vatNumber, walletName, iban, organizationName);
    }

    /// <summary>
    /// Retrieves statements for a wallet account.
    /// </summary>
    /// <remarks>
    /// The data returned will be the same as returned from the corresponding functionality from IB.
    /// It includes transactions that have happened outside of the eWallet ecosystem.
    /// </remarks>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="request">The request containing statement details.</param>
    /// <returns>A response containing wallet statements.</returns>
    /// <response code="200">Statements retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(WalletStatementsResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/statements", Name = "statements")]
    [EwalletAuthorize(AuthorizationTypes.Admin)]
    public async Task<ActionResult<WalletStatementsResponse>> GetWalletStatements([FromRoute] Guid walletId, [FromQuery] WalletStatementsRequest request)
    {
        return await _walletService.GetWalletStatements(walletId, request);
    }

    /// <summary>
    /// Retrieves transactions for a wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="request">The request containing transaction details.</param>
    /// <returns>A list of transactions for the specified wallet.</returns>
    /// <response code="200">Transactions retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<Transaction>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/transactions", Name = "getwallettransactions")]
    [EwalletAuthorize(AuthorizationTypes.TransactionView, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<List<Transaction>>> GetWalletTransactions([FromRoute] Guid walletId, [FromQuery] TransactionRequestQueryParams request)
    {
        return await _walletService.GetWalletTransactions(walletId, request);
    }

    /// <summary>
    /// Loads funds into the wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="request">The request containing load details.</param>
    /// <returns>The response after loading the wallet.</returns>
    /// <response code="200">Funds loaded successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(WalletLoadResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPost]
    [Route("{walletId}/load", Name = "load")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<WalletLoadResponse>> Load([FromRoute] Guid walletId, WalletLoadRequest request)
    {
        return await _walletService.Load(walletId, request);
    }

    /// <summary>
    /// Unloads funds from the wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="request">The request containing unload details.</param>
    /// <returns>The response after unloading the wallet.</returns>
    /// <response code="200">Funds unloaded successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(WalletLoadResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPost]
    [Route("{walletId}/unload", Name = "unload")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<WalletLoadResponse>> UnLoad([FromRoute] Guid walletId, WalletLoadRequest request)
    {
        return await _walletService.Unload(walletId, request);
    }
}
