using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.HttpClients;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.CorporateApi;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Repository;

public class CorporateApiClientService : AuditableClientService, ICorporateApiClientService
{
    private readonly HttpClient _client;
    private readonly CorporateApiClientSettings _configuration;
    private readonly ILogger<CorporateApiClientService> _logger;

    public CorporateApiClientService(
        IHttpClientFactory httpClientFactory,
        IOptions<CorporateApiClientSettings> configuration,
        ILogger<CorporateApiClientService> logger,
        IServiceAuditRepositoryService serviceAuditRepositoryService)
        : base(serviceAuditRepositoryService, logger)
    {
        _configuration = configuration.Value;
        _client = httpClientFactory.CreateClient(_configuration.Client);
        _logger = logger;
    }

    public override HttpClient WithHttpClient()
    {
        return _client;
    }

    public async Task<bool> ConnectAccountAsync(string userId, string ibanAccount)
    {
        if (string.IsNullOrWhiteSpace(ibanAccount)) throw new WalletAccountNotFoundException();

        var availableAccount = await GetAvailableAccount(userId, ibanAccount);
        var linkedAccounts = await GetLinkedAccounts(userId);

        // TODO: remove after we unlink an account.
        var linkedAccount = linkedAccounts.FirstOrDefault(x => x.Values.Select(x => x.Number).Contains(ibanAccount));
        if (linkedAccount != null) return true;

        var response = await UpdateProducts(userId, availableAccount, linkedAccounts);
        return response.Success;
    }
    public async Task<bool> DisconnectAccountAsync(string userId, string ibanAccount)
    {
        if (string.IsNullOrWhiteSpace(ibanAccount)) throw new WalletAccountNotFoundException();
        var linkedAccounts = await GetLinkedAccounts(userId);

        var linkedAccount = linkedAccounts.FirstOrDefault(x => x.Values.Select(x => x.Number).Contains(ibanAccount));
        if (linkedAccount == null)
        {
           return true; // Nothing to disconnect
        }

        linkedAccounts.Remove(linkedAccount);
        var response = await UpdateProducts(userId, null, linkedAccounts);
        return response.Success;
    }

    public async Task<CompanyUserProfiles> GetCompanyUsersAsync(string userId)
    {
        var companyUsersRequest = new GetCompanyUserGenericRequest(userId).Wrap();
        using var updateProductsRequest = companyUsersRequest.ToHttpRequestMessage(_configuration.GetCompanyUsers);
        return await SendAsync<CompanyUserProfiles>(updateProductsRequest);
    }

    #region [ Private Helpers ]

    private async Task<CorpUserProductDetails> GetAvailableAccount(string userId, string ibanAccount)
    {
        var request = new CompanyUserGenericRequest(userId).Wrap();
        using var availableProductsRequest = request.ToHttpRequestMessage(_configuration.GetAvailableProducts);
        var availableProductsResponse = await SendAsync<AvailableProductsResponse>(availableProductsRequest);

        var availableProduct = availableProductsResponse
            .Products
            .Where(x => x.ProductCode == ProductCodeEnum.Accounts.ToString())
            .SelectMany(x => x.Values ?? [])
            .FirstOrDefault(x => string.Equals(x.Number, ibanAccount, StringComparison.OrdinalIgnoreCase));

        if (availableProduct != null)
        {
            return availableProduct;
        }

        _logger.LogError("Available product not found for account {Account}", ibanAccount);
        throw new ProductNotFoundException(ibanAccount);
    }

    private async Task<List<ProductInfoResponse>> GetLinkedAccounts(string userId)//, string account)
    {
        var request = new CompanyUserGenericRequest(userId).Wrap();
        using var linkedProductsRequest = request.ToHttpRequestMessage(_configuration.GetLinkedProducts);
        var linkedProductsResponse = await SendAsync<LinkedProductsResponse>(linkedProductsRequest);

        //var linkedAccounts = linkedProductsResponse.Products
        //    .Where(x => x.ProductCode == ProductCodeEnum.Accounts.ToString())
        //    .SelectMany(x => x.Values ?? [])
        //    .ToList();
        //var linkedAccount = linkedAccounts.FirstOrDefault(x => string.Equals(x.Number, account, StringComparison.OrdinalIgnoreCase));
        //if (linkedAccount != null)
        //{
        //    _logger.LogError($"Linked product already exists for account {account}");
        //    throw new GenericException();
        //}

        return linkedProductsResponse.Products;
    }

    private async Task<UpdateProductsResponse> UpdateProducts(string userId, CorpUserProductDetails? availableAccount, List<ProductInfoResponse> linkedAccounts)
    {
        var updateProductsReq = new UpdateProductsRequest(userId, availableAccount, linkedAccounts).Wrap();
        using var updateProductsRequest = updateProductsReq.ToHttpRequestMessage(_configuration.UpdateProducts);
        var res = await SendAsync<UpdateProductsResponse>(updateProductsRequest);
        return res;
    }

    #endregion
}
