﻿using System.Text.Json.Serialization;

namespace Nbg.Ewallet.Repository.Types;

[JsonConverter(typeof(JsonStringEnumConverter))]
/// <summary>
/// The status indicates if a transaction has been sent for processing to the core system or is still waiting for approval.
/// Status options: PENDING_APPROVAL, EXECUTED.
/// It is not related to the execution success.
/// </summary>
public enum TransactionStatus
{
    PENDING_APPROVAL,
    EXECUTED,
    REJECTED
}
