﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Wallet Register Request
/// </summary>
public class WalletRegister
{
    /// <summary>
    /// Wallet name to be assigned to the newly created wallet.
    /// Can be used to be searched by other wallet user.
    /// </summary>
    [DataMember(Name = "walletName")]
    public string WalletName { get; set; }
}
