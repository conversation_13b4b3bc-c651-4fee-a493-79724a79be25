using System.Text.Json;
using System.Text.Json.Serialization;
using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using System.Net;
using System.Text;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Base class for all Sandbox API tests providing common functionality
    /// </summary>
    public abstract class SandboxTestBase : IAsyncLifetime
    {
        protected readonly SandboxApiFactory Factory;
        protected readonly HttpClient Client;
        protected readonly JsonSerializerOptions JsonOptions;
        protected readonly ITestOutputHelper Output;

        // Test marker GUID to check if sandbox data is already loaded
        protected static readonly Guid TestMarkerGuid = new Guid("*************-9999-9999-************");

        // Static flag to ensure one-time initialization
        private static bool _sandboxInitialized = false;
        private static readonly object _initLock = new object();

        /// <summary>
        /// Resets the sandbox initialization flag - call this after deleting sandbox
        /// </summary>
        public static void ResetInitializationFlag()
        {
            lock (_initLock)
            {
                _sandboxInitialized = false;
            }
        }

        /// <summary>
        /// Completely resets the sandbox for a fresh test sequence
        /// Deletes the sandbox and resets the initialization flag
        /// </summary>
        public async Task ResetSandboxAsync()
        {
            Output.WriteLine("🔄 Resetting sandbox for fresh test sequence...");

            // Delete the sandbox
            await Factory.DeleteSandboxAsync();

            // Reset the initialization flag so next test will reload data
            ResetInitializationFlag();

            Output.WriteLine("✅ Sandbox reset complete - next test will load fresh data");
        }

        protected SandboxTestBase(ITestOutputHelper output)
        {
            Output = output;
            Factory = new SandboxApiFactory();
            Client = Factory.CreateClient();
            Client.DefaultRequestHeaders.Add("sandboxId", Factory.SandboxId);

            JsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            JsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        /// <summary>
        /// Initialize sandbox data before tests run (only once)
        /// </summary>
        public virtual async Task InitializeAsync()
        {
            // Use lock to ensure thread-safe one-time initialization
            lock (_initLock)
            {
                if (_sandboxInitialized)
                {
                    Output.WriteLine("✅ Sandbox data already initialized, skipping...");
                    return;
                }
            }

            try
            {
                Output.WriteLine("🔍 Checking if sandbox data is already loaded...");

                // Check if test marker data exists in sandbox
                var isDataLoaded = await CheckIfSandboxDataExists();

                if (isDataLoaded)
                {
                    Output.WriteLine("✅ Sandbox data already exists, skipping initialization");
                    lock (_initLock)
                    {
                        _sandboxInitialized = true;
                    }
                    return;
                }

                Output.WriteLine("🔄 Loading sandbox data for the first time...");

                // Load sandbox data (includes test marker)
                await Factory.LoadSandboxDataAsync();

                // Note: Test marker is now included in the sandbox import data
                Output.WriteLine("✅ Test marker included in sandbox data");

                lock (_initLock)
                {
                    _sandboxInitialized = true;
                }

                Output.WriteLine("✅ Sandbox initialization completed successfully");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"❌ Sandbox initialization failed: {ex.Message}");
                throw;
            }
        }

        public virtual Task DisposeAsync()
        {
            Client.Dispose();
            Factory.Dispose();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Helper method to deserialize HTTP response content to specified type
        /// </summary>
        protected async Task<T?> DeserializeResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<T>(content, JsonOptions);
        }

        /// <summary>
        /// Helper method to serialize object to JSON content
        /// </summary>
        protected StringContent SerializeToJsonContent<T>(T obj)
        {
            var json = JsonSerializer.Serialize(obj, JsonOptions);
            return new StringContent(json, Encoding.UTF8, "application/json");
        }

        /// <summary>
        /// Check if sandbox data is already loaded by checking the sandbox directly
        /// </summary>
        private async Task<bool> CheckIfSandboxDataExists()
        {
            try
            {
                Output.WriteLine($"🔍 Checking if sandbox data exists for sandbox ID: {Factory.SandboxId}");

                // Use the proper sandbox endpoint to check if data exists
                var response = await Client.GetAsync($"/sandbox/{Factory.SandboxId}");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Output.WriteLine($"✅ Sandbox found, checking for test marker...");

                    // Check if our test marker GUID exists in the sandbox data
                    var hasTestMarker = responseContent.Contains(TestMarkerGuid.ToString());

                    if (hasTestMarker)
                    {
                        Output.WriteLine($"✅ Test marker found in sandbox: {TestMarkerGuid}");
                        return true;
                    }
                    else
                    {
                        Output.WriteLine($"❌ Test marker not found in sandbox: {TestMarkerGuid}");
                        return false;
                    }
                }
                else if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    Output.WriteLine($"❌ Sandbox not found: {Factory.SandboxId}");
                    return false;
                }
                else
                {
                    Output.WriteLine($"⚠️ Unexpected response when checking sandbox: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Output.WriteLine($"⚠️ Error checking sandbox data existence: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// Create a test marker in the sandbox to indicate data has been loaded
        /// Note: This should be part of the sandbox import process, not a separate API call
        /// </summary>
        private async Task CreateTestMarker()
        {
            try
            {
                Output.WriteLine($"📝 Test marker should be included in sandbox import data: {TestMarkerGuid}");

                // The test marker should be included in the sandbox import data itself
                // rather than created as a separate API call
                // This is just a placeholder to indicate the marker concept

                // In a real implementation, the test marker would be part of the JSON data
                // that gets imported into the sandbox, something like:
                // {
                //   "testMarker": {
                //     "id": "*************-9999-9999-************",
                //     "name": "TEST_MARKER_DO_NOT_DELETE",
                //     "description": "Indicates sandbox data has been loaded",
                //     "createdAt": "2024-01-01T00:00:00Z"
                //   },
                //   "wallets": [...],
                //   "users": [...],
                //   ...
                // }

                Output.WriteLine($"✅ Test marker concept prepared: {TestMarkerGuid}");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"⚠️ Error preparing test marker: {ex.Message}");
            }
        }

        /// <summary>
        /// Helper method to assert successful response and deserialize content
        /// </summary>
        protected async Task<T> AssertSuccessAndDeserialize<T>(HttpResponseMessage response)
        {
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result = await DeserializeResponseAsync<T>(response);
            result.Should().NotBeNull();
            return result!;
        }

        /// <summary>
        /// Helper method to assert error response with specific status code
        /// </summary>
        protected async Task AssertErrorResponse(HttpResponseMessage response, HttpStatusCode expectedStatusCode)
        {
            response.StatusCode.Should().Be(expectedStatusCode);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().NotBeNullOrEmpty();
        }
    }
}
