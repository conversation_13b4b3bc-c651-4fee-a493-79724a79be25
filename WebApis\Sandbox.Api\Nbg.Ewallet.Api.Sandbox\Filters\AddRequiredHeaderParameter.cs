﻿using System;
using System.Text.RegularExpressions;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Nbg.Ewallet.Api.Sandbox;
using Swashbuckle.AspNetCore.SwaggerGen;

public class AddRequiredHeaderParameter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var controllerName = Regex.Replace(context?.MethodInfo.ReflectedType?.Name ?? string.Empty, "[^a-zA-Z_.]+", "", RegexOptions.Compiled);
        if (controllerName.Equals("SandboxController", StringComparison.OrdinalIgnoreCase))
        {
            return;
        }

        operation.Parameters ??= [];

        operation.Parameters.Add(new OpenApiParameter
        {
            Name = "Request-ID",
            In = ParameterLocation.Header,
            Description = "ID of the request, unique to the call, as determined by the initiating party.",
            Required = true,
            Schema = new OpenApiSchema
            {
                Type = "string",
                Example = new OpenApiString("88579F09-1FE5-4286-9DE4-4EE5270A3667")
            }
        });

        operation.Parameters.Add(new OpenApiParameter
        {
            Name = "sandbox-id",
            In = ParameterLocation.Header,
            Description = "The unique id of the sandbox to be used",
            Required = true,
            Schema = new OpenApiSchema
            {
                Type = "string",
                Example = new OpenApiString("my-" + Extensions.ApiName + "-sandbox")
            }
        });
    }
}
