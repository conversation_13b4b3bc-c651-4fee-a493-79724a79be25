{"ConnectionStrings": {"SandboxApps": "Server=S0000A1007,2544;database=SandboxApps;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False"}, "SandboxServiceConfig": {"Sandbox": "SandboxApps", "StorageService": "SQLServer"}, "UrlSettings": {"AuthorizationUrl": "https://my.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope=sandbox-ewallet-api-v1&redirect_uri={{redirect_uri}}&response_type=code"}, "Authentication": {"Authority": "https://my.nbg.gr/identity", "ApiName": "F7C38A41-F73B-4B8F-9813-DCDD33132B95", "ApiSecret": "FD587490-B53B-4602-B42B-DE8094EDF199", "ApiScope": "sandbox-ewallet-api-v1", "ManagementApiScope": "sandbox-ewallet-management"}, "ConsentConnectorSettings": {"ConsentsApiUrl": "https://coreapplayergeneric.nbg.gr/consent.core.api/", "ApplicationConsentTemplateId": "1a20c884-d247-49b8-84ec-fb060c03a60e"}, "ScaClient": {"BaseAddress": "http://localhost/NBG.NetCore.sca/", "DangerousAcceptAnyServerCertificate": true, "ProxyUrl": "", "UseProxy": false, "ProxyUsername": false, "ProxyPassword": false, "ProxyDomain": true, "MaxConnectionsPerServer": 10}, "ScaFilter": {"ChallengeEndpoint": "sca/challenge", "ValidateEndpoint": "sca/validate", "DisableScaWhitelisting": true, "ScaTokenExpirationInSeconds": 300}, "NLog": {"autoReload": true, "throwExceptions": false, "throwConfigExceptions": true, "internalLogLevel": "Off", "internalLogFile": "c:/logs/internal-nlog.txt", "globalThreshold": "Trace", "variables": {"logRootFolder": "c:/logs", "applicationName": "EwalletSandbox"}, "time": {"type": "AccurateUTC"}, "default-wrapper": {"type": "AsyncWrapper", "overflowAction": "Block"}, "targets": {"async": true, "logFile": {"type": "File", "fileName": "${logRootFolder}/nlog-${applicationName}-${shortdate}.log"}, "logEvent": {"type": "EventLog", "log": "${applicationName}", "source": "${applicationName}", "layout": "${callsite:className=true:includeNamespace=true:includeSourcePath=true:methodName=true} ${message}${newline}${exception:format=ToString}"}}, "rules": [{"logger": "Microsoft.*", "maxLevel": "Debug", "writeTo": "logEvent"}, {"logger": "*", "minLevel": "Debug", "writeTo": "logEvent"}]}}