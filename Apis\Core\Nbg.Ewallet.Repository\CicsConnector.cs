﻿using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using nbg.ewallet.repository.types;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.CosmosConnector.Types.Customer;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;
using Nbg.NetCore.CosmosConnector.Types.OpenSBAccount;
using Nbg.NetCore.CosmosConnector.Types.UpdIndicator;
using Nbg.NetCore.Services.Cics.Http;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbusrpr;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraall;
using Nbg.NetCore.Services.Cics.Http.Contract.Jcraupd;
using Nbg.NetCore.Services.Cics.Http.Contract.Jiball;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Repository;

public class CicsConnector : IMainFrameConnector
{
    private readonly ILogger<CicsConnector> _logger;
    private readonly IAuditableCicsConnector _cicsHttpConnector;
    private const string _yes = "Y";

    public CicsConnector(ILogger<CicsConnector> logger, IAuditableCicsConnector cicsHttpConnector)
    {
        _logger = logger;
        _cicsHttpConnector = cicsHttpConnector;
    }

    public async Task<NetCore.CosmosConnector.Types.Customer.Customer> GetCustomerDataAsync(string customerCode)
    {
        var response = await _cicsHttpConnector.Jcraall(new CicsJsonRequest<JcraallRequestPayload> { Payload = _createJcraallRequest(customerCode) });

        if (response.Exception == null)
        {
            return response.Payload.ToCustomer();
        }

        _logger.LogError("CicsJsonRequest Exception in JCRAALL with code {ExceptionCode} and message {ExceptionDescr}",
            response.Exception.Code, response.Exception.Descr);

        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JcraallRequestPayload _createJcraallRequest(string customerCode)
    {
        return new JcraallRequestPayload
        {
            CustomerID = customerCode,
            EnablePerformanceTuning = _yes,
            FindCompanyData = _yes,
            FindLegalDocuments = _yes,
            FindConfirmationDocuments = _yes,
            FindAddresses = _yes,
            FindLogicalAddresses = _yes,
            FindSystemAddresses = _yes,
            FindNames = _yes,
            FindIndications = _yes,
            FindAccounts = _yes,
            FindAccountDetails = _yes,
            FindAccountRelIndications = _yes,
            FindGeneralInformation = _yes,
            FindQuestionnaire = _yes,
            FindUserRelations = _yes
        };
    }

    public async Task<CustomerAuthorizationLevel> GetAuthorizationLevelAsync(string userId)
    {
        var response = await _cicsHttpConnector.JiballAsync(new CicsJsonRequest<JiballRequestPayload> { Payload = _createJiballRequest(userId) });

        if (response.Exception == null)
        {
            CustomerAuthorizationLevel authCustomerData = new() { Approvals = response.Payload.CompUserData.Approvals, Category = response.Payload.CompUserData.Category };
            return authCustomerData;
        }

        _logger.LogError("CicsJsonRequest Exception in JIBALL with code {ExceptionCode} and message {ExceptionDescr}",
            response.Exception.Code, response.Exception.Descr);

        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JiballRequestPayload _createJiballRequest(string userId)
    {
        return new JiballRequestPayload { FindCompUserDetails = _yes, Userid = userId };
    }

    public async Task<string> GetTaxIdFromUserProfileAsync(string userId, string customerCode)
    {
        var payload = CreateJbusrprRequest(userId);
        var response =
            await _cicsHttpConnector.JbusrprAsync(new CicsJsonRequest<JbusrprRequestPayload> { Payload = payload });

        _logger.LogDebug("GetUserProfileAsync response: {Response}", JsonSerializer.Serialize(response));
        if (response.Exception == null) return response.Payload.Common.TaxId;

        _logger.LogError("CicsJsonRequest Exception in JBUSRPR with code {ExceptionCode} and message {ExceptionDescr}",
            response.Exception.Code, response.Exception.Descr);
        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JbusrprRequestPayload CreateJbusrprRequest(string userId)
    {
        return new JbusrprRequestPayload { FetchLogicalAddressSystem = "X", UserId = userId, };
    }

    public async Task<string> GetUserProfileAsync(string userId)
    {
        var payload = _createJbusrprRequest(userId);
        var response = await _cicsHttpConnector.JbusrprAsync(new CicsJsonRequest<JbusrprRequestPayload> { Payload = payload });

        _logger.LogDebug("GetUserProfileAsync response: {Response}", JsonSerializer.Serialize(response));
        if (response.Exception == null)
        {
            return response.Payload.Common.TaxId;
        }

        _logger.LogError("CicsJsonRequest Exception in JBUSRPR with code {ExceptionCode} and message {ExceptionDescr}",
            response.Exception.Code, response.Exception.Descr);

        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    private static JbusrprRequestPayload _createJbusrprRequest(string userId)
    {
        return new JbusrprRequestPayload { FetchLogicalAddressSystem = "X", UserId = userId, };
    }

    private const string _alreadyExistsErrorCode = "040";

    public async Task<UpdIndicator> SubmitCustomerCommissionAsync(string userId, string customerCode, long? indicationKey, long? indicationValue)
    {
        var payload = _createJcraupdRequest(userId, indicationKey, indicationValue);
        var response = await _cicsHttpConnector.JcraupdAsync(new CicsJsonRequest<JcraupdRequestPayload> { Payload = payload });

        var result = response.Payload.ToUpdIndicator();
        _logger.LogDebug("GetUserProfileAsync response: {Response}", JsonSerializer.Serialize(response));

        if (response.Exception != null)
        {
            _logger.LogError("CicsJsonRequest Exception in JCRAUPD with code {ExceptionCode} and message {ExceptionDescr}",
                response.Exception.Code, response.Exception.Descr);

            if (response.Exception.Code == _alreadyExistsErrorCode) return result;

            throw new Exception(response.Exception.Descr);
        }

        if (response.Fault != null)
        {
            _logger.LogError("CicsJsonRequest Exception in JCRAUPD with code {FaultDetail} and message {FaultFaultString}",
                response.Fault.Detail, response.Fault.FaultString);
            throw new Exception(response.Fault.FaultString);
        }

        return result;
    }

    private static JcraupdRequestPayload _createJcraupdRequest(string userId, long? indicationType,
        long? indicationValue)
    {
        var request = new JcraupdRequestPayload
        {
            Userid = userId,
            HandleIndications = _yes,
            UpdUserid = "E99999",
            UpdBranch = "700",
            SubBranch = "4",
            IndicationsData = new IndicationsData
            {
                IndicationType = indicationType,
                IndicationValue = indicationValue,
                EffectiveDate = DateTime.Now.ToString("dd.MM.yyyy"),
                EndDate = string.Empty,
                FlagDelete = string.Empty,
                FlagAuth = string.Empty
            }
        };
        return request;
    }

    public async Task<OpenSBAccount> CreateAccountAsync(string craCode, string userId, string productCode)
    {
        var mJBOPACRequest = new JBOPACRequest(craCode, userId, productCode);
        var response = await _cicsHttpConnector.TryExecutePackage<JBOPACResponse.JBOAccountInfo>("JBOPAC", mJBOPACRequest);

        if (response.Exception != null)
        {
            _logger.LogError("JBOPACRequest Exception with code {ExceptionCode} and message {ExceptionDescr}",
                response.Exception.Code, response.Exception.Descr);
            throw new Exception(response.Exception.Descr);
        }

        if (response.Fault != null)
        {
            _logger.LogError("JBOPACRequest Fault with code {FaultDetail} and message {FaultFaultString}", response.Fault.Detail, response.Fault.FaultString);
            return null;
        }

        return new OpenSBAccount
        {
            AccountDetails = new NetCore.CosmosConnector.Types.OpenSBAccount.AccountDetails
            {
                AccountInfo = new AccountInfo { AccountId = response.Payload.Account },
                Iban = response.Payload.AccountIban
            }
        };
    }

    public async Task<CustomerProductDetails> GetCustomerProductDetailsAsync(string customerCode)
    {
        var response = await _cicsHttpConnector.Jcraall(new CicsJsonRequest<JcraallRequestPayload> { Payload = _createJcraallRequest(customerCode) });

        if (response.Exception == null)
        {
            return response.Payload.Accounts.ToCustomerProductDetails();
        }

        _logger.LogError("CicsJsonRequest Exception in JCRAALL with code {ExceptionCode} and message {ExceptionDescr}",
            response.Exception.Code, response.Exception.Descr);

        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    public Task<string> GetAccountFromIBAN(string iban)
    {
        return Task.FromResult(AccountHelpers.IbanToAccount(iban));
    }

    public Task<string> GetIBANFromAccount(string account)
    {
        return Task.FromResult(AccountHelpers.IbanFromAccount(account, true));
    }
}
