﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Consents;

/// <summary>
/// Consent response
/// </summary>
public class Consent2
{
    /// <summary>
    /// Consent id
    /// </summary>
    [DataMember(Name = "consentId")]
    public Guid consentId { get; set; }

    /// <summary>
    /// Consent Status
    /// </summary>
    [DataMember(Name = "status")]
    public string status { get; set; }

    /// <summary>
    /// Consent authorization url
    /// </summary>
    [DataMember(Name = "authorizationUrl")]
    public string AuthorizationUrl { get; set; }
}

// for later
public enum AuthorizationStatus
{
    AwaitingAuthorization,
    Authorized,
    Rejected
}
