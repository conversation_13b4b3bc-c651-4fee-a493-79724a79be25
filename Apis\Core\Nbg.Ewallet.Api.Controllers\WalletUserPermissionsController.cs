using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Wallet;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// This Controller Provides Wallet User Permissions functionality.
/// </summary>
[ApiController]
[ApiExplorerSettings(GroupName = "Wallet")]
[Route("wallet")]
[Produces("application/json")]
[Consumes("application/json")]
public class WalletUserPermissionsController : ControllerBase
{
    private readonly IWalletUserPermissionsService _walletUserPermissionsService;

    public WalletUserPermissionsController(IWalletUserPermissionsService walletUserPermissionsService)
    {
        _walletUserPermissionsService = walletUserPermissionsService;
    }

    /// <summary>
    /// Grants access to internal company users.
    /// </summary>
    /// <param name="request">The request containing user permissions details.</param>
    /// <returns>A response indicating the result of the operation.</returns>
    /// <response code="200">Permissions granted successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(UserPermissionResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPost]
    [Route("{walletId}/user-permissions", Name = "SetWalletUserPermissions")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<UserPermissionResponse>> SetWalletUserPermissions(SetWalletUserPermissionsRequest request)
    {
        return await _walletUserPermissionsService.SetWalletUserPermissions(request);
    }

    /// <summary>
    /// Retrieves all internal company users' permissions.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="showAll">Whether to show all permissions.</param>
    /// <returns>A list of user permissions.</returns>
    /// <response code="200">Permissions retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(UserPermissionResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    [Route("{walletId}/user-permissions", Name = "GetWalletUserPermissions")]
    public async Task<ActionResult<UserPermissionResponse>> GetWalletUserPermissions(Guid walletId, [FromQuery] bool showAll)
    {
        return await _walletUserPermissionsService.GetWalletUserPermissions(walletId, showAll);
    }

    /// <summary>
    /// Retrieves access rights for a specific internal wallet user.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="userid">The user identifier.</param>
    /// <param name="showAll">Whether to show all permissions, even if the permission is expired.</param>
    /// <returns>A list of permissions for the specified user.</returns>
    /// <response code="200">Permissions retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<UserPermission>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/user-permissions/{userId}", Name = "GetWalletPermissionForUser")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<List<UserPermission>>> GetWalletPermissionForUser(Guid walletId, string userid, [FromQuery] bool showAll)
    {
        return await _walletUserPermissionsService.GetWalletPermissionForUser(walletId, userid, showAll);
    }

    /// <summary>
    /// Revokes access of a specific internal user.
    /// </summary>
    /// <param name="userid">The user identifier.</param>
    /// <returns>The revoked user permission.</returns>
    /// <response code="200">Permission revoked successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(UserPermission), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPut]
    [Route("{walletId}/user-permissions/{userId}/revoke", Name = "RevokeUserPermissionsForUser")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<UserPermission>> RevokeUserPermissionsForUser(string userid)
    {
        return await _walletUserPermissionsService.RevokeUserPermissionsForUser(userid);
    }

    /// <summary>
    /// Retrieves available user to assign user permissions to.
    /// </summary>
    /// <remarks>
    /// Retrieves all company users that are available to be connected to the wallet.
    /// This will only return data for corporate IB accounts.
    /// </remarks>
    /// <param name="walletId">The wallet identifier.</param>
    /// <returns>A response containing available users.</returns>
    /// <response code="200">Users retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(AvailableUserResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/users", Name = "GetOrganizationUsers")]
    [EwalletAuthorize(AuthorizationTypes.Admin)]
    public async Task<ActionResult<AvailableUserResponse>> GetOrganizationUsers(Guid walletId)
    {
        return await _walletUserPermissionsService.GetOrganizationUsers(walletId);
    }
}
