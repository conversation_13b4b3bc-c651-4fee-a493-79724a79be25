{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iis": {"applicationUrl": "https://localhost/Nbg.Ewallet.Api.Sandbox", "sslPort": 443}, "iisExpress": {"applicationUrl": "http://localhost:52269/Nbg.Ewallet.Api.Sandbox", "sslPort": 44384}}, "profiles": {"IIS": {"commandName": "IIS", "launchBrowser": true, "launchUrl": "index.html", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "index.html", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:52269/", "sslPort": 44384}}}