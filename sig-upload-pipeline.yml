trigger:
  - none

# mm HH DD MM DW
#  \  \  \  \  \__ Days of week
#   \  \  \  \____ Months
#    \  \  \______ Days
#     \  \________ Hours
#      \__________ Minutes

schedules:
  - cron: "0 0 * * Mon-Fri"
    displayName: Daily midnight upload to SIG
    always: true
    branches:
      include:
      - master  

resources:
  repositories:
    - repository: devopsTemplates
      type: git
      ref: refs/heads/main
      name: DevOps_Private/pipelines

variables:
  SIG.ArtifactName: 'nbg-ewallet-api'
  SIG.BuildId: $[format('{0:yyyyMMdd}', pipeline.startTime)]
  SIG.ArchiveContents: |
    **
    !**/.git/**
    !**/.git*    
    !**/**.sql
    !**/**.json
    !**/**.pubxml
    !**/**.yaml
    !**/**.yml
    !**/**.dll
    !**/**.bat

pool:
  vmImage: ubuntu-latest

steps: 
- checkout: self
- template: templates/sig-stepsV2.yaml@devopsTemplates