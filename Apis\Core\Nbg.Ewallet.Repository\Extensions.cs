﻿using System;
using System.Threading.Tasks;
using bUtility;
using Nbg.NetCore.Services.Cics.Http;

namespace Nbg.Ewallet.Repository;

public static class Extensions
{
    #region CICSHttpConnector Methods

    /// <summary>
    /// Executes JSON service request. Returns null if CicsHttpConnectorException is thrown or response has Fault field.
    /// The caller of the method must handle response exception
    /// </summary>
    /// <typeparam name="TPayload">type of host Response Payload</typeparam>
    /// <param name="httpConn">ICicsHttpConnector</param>
    /// <param name="programName">the name of the program to execute</param>
    /// <param name="requestPayload">the data payload to send to the host</param>
    /// <returns><see cref="CicsJsonResponse"/>Host Response containing Exception or Payload</returns>
    internal static async Task<CicsJsonResponse<TPayload>> TryExecutePackage<TPayload>(this ICicsHttpConnector httpConn, string programName,
        object requestPayload)
    {
        try
        {
            var hostRequest = new CicsJsonRequest<object>(requestPayload);
            CicsJsonResponse<TPayload> response = await httpConn.ExecuteAsync<object, TPayload>(hostRequest, programName);
            if (response == null)
            {
                bUtility.Logging.Logger.Current?.Warn($"{programName} returned null response");
                return null;
            }

            if (response.Fault?.FaultString.Clear() == null) return response;

            bUtility.Logging.Logger.Current?.Error($"{programName} Fault: {response.Fault.FaultString}");
            throw new Exception(response.Fault.FaultString);

            return response;
        }
        catch (Exception ex)
        {
            return null;
        }
    }

    #endregion CICSHttpConnector Methods
}
