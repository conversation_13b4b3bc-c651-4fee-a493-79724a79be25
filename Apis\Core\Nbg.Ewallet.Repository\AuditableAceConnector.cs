﻿using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.NetCore.CosmosConnector.Implementation;
using Nbg.NetCore.CosmosConnector.Interfaces;
using Nbg.NetCore.CosmosConnector.Types;

namespace Nbg.Ewallet.Repository;

public class AuditableAceConnector : IAuditableAceConnector
{
    private readonly IServiceAuditRepositoryService _serviceAuditRepository;
    private readonly IAceConnector _aceConnector;
    private readonly ILogger<AuditableAceConnector> _logger;
    private readonly bool _isLoggingEnabled;
    private readonly string _aceUrl;

    public AuditableAceConnector(
        IServiceAuditRepositoryService serviceAuditRepository,
        IAceConnector aceConnector,
        IOptions<AceConnectorOptions> aceOptions,
        IOptions<ServiceAuditSettings> auditOptions,
        ILogger<AuditableAceConnector> logger)
    {
        _serviceAuditRepository = serviceAuditRepository;
        _aceConnector = aceConnector;
        _logger = logger;

        _isLoggingEnabled = auditOptions?.Value?.EnableAudit ?? false;
        if (!_isLoggingEnabled)
        {
            _logger.LogInformation("Service Audit is not enabled");
            return;
        }

        var host = aceOptions?.Value?.Host?.Trim('/') ?? "UNKNOWN";
        var port = aceOptions?.Value?.Port ?? 0;
        _aceUrl = port > 0 ? $"{host}:{port}" : host;

        _logger.LogDebug("ACE connector URL will be: {ACEURL}", _aceUrl);
    }

    public Task<AceResponse<TResult>> GetAsync<TRequest, TResult>(AceRequest<TRequest> request, string resource, AceQueryParameters queryParameters) =>
        ExecuteAndLog(() => _aceConnector.GetAsync<TRequest, TResult>(request, resource, queryParameters), request, resource);

    public Task<AceResponse<TResult>> PostAsync<TRequest, TResult>(AceRequest<TRequest> request, string resource) =>
        ExecuteAndLog(() => _aceConnector.PostAsync<TRequest, TResult>(request, resource), request, resource);

    public Task<AceResponse<TResult>> PutAsync<TRequest, TResult>(AceRequest<TRequest> request, string resource, AceQueryParameters queryParameters) =>
        ExecuteAndLog(() => _aceConnector.PutAsync<TRequest, TResult>(request, resource, queryParameters), request, resource);

    private async Task<AceResponse<TResult>> ExecuteAndLog<TRequest, TResult>(Func<Task<AceResponse<TResult>>> func, AceRequest<TRequest> request, string resource)
    {
        if (!_isLoggingEnabled)
            return await func().ConfigureAwait(false);

        var requestString = _serializeObjectSafe(request);
        var audit = _serviceAuditRepository.GetNewServiceAudit(_aceUrl, resource, requestString);

        AceResponse<TResult> result = null;
        try
        {
            result = await func().ConfigureAwait(false);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ACE connector: {Exception}", ex.ToString());
            audit.ErrorData = ex.ToString();
            throw;
        }
        finally
        {
            audit.EndTime = DateTime.Now;
            audit.ExecutionTime = (TimeSpan)(audit.EndTime - audit.Timestamp);

            if (result != null)
            {
                audit.Id = Guid.TryParse(result.Headers.RequestUUID, out var requestId) ? requestId : Guid.NewGuid();
                audit.ClientSession = result.Headers.GlobalUUID;

                if (result.Payload != null)
                    audit.ResponseTextData = _serviceAuditRepository.HideSensitiveProperties(_serializeObjectSafe(result.Payload));

                if (result.ErrorResponse != null)
                    audit.ErrorData = _serviceAuditRepository.HideSensitiveProperties(_serializeObjectSafe(result.ErrorResponse));
            }

            await _serviceAuditRepository.InsertAuditEntryAsync(audit).ConfigureAwait(false);
        }
    }

    private static string _serializeObjectSafe<T>(T obj) => obj == null ? string.Empty : JsonSerializer.Serialize(obj);

    public AceResponse<TResult> Post<TRequest, TResult>(AceRequest<TRequest> request, string resource)
    {
        throw new NotImplementedException();
    }

    public AceResponse<TResult> Get<TRequest, TResult>(AceRequest<TRequest> request, string resource, AceQueryParameters queryParameters)
    {
        throw new NotImplementedException();
    }

    public AceResponse<TResult> Put<TRequest, TResult>(AceRequest<TRequest> request, string resource, AceQueryParameters queryParameters)
    {
        throw new NotImplementedException();
    }
}
