﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface IAuthorizationRepositoryService
{
    Task RepoAuthorizationRequestSaveAsync(RepoAuthorizationRequest authRequest);

    Task<List<RepoAuthorizationRequest>> RepoAuthorizationRequestFindAllByWalletIdAndRequestStatusAsync(Guid walletId, RequestStatus? status);

    Task<List<RepoAuthorizationRequest>> RepoAuthorizationRequestFindAllByRequestorWalletIdAndTargetWalletIdAndStatusAsync(Guid requestorWalletId, Guid targetWalletId, RequestStatus status);

    Task<RepoAuthorizationRequest> RepoAuthorizationRequestFindOneByWalletIdAndAuthorizationRequestId(Guid walletId, Guid requestId);

    Task RepoAuthorizationRequestUpdateAuthorizationRequestAsync(RepoAuthorizationRequest request);
}
