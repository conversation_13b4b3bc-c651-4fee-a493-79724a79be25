﻿using System.Collections.Generic;
using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Types.Subscriptions;

/// <summary>
/// SubscriptionPlan definition
/// </summary>
public class SubscriptionPlan
{
    /// <summary>
    /// Id of the SubscriptionPlan
    /// </summary>
    [DataMember(Name = "id")]
    public SubscriptionTier Id { get; set; }

    /// <summary>
    /// Defined discounts per transaction type
    /// </summary>
    [DataMember(Name = "discounts")]
    public List<DiscountTransaction> discounts { get; set; }
}
