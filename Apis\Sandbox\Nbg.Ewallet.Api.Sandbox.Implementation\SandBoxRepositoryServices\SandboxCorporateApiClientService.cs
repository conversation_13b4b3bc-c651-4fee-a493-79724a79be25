using System.Linq;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxCorporateApiClientService : ICorporateApiClientService
{
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;

    public SandboxCorporateApiClientService(ISandBoxRepositoryService sandBoxRepositoryService)
    {
        _sandBoxRepositoryService = sandBoxRepositoryService;
    }

    public Task<bool> ConnectAccountAsync(string userId, string ibanAccount)
    {
        return Task.FromResult(true);
    }

    public Task<bool> DisconnectAccountAsync(string userId, string ibanAccount)
    {
        return Task.FromResult(true);
    }

    public async Task<CompanyUserProfiles> GetCompanyUsersAsync(string userId)
    {
        var sandboxDataModel = await _sandBoxRepositoryService.GetSandBoxModel();
        var wallet = sandboxDataModel.Wallets.Where(w => w.OwnerUserId == userId).FirstOrDefault();
        if (!sandboxDataModel.AvailableUsers.ContainsKey(wallet.WalletId))
        {
            return new CompanyUserProfiles { UserProfiles = [] };
        }

        var userProfiles = sandboxDataModel.AvailableUsers[wallet.WalletId].Select(au => new CompanyUserProfile { UserId = au.Userid, Alias = au.Alias }).ToList();

        return new CompanyUserProfiles { UserProfiles = userProfiles };
    }
}
