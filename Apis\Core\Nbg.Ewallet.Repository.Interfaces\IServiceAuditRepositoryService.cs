﻿using System.Net.Http;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.Services.Cics.Http;

namespace Nbg.Ewallet.Repository.Interfaces;
public interface IServiceAuditRepositoryService
{
    Task InsertAuditEntryAsync(ServiceAudit auditEntry);
    Task UpdateAuditEntryAsync(ServiceAudit auditEntry);
    Task<ServiceAudit> AuditBeforeExecAsync(HttpRequestMessage requestMsg);
    Task AuditAfterExecAsync(ServiceAudit auditEntry, HttpResponseMessage responseMsg);
    Task<ServiceAudit> AuditCicsBeforeExecAsync<TRequest>(CicsJsonRequest<TRequest> request, string action);
    Task AuditCicsAfterExecAsync<TResponse>(ServiceAudit auditEntry, CicsJsonResponse<TResponse> response);
    ServiceAudit GetNewServiceAudit(string url, string action, string requestString);
    string HideSensitiveProperties(string jsonText);
}
