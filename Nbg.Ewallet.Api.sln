﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34511.84
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Apis", "Apis", "{C047D084-CF21-4BE7-8E48-7CA7C5F94772}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{C9DD425C-145F-4E43-9592-35BBA8CA7F01}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{322A39F9-90A3-4670-8A90-5011C39F16C4}"
	ProjectSection(SolutionItems) = preProject
		DIrectory.Build.props = DIrectory.Build.props
		Solution Items\docker-compose.yml = Solution Items\docker-compose.yml
		Solution Items\EventViewer.ps1 = Solution Items\EventViewer.ps1
		Initial DB scripts.sql = Initial DB scripts.sql
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WebApis", "WebApis", "{E637F927-3C07-46E1-AE7D-DF5D888C31F4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core.Api", "Core.Api", "{1F2CB35A-5220-4E42-93B5-779D99586F37}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sandbox", "Sandbox", "{BF629315-C4EE-4B16-B010-516F916734A8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api", "WebApis\Core.Api\Nbg.Ewallet.Api\Nbg.Ewallet.Api.csproj", "{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sandbox.Api", "Sandbox.Api", "{EB1A27B5-C020-49E3-98D9-EEA7B8F9E51D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Types", "Apis\Core\Nbg.Ewallet.Api.Core.Types\Nbg.Ewallet.Api.Types.csproj", "{FF261D83-7A7F-4A69-B057-E621A28AAC6A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Controllers", "Apis\Core\Nbg.Ewallet.Api.Controllers\Nbg.Ewallet.Api.Controllers.csproj", "{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Interfaces", "Apis\Core\Nbg.Ewallet.Api.Interfaces\Nbg.Ewallet.Api.Interfaces.csproj", "{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Implementation", "Apis\Core\Nbg.Ewallet.Api.Implementation\Nbg.Ewallet.Api.Implementation.csproj", "{DD349571-E018-405C-9BA5-59061B7AAD79}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Sandbox.Controllers", "Apis\Sandbox\Nbg.Ewallet.Api.Controllers\Nbg.Ewallet.Api.Sandbox.Controllers.csproj", "{1081A079-99EF-4A85-9E76-EECEDDEF5B04}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Sandbox.Implementation", "Apis\Sandbox\Nbg.Ewallet.Api.Sandbox.Implementation\Nbg.Ewallet.Api.Sandbox.Implementation.csproj", "{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Sandbox", "WebApis\Sandbox.Api\Nbg.Ewallet.Api.Sandbox\Nbg.Ewallet.Api.Sandbox.csproj", "{426732F0-A529-4CEB-AADD-002F5B9D5098}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docs.Api", "Docs.Api", "{8721731E-5AB7-417A-B27D-C6E80F010099}"
	ProjectSection(SolutionItems) = preProject
		WebApis\Docs.Api\.Sandbox.postman_environment.json = WebApis\Docs.Api\.Sandbox.postman_environment.json
		WebApis\Docs.Api\EWallet.postman_collection.json = WebApis\Docs.Api\EWallet.postman_collection.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docs", "Docs", "{A6142DEC-A79D-498E-8AC1-6FDB1BE5F883}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Diagrams", "Diagrams", "{B7802760-CCD3-4ACC-9B79-73E911D955E9}"
	ProjectSection(SolutionItems) = preProject
		Docs\Diagrams\admin_configureB2BChannels.mermaid = Docs\Diagrams\admin_configureB2BChannels.mermaid
		Docs\Diagrams\admin_configureB2BRules.mermaid = Docs\Diagrams\admin_configureB2BRules.mermaid
		Docs\Diagrams\admin_uploadTerms.mermaid = Docs\Diagrams\admin_uploadTerms.mermaid
		Docs\Diagrams\company_getSubscriptionPlanStatus.mermaid = Docs\Diagrams\company_getSubscriptionPlanStatus.mermaid
		Docs\Diagrams\company_registeredUsers.mermaid = Docs\Diagrams\company_registeredUsers.mermaid
		Docs\Diagrams\company_remove.mermaid = Docs\Diagrams\company_remove.mermaid
		Docs\Diagrams\company_renew.mermaid = Docs\Diagrams\company_renew.mermaid
		Docs\Diagrams\company_terminateSubscriptionPlan.mermaid = Docs\Diagrams\company_terminateSubscriptionPlan.mermaid
		Docs\Diagrams\company_wallet.mermaid = Docs\Diagrams\company_wallet.mermaid
		Docs\Diagrams\consents_authorize.mermaid = Docs\Diagrams\consents_authorize.mermaid
		Docs\Diagrams\consents_create.mermaid = Docs\Diagrams\consents_create.mermaid
		Docs\Diagrams\consents_getConsent.mermaid = Docs\Diagrams\consents_getConsent.mermaid
		Docs\Diagrams\getauthorizationrequests.mermaid = Docs\Diagrams\getauthorizationrequests.mermaid
		Docs\Diagrams\home_getprofile.mermaid = Docs\Diagrams\home_getprofile.mermaid
		Docs\Diagrams\requestAuthorizations.mermaid = Docs\Diagrams\requestAuthorizations.mermaid
		Docs\Diagrams\subscription_getSubscriptionPlans.mermaid = Docs\Diagrams\subscription_getSubscriptionPlans.mermaid
		Docs\Diagrams\terms_getTerms.mermaid = Docs\Diagrams\terms_getTerms.mermaid
		Docs\Diagrams\usecase_registration.mermaid = Docs\Diagrams\usecase_registration.mermaid
		Docs\Diagrams\userPermission_revoke.mermaid = Docs\Diagrams\userPermission_revoke.mermaid
		Docs\Diagrams\user_add.mermaid = Docs\Diagrams\user_add.mermaid
		Docs\Diagrams\user_getLimits.mermaid = Docs\Diagrams\user_getLimits.mermaid
		Docs\Diagrams\user_getRights.mermaid = Docs\Diagrams\user_getRights.mermaid
		Docs\Diagrams\user_getUser.mermaid = Docs\Diagrams\user_getUser.mermaid
		Docs\Diagrams\user_getWallets.mermaid = Docs\Diagrams\user_getWallets.mermaid
		Docs\Diagrams\user_limits.mermaid = Docs\Diagrams\user_limits.mermaid
		Docs\Diagrams\user_remove.mermaid = Docs\Diagrams\user_remove.mermaid
		Docs\Diagrams\user_rights.mermaid = Docs\Diagrams\user_rights.mermaid
		Docs\Diagrams\user_update.mermaid = Docs\Diagrams\user_update.mermaid
		Docs\Diagrams\walletPermission_revoke.mermaid = Docs\Diagrams\walletPermission_revoke.mermaid
		Docs\Diagrams\wallet_account.mermaid = Docs\Diagrams\wallet_account.mermaid
		Docs\Diagrams\wallet_createsubscription.mermaid = Docs\Diagrams\wallet_createsubscription.mermaid
		wallet_getsubscription.mermaid = wallet_getsubscription.mermaid
		Docs\Diagrams\wallet_info.mermaid = Docs\Diagrams\wallet_info.mermaid
		Docs\Diagrams\wallet_load.mermaid = Docs\Diagrams\wallet_load.mermaid
		Docs\Diagrams\wallet_register.mermaid = Docs\Diagrams\wallet_register.mermaid
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Proxy.Api", "Proxy.Api", "{AB635B0A-E044-4DAE-BF1B-EF65E88007D4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Proxy.Api", "WebApis\Proxy.Api\Nbg.Ewallet.Proxy.Api\Nbg.Ewallet.Proxy.Api.csproj", "{21080C64-2F48-49E4-9424-42AB4EADF48C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Repository", "Apis\Core\Nbg.Ewallet.Repository\Nbg.Ewallet.Repository.csproj", "{DBDF8435-C693-4155-A186-9444231440B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Repository.Types", "Apis\Core\Nbg.Ewallet.Repository.Types\Nbg.Ewallet.Repository.Types.csproj", "{356390C6-EEDB-4132-B850-5B49B411E8B3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Postman", "Postman", "{5A84FA85-7D9D-4140-80D3-10A07328A8AA}"
	ProjectSection(SolutionItems) = preProject
		Docs\Postman\e-wallet Proxy - Dev.postman_environment.json = Docs\Postman\e-wallet Proxy - Dev.postman_environment.json
		Docs\Postman\eWallet API.postman_collection.json = Docs\Postman\eWallet API.postman_collection.json
		Docs\Postman\eWallet API.postman_environment.json = Docs\Postman\eWallet API.postman_environment.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "sql", "sql", "{4312C906-43EF-4768-8007-3C56E69AFA10}"
	ProjectSection(SolutionItems) = preProject
		auxiliary_scripts.sql = auxiliary_scripts.sql
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "migrations", "migrations", "{9F615454-E822-40E8-A722-F01F84E5D643}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SpecFlow", "SpecFlow", "{E20011CC-7A5A-49AB-BAE9-2DA790211823}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EWallet.Tests", "EWallet.Tests\EWallet.Tests.csproj", "{C63212AC-1163-4434-B0D3-B4D5B8B016B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nbg.Ewallet.Api.Sandbox.Types", "Apis\Sandbox\Nbg.Ewallet.Api.Sandbox.Types\Nbg.Ewallet.Api.Sandbox.Types.csproj", "{58F47583-4560-416C-A513-521B494A1F36}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Nbg.Ewallet.Repository.Test", "Nbg.Ewallet.Repository.Test\Nbg.Ewallet.Repository.Test.csproj", "{B241BEF0-D584-4794-9951-796D2488552B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Nbg.Ewallet.Repository.Interfaces", "Apis\Core\Nbg.Ewallet.Repository.Interfaces\Nbg.Ewallet.Repository.Interfaces.csproj", "{12ABC47D-99C4-4295-9BC3-0CED774B2792}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sandbox.Api.Test", "Sandbox.Api.Test", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SandboxApiTest", "WebApis\Sandbox.Api.Test\SandboxApiTest\SandboxApiTest.csproj", "{7A692EE6-0179-5A36-D46A-68A648266698}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Test", "Test", "{3C88983A-7878-4102-B1CF-A557A59BD1C3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SandboxTest", "SandboxTest\SandboxTest.csproj", "{F7D80D1A-0410-42F6-677F-11BBB0487145}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		DeveloperPortal|Any CPU = DeveloperPortal|Any CPU
		Development|Any CPU = Development|Any CPU
		Production|Any CPU = Production|Any CPU
		QAIDS|Any CPU = QAIDS|Any CPU
		Release|Any CPU = Release|Any CPU
		Sandbox|Any CPU = Sandbox|Any CPU
		UAT|Any CPU = UAT|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.DeveloperPortal|Any CPU.ActiveCfg = Development|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.DeveloperPortal|Any CPU.Build.0 = Development|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Development|Any CPU.Build.0 = Development|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Production|Any CPU.Build.0 = Production|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Release|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Release|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.DeveloperPortal|Any CPU.ActiveCfg = Production|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.DeveloperPortal|Any CPU.Build.0 = Production|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Development|Any CPU.Build.0 = Development|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Production|Any CPU.Build.0 = Production|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Release|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Release|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.DeveloperPortal|Any CPU.ActiveCfg = Production|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.DeveloperPortal|Any CPU.Build.0 = Production|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Development|Any CPU.Build.0 = Development|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Production|Any CPU.Build.0 = Production|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Release|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Release|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.DeveloperPortal|Any CPU.ActiveCfg = Production|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.DeveloperPortal|Any CPU.Build.0 = Production|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Development|Any CPU.Build.0 = Development|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Production|Any CPU.Build.0 = Production|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Release|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Release|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.DeveloperPortal|Any CPU.ActiveCfg = Production|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.DeveloperPortal|Any CPU.Build.0 = Production|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Development|Any CPU.Build.0 = Development|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Production|Any CPU.Build.0 = Production|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Release|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Release|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{DD349571-E018-405C-9BA5-59061B7AAD79}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.DeveloperPortal|Any CPU.ActiveCfg = Production|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.DeveloperPortal|Any CPU.Build.0 = Production|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Development|Any CPU.Build.0 = Development|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Production|Any CPU.Build.0 = Production|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Release|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Release|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.DeveloperPortal|Any CPU.ActiveCfg = Production|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.DeveloperPortal|Any CPU.Build.0 = Production|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Development|Any CPU.Build.0 = Development|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Production|Any CPU.Build.0 = Production|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Release|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Release|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.DeveloperPortal|Any CPU.ActiveCfg = DeveloperPortal|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.DeveloperPortal|Any CPU.Build.0 = DeveloperPortal|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Development|Any CPU.Build.0 = Development|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Production|Any CPU.Build.0 = Production|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.QAIDS|Any CPU.ActiveCfg = QAIDS|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.QAIDS|Any CPU.Build.0 = QAIDS|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Release|Any CPU.ActiveCfg = UAT|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Release|Any CPU.Build.0 = UAT|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Sandbox|Any CPU.ActiveCfg = Development|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.Sandbox|Any CPU.Build.0 = Development|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{426732F0-A529-4CEB-AADD-002F5B9D5098}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.DeveloperPortal|Any CPU.ActiveCfg = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.DeveloperPortal|Any CPU.Build.0 = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Development|Any CPU.Build.0 = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Production|Any CPU.ActiveCfg = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Production|Any CPU.Build.0 = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Release|Any CPU.Build.0 = Release|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{21080C64-2F48-49E4-9424-42AB4EADF48C}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.DeveloperPortal|Any CPU.ActiveCfg = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.DeveloperPortal|Any CPU.Build.0 = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Development|Any CPU.Build.0 = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Production|Any CPU.ActiveCfg = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Production|Any CPU.Build.0 = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Sandbox|Any CPU.ActiveCfg = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.Sandbox|Any CPU.Build.0 = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{DBDF8435-C693-4155-A186-9444231440B7}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.DeveloperPortal|Any CPU.ActiveCfg = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.DeveloperPortal|Any CPU.Build.0 = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Development|Any CPU.Build.0 = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Production|Any CPU.ActiveCfg = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Production|Any CPU.Build.0 = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Release|Any CPU.Build.0 = Release|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Sandbox|Any CPU.ActiveCfg = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.Sandbox|Any CPU.Build.0 = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{356390C6-EEDB-4132-B850-5B49B411E8B3}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.DeveloperPortal|Any CPU.ActiveCfg = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.DeveloperPortal|Any CPU.Build.0 = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Development|Any CPU.Build.0 = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Production|Any CPU.ActiveCfg = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Production|Any CPU.Build.0 = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Sandbox|Any CPU.ActiveCfg = Release|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.Sandbox|Any CPU.Build.0 = Release|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.DeveloperPortal|Any CPU.ActiveCfg = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.DeveloperPortal|Any CPU.Build.0 = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Development|Any CPU.Build.0 = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Production|Any CPU.ActiveCfg = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Production|Any CPU.Build.0 = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Release|Any CPU.Build.0 = Release|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Sandbox|Any CPU.ActiveCfg = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.Sandbox|Any CPU.Build.0 = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{58F47583-4560-416C-A513-521B494A1F36}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.DeveloperPortal|Any CPU.ActiveCfg = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.DeveloperPortal|Any CPU.Build.0 = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Development|Any CPU.Build.0 = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Production|Any CPU.ActiveCfg = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Production|Any CPU.Build.0 = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Release|Any CPU.Build.0 = Release|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Sandbox|Any CPU.ActiveCfg = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.Sandbox|Any CPU.Build.0 = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{B241BEF0-D584-4794-9951-796D2488552B}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.DeveloperPortal|Any CPU.ActiveCfg = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.DeveloperPortal|Any CPU.Build.0 = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Development|Any CPU.Build.0 = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Production|Any CPU.ActiveCfg = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Production|Any CPU.Build.0 = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Release|Any CPU.Build.0 = Release|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Sandbox|Any CPU.ActiveCfg = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.Sandbox|Any CPU.Build.0 = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{12ABC47D-99C4-4295-9BC3-0CED774B2792}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.DeveloperPortal|Any CPU.ActiveCfg = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.DeveloperPortal|Any CPU.Build.0 = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Development|Any CPU.ActiveCfg = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Development|Any CPU.Build.0 = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Production|Any CPU.ActiveCfg = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Production|Any CPU.Build.0 = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Sandbox|Any CPU.ActiveCfg = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.Sandbox|Any CPU.Build.0 = Release|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{7A692EE6-0179-5A36-D46A-68A648266698}.UAT|Any CPU.Build.0 = Debug|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.DeveloperPortal|Any CPU.ActiveCfg = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.DeveloperPortal|Any CPU.Build.0 = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Development|Any CPU.ActiveCfg = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Development|Any CPU.Build.0 = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Production|Any CPU.ActiveCfg = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Production|Any CPU.Build.0 = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.QAIDS|Any CPU.ActiveCfg = Debug|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.QAIDS|Any CPU.Build.0 = Debug|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Sandbox|Any CPU.ActiveCfg = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.Sandbox|Any CPU.Build.0 = Release|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.UAT|Any CPU.ActiveCfg = Debug|Any CPU
		{F7D80D1A-0410-42F6-677F-11BBB0487145}.UAT|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C9DD425C-145F-4E43-9592-35BBA8CA7F01} = {C047D084-CF21-4BE7-8E48-7CA7C5F94772}
		{1F2CB35A-5220-4E42-93B5-779D99586F37} = {E637F927-3C07-46E1-AE7D-DF5D888C31F4}
		{BF629315-C4EE-4B16-B010-516F916734A8} = {C047D084-CF21-4BE7-8E48-7CA7C5F94772}
		{84C3D3F5-88CD-49CA-B903-F1872DDFCFF8} = {1F2CB35A-5220-4E42-93B5-779D99586F37}
		{EB1A27B5-C020-49E3-98D9-EEA7B8F9E51D} = {E637F927-3C07-46E1-AE7D-DF5D888C31F4}
		{FF261D83-7A7F-4A69-B057-E621A28AAC6A} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{AC6D837A-5D9E-41A1-9A88-F02CFAEB7B16} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{695FEDF9-DD3A-4EA7-87F3-20EBA4720DB9} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{DD349571-E018-405C-9BA5-59061B7AAD79} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{1081A079-99EF-4A85-9E76-EECEDDEF5B04} = {BF629315-C4EE-4B16-B010-516F916734A8}
		{0F0C39A3-6F79-4234-B89F-39E8E7E4755C} = {BF629315-C4EE-4B16-B010-516F916734A8}
		{426732F0-A529-4CEB-AADD-002F5B9D5098} = {EB1A27B5-C020-49E3-98D9-EEA7B8F9E51D}
		{8721731E-5AB7-417A-B27D-C6E80F010099} = {E637F927-3C07-46E1-AE7D-DF5D888C31F4}
		{B7802760-CCD3-4ACC-9B79-73E911D955E9} = {A6142DEC-A79D-498E-8AC1-6FDB1BE5F883}
		{AB635B0A-E044-4DAE-BF1B-EF65E88007D4} = {E637F927-3C07-46E1-AE7D-DF5D888C31F4}
		{21080C64-2F48-49E4-9424-42AB4EADF48C} = {AB635B0A-E044-4DAE-BF1B-EF65E88007D4}
		{DBDF8435-C693-4155-A186-9444231440B7} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{356390C6-EEDB-4132-B850-5B49B411E8B3} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{5A84FA85-7D9D-4140-80D3-10A07328A8AA} = {A6142DEC-A79D-498E-8AC1-6FDB1BE5F883}
		{4312C906-43EF-4768-8007-3C56E69AFA10} = {322A39F9-90A3-4670-8A90-5011C39F16C4}
		{9F615454-E822-40E8-A722-F01F84E5D643} = {4312C906-43EF-4768-8007-3C56E69AFA10}
		{E20011CC-7A5A-49AB-BAE9-2DA790211823} = {E637F927-3C07-46E1-AE7D-DF5D888C31F4}
		{C63212AC-1163-4434-B0D3-B4D5B8B016B7} = {E20011CC-7A5A-49AB-BAE9-2DA790211823}
		{58F47583-4560-416C-A513-521B494A1F36} = {BF629315-C4EE-4B16-B010-516F916734A8}
		{B241BEF0-D584-4794-9951-796D2488552B} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{12ABC47D-99C4-4295-9BC3-0CED774B2792} = {C9DD425C-145F-4E43-9592-35BBA8CA7F01}
		{02EA681E-C7D8-13C7-8484-4AC65E1B71E8} = {E637F927-3C07-46E1-AE7D-DF5D888C31F4}
		{7A692EE6-0179-5A36-D46A-68A648266698} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F7D80D1A-0410-42F6-677F-11BBB0487145} = {3C88983A-7878-4102-B1CF-A557A59BD1C3}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E30B854B-9B0F-4CAE-A6CA-3B75BA9D917E}
	EndGlobalSection
EndGlobal
