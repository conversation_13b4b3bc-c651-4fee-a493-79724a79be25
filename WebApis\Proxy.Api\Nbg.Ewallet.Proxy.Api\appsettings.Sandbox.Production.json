{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },
    "SandboxServiceConfig": {
        "Sandbox": "SandboxApps",
        "StorageService": "SQLServer"
    },

    "ProxySettings": {
        "SkipAddSqlServer":  true
    },

    "ConnectionStrings": {
        "Audit": "Server=S0000A1007,2544;database=SandboxApps;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "IBank": "Server=S0000A1007,2544;database=SandboxApps;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "uniqueMessage": "Server=S0000A1700,2544;initial catalog=BigDataIbank;Integrated Security=SSPI;Persist Security Info=False;Connection Timeout=3;Encrypt=false",
        "appSettings": "Server=IBPrdSqlSrv,2544;database=APIConfig;Integrated Security=SSPI;Persist Security Info=False;Encrypt=false"
    },

    "AuditMiddleware": {
        "ConnectionName": "Audit",
        "UseSqlServer": true,
        "UseBigDataClient": false,
        "SetPrimarySqlServer": true,
        "SetPrimaryBigDataClient": false,
        "RequestIdHeaderName": "request-id",
        "ServiceNamePrefix": "nbg.ewallet.sandbox.api/v1",
        "IgnoredPaths": [
            "/favicon.ico"
        ],
        "RequestHttpHeaders": [
            "ByPassHeaders"
        ],
        "ResponseHttpHeaders": [
            "ByPassHeaders"
        ],
        "SensitiveProperties": []
    },

    //"MultiSinkAuditWriter": {
    //    "primarySink": "kafka",
    //    "sinks": {
    //        "kafka": {
    //            "kafkaClient": {
    //                "kafkaTopic": "prod-trlog-ibank-ingestion",
    //                "fallbackSqlConnectionString": "data source=IBPrdSqlSrv,2544;initial catalog=BigDataIbank;Integrated Security=SSPI;Persist Security Info=False;Connection Timeout=3;Encrypt=false",
    //                "producerOptions": {
    //                    "bootstrap.servers": "lkc-d9z3w7-p8xdjo.westeurope.azure.glb.confluent.cloud",
    //                    "security.protocol": "SASL_SSL",
    //                    "sasl.username": "B4R75XEVQSJROMQQ",
    //                    "sasl.mechanism": "PLAIN",
    //                    "delivery.timeout.ms": "10000",
    //                    "batch.size": "********",
    //                    "message.max.bytes": "********",
    //                    "acks": "1",
    //                    "compression.type": "snappy",
    //                    "linger.ms": "50"
    //                }
    //            }
    //        }
    //    }
    //},

    "Authentication": {
        "Authority": "https://my.nbg.gr/identity",
        "ApiName": "F7C38A41-F73B-4B8F-9813-DCDD33132B95",
        "ApiSecret": "FD587490-B53B-4602-B42B-DE8094EDF199",
        "RequiredScope": "sandbox-ewallet-api-v1",
        "ManagementApiScope": "ewallet-management"
    },

    "ProxyMiddleware": {
        "UniqueMessageId": {
            "Enabled": false
        },
        "Variables": {
            "CoreServiceUrl": "https://localhost/Nbg.Ewallet.Api.Sandbox"
        }
    },

    "Healthcheck": {
        "CheckAllocatedMemory": true,
        "CheckUris": true,
        "UriCheckOptions": [
            {
                "Uri": "https://localhost/Nbg.Ewallet.Api.Sandbox/index.html",
                "HttpClientName": "default",
                "HttpMethod": "GET",
                "TimeOutMilliseconds": 100000,
                "ExpectedCodes": [ 200 ]
            }
        ]
    },

    "CorporateUserAuthorizationPolicy": {
        "ApplyPolicy": false
    },

    "Sca": {
        "BaseAddress": "http://localhost",
        "ChallengeEndpoint": "/Nbg.NetCore.Sca/sca/challenge",
        "ValidateEndpoint": "/Nbg.NetCore.Sca/sca/validate",
        "DisableScaWhitelisting": false,
        "ScaTokenExpirationInSeconds": 300
    },

    "CustomRestSca": {
        "BaseAddress": "http://localhost/",
        "ChallengeEndpoint": "sca/challenge",
        "ValidateEndpoint": "sca/validate",
        "DisableScaWhitelisting": false,
        "ScaTokenExpirationInSeconds": 120,
        "ApplicationId": "07255F0F-5BCC-4254-90B6-75E70A487333"
    },

    "HttpClient": {
        "ClientRestSca": {
            "BaseAddress": "http://localhost/Nbg.NetCore.Sca/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": false,
            "Timeout": "00:03:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        }
    },

    "SmsOtpSettings": {
        "EnableSmsOtp": true,
        "UseSandboxSca": false
    },
    "UserIdRequestTransformer": {
        "HttpHeadersMappings": {
            "UserId": {
                "SourcePath": "preferred_username",
                "ActionToPerform": "Create",
                "ReplaceIfExists": true,
                "Source": "Claim",
                "ThrowIfNotFoundFromSource": true
            }
        }
    }

}
