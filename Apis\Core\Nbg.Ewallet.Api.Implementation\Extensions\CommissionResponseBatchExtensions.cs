﻿using ibank.ThirdParty.Types;
using Nbg.Ewallet.Api.Types.Payments;

namespace Nbg.Ewallet.Api.Implementation.Extensions;
public static class CommissionResponseBatchExtensions
{
    public static CommissionResponseBatch Add(this CommissionResponseBatch @this, CommissionResponseBatch other)
    {
        if (@this == null) return other;
        if (other == null) return @this;

        if (@this.CommissionInfo == null)
            @this.CommissionInfo = other.CommissionInfo;
        else if (other.CommissionInfo != null)
            @this.CommissionInfo.Add(other.CommissionInfo);

        return @this;
    }

    public static CommissionInfo Add(this CommissionInfo @this, CommissionInfo other)
    {
        if (@this == null) return other;
        if (other == null) return @this;

        @this.Total += other.Total;
        @this.Nbg += other.Nbg;
        @this.Merchant += other.Merchant;
        @this.ThirdParty += other.ThirdParty;
        @this.SubAgent += other.SubAgent;
        @this.NbgOrg += other.NbgOrg;

        return @this;
    }
}
