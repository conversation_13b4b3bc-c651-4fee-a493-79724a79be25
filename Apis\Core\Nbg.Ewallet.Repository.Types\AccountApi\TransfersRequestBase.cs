﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;
/// <summary>
/// TransferRequest definition
/// </summary>
public class TransfersRequestBase
{
    /// <summary>
    /// Username of the logged in user
    /// </summary>
    [Required]
    [DataMember(Name = "userId")]
    public string UserID { get; set; }

    #region Debit account Info

    /// <summary>The NBG account number (e.g. 11 digits) to debit (transfer from) </summary>
    [DataMember(Name = "debitAccount")]
    [Required]
    public string DebitAccount { get => _debitAccount; set => _debitAccount = value.Trim(); }
    private string _debitAccount;

    /// <summary>The Debtor Name to debit (transfer from) </summary>
    [DataMember(Name = "debtorName")]
    public string DebtorName { get; set; }

    /// <summary>Currency, 3-letter code, e.g. <code>EUR</code> (can be empty if debitAccount is IBAN)</summary>
    [DataMember(Name = "debitAccountCurrency")]
    public string DebitAccountCurrency { get; set; }

    #endregion

    #region Receiver Info

    /// <summary>The client designates the desired type of transfer.
    ///Για την τιμή OWN γίνεται επαλήθευση οτι ο λογαριασμός πίστωσης ανήκει στον χρήστη
    /// </summary>
    [DataMember(Name = "transferType")]
    public TransferType? TransferType { get; set; }

    /// <summary>Receiver account
    /// Λογαριασμός παραλήπτη (σε μορφή ΙΒΑΝ/ΤΕΧΤ)</summary>
    [DataMember(Name = "receiverAcc")]
    [Required]
    public string ReceiverAcc { get; set; }

    /// <summary>(IBAN/TEXT)Τύπος μεταφοράς, με χρήση IBAN ή free Text.
    /// Αν μείνει κενό θα πάρει τιμή ανάλογα με τον τύπο του receiverAcc</summary>
    [DataMember(Name = "receiverAccType")]
    public ReceiverAccType? ReceiverAccType { get; set; }

    /// <summary>Beneficiary BIC.</summary>
    /// <summary>Bic τράπεζας παραλήπτη. Μπορεί να μείνει κενό αν το receiverAcc είναι σε μορφή IBAN</summary>
    [DataMember(Name = "beneficiaryBic")]
    [Required]
    public string BeneficiaryBic { get; set; }

    /// <summary>Receiver</summary>
    /// <summary>Παραλήπτης. Το πεδίο πρέπει να συμπληρωθεί (με κεφαλαίους χαρακτήρες) όταν γίνεται μεταφορά εκτός Εθνικής </summary>
    [DataMember(Name = "receiverGR")]
    public string ReceiverGR { get; set; }

    /// <summary>Receiver address</summary>
    /// <summary>Διεύθυνση παραλήπτη - Οδός και αριθμός - Απαιτείται μόνο για μεταφορές εκτός ΕΤΕ </summary>
    [DataMember(Name = "receiverAddrGR")]
    public string ReceiverAddrGR { get; set; }

    /// <summary>Πολη παραλήπτη - Απαιτείται μόνο για μεταφορές εκτός ΕΤΕ </summary>
    [DataMember(Name = "receiverCityGR")]
    public string ReceiverCityGR { get; set; }

    #endregion

    #region Foreign Transfer Wihtout IBAN

    /// <summary>Clearance system (values from table, null for NO CLEARANCE SYSTEM)</summary>
    /// <summary>ΣΥΣΤ. ΕΚΚΑΘ. (τιμές από πίνακα, κενό για ΧΩΡΙΣ ΣΥΣΤΗΜΑ ΕΚΚΑΘΑΡΙΣΗΣ)</summary>
    [DataMember(Name = "systType")]
    public string SystType { get; set; }

    /// <summary>Clearance system code</summary>
    /// <summary>ΚΩΔΙΚΟΣ ειδ. συστ. εκκαθάρισης</summary>
    [DataMember(Name = "systCode")]
    public string SystCode { get; set; }

    /// <summary>Clearance system BIC</summary>
    /// <summary>BIC για ειδ. συστ. εκκαθάρισης</summary>
    [DataMember(Name = "systBic")]
    public string SystBic { get; set; }

    /// <summary>Clearance system destination country</summary>
    /// <summary>ΧΩΡΑ ΠΛΗΡΩΜΗΣ για ειδ. συστ. εκκαθάρισης</summary>
    [DataMember(Name = "systCountry")]
    public string SystCountry { get; set; }

    #endregion

    #region Amount & Expenses

    /// <summary>Transfered amount</summary>
    [DataMember(Name = "amount")]
    [Required]
    public decimal Amount { get; set; }

    /// <summary>Currency, 3-letter code, e.g. <code>EUR</code> </summary>
    [DataMember(Name = "currency")]
    [Required]
    public string Currency { get; set; }

    /// <summary>Who pays the transaction commission </summary>
    /// <summary>Επιμερισμός Χρεώσεων SHA / BEN / OUR.
    /// Το πεδίο πρέπει να συμπληρωθεί για μεταφορές ΕΚΤΟΣ Εθνικής</summary>
    [DataMember(Name = "expenses")]
    public ExpensesType? Expenses { get; set; }

    #endregion

    #region Other Transfer Info

    /// <summary>ΕΠΕΙΓΟΥΣΑ ΑΠΟΣΤΟΛΗ</summary>
    [DataMember(Name = "emergency")]
    public bool Emergency { get; set; }

    /// <summary>Online send for domestic banks </summary>
    /// <summary>Αποστολή άμεσα σε τράπεζα εσωτερικού ισχυει για  ATTIGRAA, CRBAGRAA, ERBKGRAA, PIRBGRAA</summary>
    [DataMember(Name = "onlineSend")]
    public bool? OnlineSend { get; set; }

    /// <summary> Για SEPA instant payments. </summary>
    [DataMember(Name = "instantSend")]
    public bool? InstantSend { get; set; }

    /// <summary>Reason (Uppercase up to 140 characters)</summary>
    [DataMember(Name = "reason")]
    [Required]
    public string Reason { get; set; }

    #endregion

    #region Other Optional Fields

    /// <summary>OTP for the transaction </summary>
    [DataMember(IsRequired = false, Name = "tanNumber")]
    public string TanNumber { get; set; }

    /// <summary>True when OTP is required</summary>
    [DataMember(Name = "isSmsOtp")]
    public bool? IsSmsOtp { get; set; }

    /// <summary>Remittance type (from table)</summary>
    /// <summary>ΣΚΟΠΟΣ ΕΜΒ. (από πίνακα)</summary>
    [DataMember(Name = "remType")]
    public string RemType { get; set; }

    /// <summary>Remittance category </summary>
    /// <summary>ΕΙΔΟΣ ΕΜΒΑΣΜΑΤΟΣ. </summary>
    [DataMember(Name = "remCategory")]
    public string RemCategory { get; set; }

    /// <summary>Imported good type (from table)</summary>
    [DataMember(Name = "remImportedGood")]
    public CodeDescription RemImportedGood { get; set; }

    /// <summary>Approval Id</summary>
    [DataMember(Name = "approvalId")]
    public string ApprovalId { get; set; }

    /// <summary>Remittance execution type (from table)</summary>
    [DataMember(Name = "foreignExecType")]
    public string ForeignExecType { get; set; }

    /// <summary>Sender reference number</summary>
    /// <summary>ΚΩΔΙΚΟΣ ΑΝΑΦΟΡΑΣ ΕΝΤΟΛΕΑ</summary>
    [DataMember(Name = "senderRefNum")]
    public string SenderRefNum { get; set; }

    /// <summary>Seller ID</summary>
    [DataMember(Name = "sellerID")]
    public string SellerID { get; set; }

    /// <summary>Seller name</summary>
    [DataMember(Name = "sellerTradingName")]
    public string SellerTradingName { get; set; }

    /// <summary>Seller sub Id</summary>
    [DataMember(Name = "sellerSubID")]
    public string SellerSubID { get; set; }

    /// <summary>AuxInfo</summary>
    [DataMember(Name = "auxInfo")]
    public string AuxInfo { get; set; }

    /// <summary>MassFlag </summary>
    [DataMember(Name = "massFlag")]
    public string MassFlag { get; set; }

    #endregion

    /// <summary>Destination country of transaction </summary>
    [DataMember(Name = "destCountry")]
    public string DestCountry { get; set; }

    /// <summary>
    /// Is sms otp free
    /// </summary>
    public bool IsSmsOtpFree { get; set; }

    [DataMember(Name = "allowDuplicate")]
    public bool? AllowDuplicate { get; set; } // = true;

    // <summary>Το ΣΥΔΙΠΕΛ του χρήστη, για το fraud detection.</summary>
    [DataMember(Name = "sydipel")]
    public string SYDIPEL { get; set; }

    [DataMember(Name = "isCorporateUser")]
    public bool? IsCorporateUser { get; set; }
}

[DataContract]
public class CodeDescription
{
    /// <summary> Code </summary>
    [DataMember(Name = "code")]
    public string Code { get; set; }

    /// <summary>Code's description</summary>
    [DataMember(Name = "description")]
    public string Description { get; set; }
}

[DataContract]
public enum TransferType
{
    [EnumMember]
    OWN,

    [EnumMember]
    NBG,

    [EnumMember]
    OTHER,

    [EnumMember]
    UNKNOWN
}

[DataContract]
public enum ReceiverAccType
{
    [EnumMember]
    IBAN,

    [EnumMember]
    TEXT
}

[DataContract]
public enum ExpensesType
{
    [EnumMember]
    SHA = 1,

    [EnumMember]
    BEN = 2,

    [EnumMember]
    OUR = 3
}
