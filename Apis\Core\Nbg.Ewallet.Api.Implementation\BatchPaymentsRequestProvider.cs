﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Payments;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Records;
using TransactionStatus = Nbg.Ewallet.Repository.Types.TransactionStatus;

namespace Nbg.Ewallet.Api.Implementation;

public class BatchPaymentsRequestProvider : PayTransBase, IBatchPaymentsRequestProvider
{
    private readonly IMapper _mapper;
    private readonly ICustomerCommissionService _customerCommissionService;
    private readonly ITransactionRepositoryService _transactionRepository;
    private readonly ITPPApiClientService _tppApiClientService;
    private readonly IAccountsApiClientService _accountsApiClientService;
    private readonly IIbanAccountUtility _ibanAccountUtility;
    private readonly ILogger<BatchPaymentsRequestProvider> _logger;

    public BatchPaymentsRequestProvider(
        IMapper mapper,
        IHttpContextRepositoryService httpContextService,
        IAuthContextService authContextService,
        ICustomerCommissionService customerCommissionService,
        ITransactionRepositoryService transactionRepository,
        ITPPApiClientService tppApiClientService,
        IAccountsApiClientService accountsApiClientService,
        IIbanAccountUtility ibanAccountUtility,
        ILogger<BatchPaymentsRequestProvider> logger)
        : base(httpContextService, authContextService)
    {
        _mapper = mapper;
        _customerCommissionService = customerCommissionService;
        _transactionRepository = transactionRepository;
        _tppApiClientService = tppApiClientService;
        _accountsApiClientService = accountsApiClientService;
        _ibanAccountUtility = ibanAccountUtility;
        _logger = logger;
    }

    public PaymentsResponse EnrichResponse(BatchPaymentsRequest request, List<RepoTransaction> transactions)
    {
        var paymentsResponse = request.CreateEmptyResponse();
        foreach (var payment in request.Batch)
        {
            var paymentResponse = payment.EnrichResponse(new ExtendedPaymentResponse()
            {
                RequiresApproval = transactions.Select(x => x.Status.Value == TransactionStatus.PENDING_APPROVAL).FirstOrDefault(),
                TransactionId = transactions.Select(x => x.TransactionId).FirstOrDefault()
            });
            paymentsResponse.Payments.Add(paymentResponse);
        }

        return paymentsResponse;
    }

    public async Task<PaymentsResponse> HandleTransactionExecutionsAsync(BatchPaymentsRequest request, List<RepoTransaction> transactions, RepoWallet wallet)
    {
        var (userId, customerCode, executedAs) = GetUserAndCustomerCode();

        await EnsureSufficientBalance(request, wallet, userId);

        var paymentsResponse = request.CreateEmptyResponse();
        foreach (var payment in request.Batch)
        {
            var transaction = transactions.FirstOrDefault(x => x.TransactionId == payment.TransactionId);

            ExtendedPaymentResponse paymentResponse = await TryExecuteCommissionAsync(payment, transaction, wallet, userId, customerCode);
            if (paymentResponse != null)
            {
                paymentsResponse.Payments.Add(paymentResponse);
                continue;
            }

            transaction.ExecutedAs = executedAs;
            transaction.ExecutedBy = userId;
            paymentResponse = await TryPayAsync(payment, transaction);
            paymentsResponse.Payments.Add(paymentResponse);
        }

        return paymentsResponse;
    }

    public async Task<PaymentsResponse> TransactionForecastExecutionsAsync(BatchPaymentsRequest request, RepoWallet wallet)
    {
        var (userId, _, _) = GetUserAndCustomerCode();

        await EnsureSufficientBalance(request, wallet, userId);

        return new() { BatchId = request.BatchId };
    }

    public Task<List<RepoTransaction>> InitiateRepoTransactions(BatchPaymentsRequest request, RepoWallet wallet, string userId)
    {
        var transactions = new List<RepoTransaction>();
        foreach (var payment in request.Batch)
        {
            var transaction = payment.ToPendingRepoTransaction(wallet, userId);
            transaction.BatchId = request.BatchId;
            transactions.Add(transaction);

            payment.TransactionId = transaction.TransactionId;
        }

        return Task.FromResult(transactions);
    }

    public async Task<ValidateRequestResult> ValidateRequest(BatchPaymentsRequest request, RepoWallet wallet)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        //check if batch is not empty
        if (request.Batch.Count == 0)
        {
            throw new EmptyBatch();
        }

        //check if in batch all payments have account IBAN
        if (request.Batch.Any(x => string.IsNullOrWhiteSpace(x.Debtor?.DebtorAccount?.IBAN)))
        {
            throw new SourceAccountMissing();
        }

        //check if request has the same debit account in all payments
        var firstIban = request.Batch[0].Debtor.DebtorAccount.IBAN;
        if (request.Batch.Any(x => x.Debtor.DebtorAccount.IBAN != firstIban))
        {
            throw new SourceAccountNotTheSame();
        }

        var (userId, _, _) = GetUserAndCustomerCode();
        var payment = request.Batch.FirstOrDefault();

        return await payment.ValidateAccountAvailableToUser(wallet, userId, _accountsApiClientService);
    }

    public async Task<BatchCommissionResponse> GetCommissionAsync(BatchPaymentsRequest request, Guid walletId)
    {
        var (userId, customerCode, executedAs) = GetUserAndCustomerCode();

        var response = new BatchCommissionResponse() { Commissions = [], CommissionSum = CommissionResponseBatch.CommissionSum() };
        var commissionTransactionCount = 0;
        foreach (var payment in request.Batch)
        {
            payment.UserID = executedAs;
            await _customerCommissionService.UpdateCustomerCommissionAsync(walletId, TransactionSubType.GenericPayment, userId, customerCode, commissionTransactionCount);

            try
            {
                var commission = await _tppApiClientService.GetCommissionAsync(payment);
                var commissionBatch = _mapper.Map<CommissionResponseBatch>(commission);
                //keep the original transaction ID for reference
                commissionBatch.TransactionId = payment.TransactionId;
                response.Commissions.Add(commissionBatch);
                response.CommissionSum.Add(commissionBatch);
                commissionTransactionCount++;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to get commission for wallet {WalletId}", walletId);
                response.Commissions.Add(CommissionResponseBatch.ErrorFail(e.Message));
            }
        }

        return response;
    }

    public TotalAmountWithCommission GetTotalAmountWithCommission(BatchPaymentsRequest request, BatchCommissionResponse commissionResponse)
    {
        var totalAmount = request.Batch.Sum(x => x.SettlementInfo.Amount) + commissionResponse.CommissionSum.CommissionInfo.Total;
        return new TotalAmountWithCommission(request.Batch[0].SettlementInfo.CCY ?? "EUR", totalAmount, commissionResponse.CommissionSum.CommissionInfo.Total);
    }

    #region [ Helpers ]

    private async Task<ExtendedPaymentResponse> TryExecuteCommissionAsync(
        ExtendedPaymentsRequest payment,
        RepoTransaction transaction,
        RepoWallet wallet,
        string userId,
        string customerCode)
    {
        ExtendedPaymentResponse paymentResponse = null;
        try
        {
            await _customerCommissionService.UpdateCustomerCommissionAsync(wallet.WalletId, transaction.TransactionSubType, userId, customerCode);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to update customer commission for wallet {WalletId}", wallet.WalletId);
            transaction.UpdateTransactionFailure(e.Message);
            _ = await _transactionRepository.SaveAsync(transaction);
            paymentResponse = payment.EnrichResponse(new ExtendedPaymentResponse());
            paymentResponse.ErrorReason = e.Message;
        }

        return paymentResponse;
    }

    private async Task<ExtendedPaymentResponse> TryPayAsync(ExtendedPaymentsRequest payment, RepoTransaction transaction)
    {
        ExtendedPaymentResponse paymentResponse = null;
        var response = await _tppApiClientService.PayAsync(payment);

        try
        {
            await response.HandleResultAsync(transaction, _transactionRepository);
            paymentResponse = _mapper.Map<ExtendedPaymentResponse>(response.Payload);
            paymentResponse.TransactionId = transaction.TransactionId;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to execute payment for transaction {TransactionId}", transaction.TransactionId);
            paymentResponse = payment.EnrichResponse(new ExtendedPaymentResponse());
            paymentResponse.TransactionId = transaction.TransactionId;
            paymentResponse.ErrorReason = e.Message;
        }

        return paymentResponse;
    }

    private async Task EnsureSufficientBalance(BatchPaymentsRequest request, RepoWallet wallet, string userId)
    {
        var commissionResponse = await GetCommissionAsync(request, wallet.WalletId);


        //var accont = walletAccount ? wallet.WalletAccount : request.Debtor.DebtorAccount.IBAN.Trim();
        var accountBalance = await _accountsApiClientService.GetAccountBalanceAsync(new GetAccountBalanceRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(wallet.WalletAccount),
            UserId = userId,
            Currency = "070"
        });
        var totalAmount = request.Batch.Sum(x => x.SettlementInfo.Amount) + commissionResponse.CommissionSum.CommissionInfo?.Total;
        if (totalAmount > accountBalance.AvailableBalance)
        {
            _logger.LogDebug("Insufficient balance for user {UserId} on wallet {WalletId}. Available: {Available}, Required: {Required}",
                userId, wallet.WalletId, accountBalance.AvailableBalance, totalAmount);
            throw new InsufficientBalanceException();
        }
    }

    public void SetNewDebitAccount(BatchPaymentsRequest request, string ibanAccount)
    {
        foreach (var payment in request.Batch)
        {
            payment.Debtor.DebtorAccount.IBAN = ibanAccount;
        }
    }

    #endregion
}
