﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using nbg.netcore.consent.types;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.EWallet.Repository.Types.coreApis;
using Nbg.NetCore.CosmosConnector.Types.Constants;
using Nbg.NetCore.HttpExceptions;
using Nbg.OpenBanking.Utilities;

namespace Nbg.Ewallet.Api.Implementation;

public class WalletRegistrationService : IWalletRegistrationService
{
    private readonly ILogger<WalletRegistrationService> _logger;
    private readonly IMainFrameConnector _mainFrameConnector;
    private readonly IMapper _mapper;
    private readonly IConsentsRepositoryService _consentsRepositoryService;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly IValidationService _validationService;
    private readonly IUserPermissionsRepositoryService _userPermissionsRepositoryService;
    private readonly ICorporateApiClientService _corporateApiClientService;
    private readonly ICreateAccountService _createAccountService;

    public WalletRegistrationService(
        ILogger<WalletRegistrationService> logger,
        IMainFrameConnector mainFrameConnector,
        IMapper mapper,
        IConsentsRepositoryService consentsRepositoryService,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletRepositoryService walletRepositoryService,
        IValidationService validationService,
        IUserPermissionsRepositoryService userPermissionsRepositoryService,
        ICorporateApiClientService corporateApiClientService,
        ICreateAccountService createAccountService
    )
    {
        _logger = logger;
        _mainFrameConnector = mainFrameConnector;
        _mapper = mapper;
        _consentsRepositoryService = consentsRepositoryService;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletRepositoryService = walletRepositoryService;
        _validationService = validationService;
        _userPermissionsRepositoryService = userPermissionsRepositoryService;
        _corporateApiClientService = corporateApiClientService;
        _createAccountService = createAccountService;
    }

    public async Task<Wallet> Register(WalletRegister request)
    {
        var consent = await GetValidConsentAsync();

        var userId = _httpContextRepositoryService.GetUserId();
        var customerCode = _httpContextRepositoryService.GetCustomerCode();
        //var companyName = _httpContextRepositoryService.GetCompanyName();
        var customerData = await _mainFrameConnector.GetCustomerDataAsync(customerCode);
        var customerProductDetails = await _mainFrameConnector.GetCustomerProductDetailsAsync(customerCode);
        var authCustomerData = await _mainFrameConnector.GetAuthorizationLevelAsync(userId);
        var validatedCustomerData = await _validationService.ValidateCustomerDataAsync(customerData, customerProductDetails, authCustomerData);

        var walletName = validatedCustomerData.Name;

        var deserializedConsent = consent.ConsentData.GetDeserializedObject<EWalletConsentData>();
        if (!string.IsNullOrEmpty(deserializedConsent?.WalletName))
            walletName = deserializedConsent.WalletName;

        if (!string.IsNullOrEmpty(request.WalletName))
            walletName = request.WalletName;

        var taxId = await _mainFrameConnector.GetTaxIdFromUserProfileAsync(userId, customerCode);
        if (!string.IsNullOrEmpty(taxId))
        {
            // check if there is another wallet with the same vatnumber
            var walletsList = await _walletRepositoryService.FindAllByParamsAsync(taxId, null, null, null);
            if (walletsList.Count > 0) throw new WalletAlreadyExistsException();
        }

        if (validatedCustomerData.ValidationControls.Count > 0
            || validatedCustomerData.IsActive == false
            || validatedCustomerData.HasMissingInformation == true)
        {
            throw new RegistrationElegibilityException();
        }

        if (await _walletRepositoryService.ExistsByOwnerUserIdAsync(userId))
            throw new WalletAlreadyExistsException();

        var newWalletId = Guid.NewGuid();
        var newWallet = new Wallet()
        {
            WalletId = newWalletId,
            OwnerUserId = userId,
            WalletName = walletName,
            RegistrationDate = DateTime.UtcNow,
            VatNumber = validatedCustomerData.VatNumber,
            OrganizationName = validatedCustomerData.Name,
            IsCorporateUser = customerData.BasicInfo.Type == CustomerTypes.LegalEntity,
            OwnerCustomerCode = customerData.BasicInfo.CifId
        };

        var repoWallet = _mapper.Map<RepoWallet>(newWallet);
        repoWallet.TenantId = _httpContextRepositoryService.GetClientId();
        await _walletRepositoryService.SaveAsync(repoWallet);

        //add ownUserId permissions as an admin
        var userPermission = new UserPermission()
        {
            Id = Guid.NewGuid(),
            Admin = true,
            Approve = true,
            BalanceView = true,
            CreationDate = DateTime.UtcNow,
            ExpirationDate = new DateTime(2999, 12, 31, 0, 0, 0),
            Limits = null,
            Submit = true,
            TransactionView = true,
            InheritsAuthorizations = true,
            UserId = userId,
            WalletId = newWalletId,
        };
        var repoUserPermission = _mapper.Map<RepoUserPermission>(userPermission);
        repoUserPermission.AssignedBy = userId;
        await _userPermissionsRepositoryService.SaveAllAsync(new List<RepoUserPermission> { repoUserPermission });

        var walletResponse = _mapper.Map<Wallet>(repoWallet);
        return walletResponse;
    }

    public async Task<Wallet> CreateAccountAndConnetToWallet(Guid walletId)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var craCode = _httpContextRepositoryService.GetCustomerCode();

        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);
        if (myWallet.WalletAccount != null)
        {
            throw new WalletAlreadyExistsException();
        }

        //var productCode = await _hostCommonConfigurationService.GetProductCodeAsync();
        //var branch = await _hostCommonConfigurationService.GetBranchAsync();
        var request = new OpenAccountRequest()
        {
            //AccountType = productCode,
            UserCra = craCode,
            //CustomerBranch = branch,
            Currency = "EUR",
            IsSmsOtp = false,
            TanNumber = null,
            TermsAccepted = true,
            UserID = userId,
            //ServiceBranch = branch,
            IsCorporateUser = myWallet.IsCorporateUser
        };

        var result = await _createAccountService.OpenAccountAsync(request);
        var account = result.Iban;
        if (myWallet.IsCorporateUser) await _corporateApiClientService.ConnectAccountAsync(userId, account);
        await _walletRepositoryService.UpdateWalletAccountByWalletIdAsync(myWallet.WalletId.ToString(), account);
        myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);
        var walletResponse = _mapper.Map<Wallet>(myWallet);
        return walletResponse;
    }

    private async Task<EWalletConsent> GetValidConsentAsync()
    {
        var consentId = _httpContextRepositoryService.GetConsentId()
                        ?? throw new BadRequestException("Consent id not found");

        var repoConsentFull = await _consentsRepositoryService.GetConsentAsync(consentId);
        var consent = _mapper.Map<EWalletConsent>(repoConsentFull)
                      ?? throw new BadRequestException($"Consent with id {consentId} not found");

        if (consent.Status != ConsentStatus.Authorized)
        {
            throw new BadRequestException("Unauthorized consent");
        }

        if (consent.EndDate != null && consent.EndDate < DateTime.UtcNow)
        {
            throw new BadRequestException("Expired consent");
        }

        var subject = _httpContextRepositoryService.GetSub();
        if (consent.Sub == null || !consent.Sub.Equals(subject, StringComparison.OrdinalIgnoreCase))
        {
            throw new BadRequestException("Consent not assigned to user");
        }

        consent.Data = consent.ConsentData.GetDeserializedObject<EWalletConsentData>();
        return consent;
    }
}
