﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Payments;

public class ExtendedPaymentForecastResponse
{
    [DataMember(Name = "transactionId")]
    public Guid TransactionId { get; set; }

    public static ExtendedPaymentForecastResponse ToForecastResponse(ExtendedPaymentResponse response)
    {
        return new ExtendedPaymentForecastResponse
        {
            TransactionId = response.TransactionId
        };
    }
}


