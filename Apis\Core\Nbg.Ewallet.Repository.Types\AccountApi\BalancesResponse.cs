﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

[DataContract]
public class BalancesResponse
{
    [DataMember(Name = "accounts")]
    public List<AccountBalance> Accounts { get; set; }
}

[DataContract]
public class AccountBalance
{
    public AccountBalance()
    {
        AccountFeature = new FeatureAccount()
        {
            EnableCheque = true,
            EnableMandate = true,
            EnableOtherInfo = true,
            EnableOwners = true,
            EnableRates = true,
            EnableTransfer = true,
            EnableTrns = true
        };
    }
    /// <summary>Just the serial number of this entry.</summary>
    [DataMember(Name = "serialNum")]
    public string SerialNum { get; set; }
    /// <summary>The NBG account number (e.g. 11 digits).</summary>
    [DataMember(Name = "account")]
    public string Account { get; set; }
    /// <summary>Currency, 3-letter code , e.g. <code>EUR</code>.</summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }
    /// <summary>Διαθέσιμο υπόλοιπο.</summary>
    [DataMember(Name = "availableBalance")]
    public decimal AvailableBalance { get; set; }
    /// <summary>Λογιστικό υπόλοιπο.</summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }
    /// <summary>Nicely formatted IBAN.</summary>
    [DataMember(Name = "iban")]
    public string IBAN { get; set; }
    /// <summary>TBD</summary>
    [DataMember(Name = "flag")]
    public string Flag { get; set; }
    /// <summary>Φιλική ονομασία.</summary>
    [DataMember(Name = "alias")]
    public string Alias { get; set; }

    /// <summary>Κωδικός Προιόντος Λογ/σμού</summary>
    [DataMember(Name = "productCode")]
    public string ProductCode { get; set; }

    /// <summary>ένδειξη σύνδεσης</summary>
    [DataMember(Name = "connectInd")]
    public string ConnectInd { get; set; }

    /// <summary>ένδειξη χρέωσης</summary>
    [DataMember(Name = "debitInd")]
    public string DebitInd { get; set; }

    /// <summary>ένδειξη πίστωσης</summary>
    [DataMember(Name = "creditInd")]
    public string CreditInd { get; set; }

    /// <summary>υπόλοιποι indicators</summary>
    [DataMember(Name = "restInd")]
    public string RestInd { get; set; }

    /// <summary>ειδικός</summary>
    [DataMember(Name = "genLedger")]
    public string GenLedger { get; set; }

    ///<summary>flag για το αν είναι 'δικός' του λογαριασμός </summary>
    ///(connectInd='1' and (debitInd='1' or (creditInd='10' or creditInd='01' )) 
    [DataMember(Name = "isOwnAccount")]
    public bool IsOwnAccount { get; set; }

    [DataMember(Name = "feature")]
    public FeatureAccount AccountFeature { get; set; }

    /// <summary>
    /// υπόλοιπο διαθέσιμο για μεταφορα στο εξωτερικό
    /// </summary>
    [DataMember(Name = "newmoneyBalance")]
    public decimal NewmoneyBalance { get; set; }
}

[DataContract]
public class FeatureAccount
{
    /// <summary>
    /// Κινήσεις
    /// </summary>
    [DataMember(Name = "enableTrns")]
    public bool EnableTrns { get; set; }

    /// <summary>
    /// Αλλες Πληροφορίες
    /// </summary>
    [DataMember(Name = "enableOtherInfo")]
    public bool EnableOtherInfo { get; set; }

    /// <summary>
    /// Πάγιες
    /// </summary>
    [DataMember(Name = "enableMandate")]
    public bool EnableMandate { get; set; }

    /// <summary>
    /// Μπλοκ Επιταγών
    /// </summary>
    [DataMember(Name = "enableCheque")]
    public bool EnableCheque { get; set; }

    /// <summary>
    /// Μεταφορα
    /// </summary>
    [DataMember(Name = "enableTransfer")]
    public bool EnableTransfer { get; set; }

    /// <summary>
    /// Επιτόκια
    /// </summary>
    [DataMember(Name = "enableRates")]
    public bool EnableRates { get; set; }

    /// <summary>
    /// Συνδικαιουχοι
    /// </summary>
    [DataMember(Name = "enableOwners")]
    public bool EnableOwners { get; set; }
}
