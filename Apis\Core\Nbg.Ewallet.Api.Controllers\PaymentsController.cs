﻿using System;
using System.Threading.Tasks;
using ibank.ThirdParty.Types;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Payments;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// This Controller provides functionality for EWallet Payments
/// </summary>
[Produces("application/json")]
[Consumes("application/json")]
[ApiExplorerSettings(GroupName = "Payments")]
public class PaymentsController : BaseController
{
    private readonly IPaymentsService _paymentsService;

    public PaymentsController(IPaymentsService paymentsService, ILogger<PaymentsController> logger) : base(logger)
    {
        _paymentsService = paymentsService;
    }

    /// <summary>
    /// Execute Massive Payment
    /// </summary>
    [HttpPost]
    [Route("{walletId}/batch/execute", Name = "Execute")]
    public async Task<ActionResult<PaymentsResponse>> Execute(BatchPaymentsRequest request, [FromRoute] string walletId)
    {
        return await _paymentsService.ExecuteAsync(request, walletId);
    }

    /// <summary>
    /// Bill Payment from a wallet to an organization
    /// </summary>
    [HttpPost]
    [Route("{walletId}/pay", Name = "Pay")]
    public async Task<ActionResult<ExtendedPaymentResponse>> Pay(ExtendedPaymentsRequest request, [FromRoute] string walletId)
    {
        return await _paymentsService.PayAsync(request, walletId);
    }

    /// <summary>
    /// Forecasts a batch of payment transactions to validate if sufficient balance is available for each, without executing the actual payments.
    /// </summary>
    [HttpPost]
    [Route("{walletId}/batch/execute-forecast", Name = "Execute Forecast")]
    public async Task<ActionResult<PaymentsForecastResponse>> ExecuteForecast(BatchPaymentsRequest request, [FromRoute] string walletId)
    {
        return await _paymentsService.ExecuteForecastAsync(request, walletId);
    }

    /// <summary>
    /// Forecasts a single bill payment transaction to validate if sufficient balance is available, without executing the actual payment.
    /// </summary>
    [HttpPost]
    [Route("{walletId}/pay-forecast", Name = "Pay Forecast")]
    public async Task<ActionResult<ExtendedPaymentForecastResponse>> PayForecast(ExtendedPaymentsRequest request, [FromRoute] string walletId)
    {
        return await _paymentsService.PayForecastAsync(request, walletId);
    }

    /// <summary>
    /// Bill Payment commissions
    /// </summary>
    [HttpPost]
    [Route("{walletId}/commission", Name = "Commission")]
    public async Task<ActionResult<CommissionResponse>> Commission(ExtendedPaymentsRequest request, [FromRoute] Guid walletId)
    {
        return await _paymentsService.CommissionAsync(request, walletId);
    }

    /// <summary>
    /// Execute Massive Payment
    /// </summary>
    [HttpPost]
    [Route("{walletId}/batch/commission", Name = "BatchCommission")]
    public async Task<ActionResult<BatchCommissionResponse>> BatchCommission(BatchPaymentsCommissionRequest request, [FromRoute] Guid walletId)
    {
        return await _paymentsService.BatchCommissionAsync(request, walletId);
    }

    /// <summary>
    /// Pay
    /// </summary>
    //[HttpPost]
    //[Route("pay", Name = "Pay")]
    //public async Task<ActionResult<PaymentsResponse>> Pay(PaymentsPayRequest request)
    //{
    //    return await HandleResult(this, _paymentsService, service => service.Pay(request));
    //}
}
