using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using specflow.tests.Helpers;
using SpecFlowConfiguration;
using TechTalk.SpecFlow;

namespace EWallet.Tests
{
    [Binding]
    public class WalletRegistrationSteps
    {
        private readonly HttpClient _httpClient;
        private FeatureContext _featureContext;
        private ISpecFlownfigurationService _specFlownfigurationService;
        public WalletRegistrationSteps(HttpClient httpClient, FeatureContext featureContext, ISpecFlownfigurationService specFlownfigurationService)
        {
            _httpClient = httpClient;
            _featureContext = featureContext;
            _specFlownfigurationService = specFlownfigurationService;
            if (!_featureContext.ContainsKey("RegisteredWallets"))
            {
                _featureContext.Add("RegisteredWallets", new List<Wallet>());
            }
            if (!_featureContext.ContainsKey("Subscriptions"))
            {
                _featureContext.Add("Subscriptions", new Dictionary<Guid, SubscriptionResponse>());
            }
            if (!_featureContext.ContainsKey("LoggedUser"))
            {
                _featureContext.Add("LoggedUser", "");
            }
        }

        [When(@"I register a wallet with (.*)")]
        public async Task WhenIRegisterAWallet(string walletName)
        {

            var registerRequest = new WalletRegister { WalletName = walletName };
            var response = await _httpClient.PostAsJsonAsync("wallet/register", registerRequest);

            var walletRegistrationResponse = await response.Content.ReadAsStringAsync();
            var newWallet = JsonSerializer.Deserialize<Wallet>(walletRegistrationResponse);

            var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
            walletsList.Add(newWallet);
        }

        [Then(@"I create an account for the registered wallet with the walletName (.*)")]
        public async Task ThenICreateAnAccountForTheRegisteredWalletWithTheWalletName(string walletName)
        {
            var registeredWallets = _featureContext.Get<List<Wallet>>("RegisteredWallets");
            var wallet = registeredWallets.First<Wallet>(x => x.WalletName == walletName);
            string walletId = wallet.WalletId.ToString();
            var uri = $"wallet/{walletId}/account";
            var response = await _httpClient.PostAsJsonAsync(uri, new { });
            response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        }

        [Given(@"I get the organization users for the (.*)")]
        public async Task GivenIGetTheOrganizationUsersForThe(string walletName)
        {
            var registeredWallets = _featureContext.Get<List<Wallet>>("RegisteredWallets");
            var wallet = registeredWallets.First<Wallet>(x => x.WalletName == walletName);
            var walletId = wallet.WalletId.ToString();
            var uri = $"wallet/{walletId}/users";

            var response = await _httpClient.GetAsync(uri);
            response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        }

        [Given(@"I am logged in as the user (.*)")]
        public async Task GivenIAmLoggedInAsUser(string userId)
        {
            var config = _specFlownfigurationService.GetConfiguration();

            var user = config.GetUserId(userId);
            switch (config.Environment)
            {
                case "SandBox":
                    {
                        var token = await AuthCodeAuthorizationHelper.GetAuthCodeAuthorizationHeaderAsync(user.userName, user.passWord);
                        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                        _featureContext["LoggedUser"] = user.userName;
                    }
                    break;
                case "SandBoxDirect":
                    {
                        //remove old headers if any
                        _httpClient.DefaultRequestHeaders.Remove("UserId");
                        _httpClient.DefaultRequestHeaders.Remove("Customer-Code");

                        _httpClient.DefaultRequestHeaders.Add("UserId", user.userName);
                        //_httpClient.DefaultRequestHeaders.Add("Customer-Code", config.GetUser(userId).customerCode);
                        _httpClient.DefaultRequestHeaders.Add("Customer-Code", config.GetUserId(userId).customerCode);

                        _featureContext["LoggedUser"] = user.userName;
                    }
                    break;
                case "Core":
                    {

                        //remove old headers if any
                        _httpClient.DefaultRequestHeaders.Remove("UserId");
                        _httpClient.DefaultRequestHeaders.Remove("Customer-Code");

                        _httpClient.DefaultRequestHeaders.Add("UserId", user.userName);
                        _httpClient.DefaultRequestHeaders.Add("Customer-Code", config.GetUserId(userId).customerCode);
                        _featureContext["LoggedUser"] = user.userName;
                    }
                    break;
                case "QA":
                    {
                        var token = await AuthCodeAuthorizationHelper.GetAuthCodeAuthorizationHeaderAsync(user.userName, user.passWord);
                        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                        _featureContext["LoggedUser"] = user.userName;
                    }
                    break;
            }
        }

        [When(@"I register a new wallet with wallet name (.*)")]
        public async Task WhenIRegisterForANewWalletWithWalletName(string walletName)
        {
            var registerRequest = new WalletRegister { WalletName = walletName };
            var response = await _httpClient.PostAsJsonAsync("wallet/register", registerRequest);
            response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

            var walletRegistrationResponse = await response.Content.ReadAsStringAsync();
            var newWallet = JsonSerializer.Deserialize<Wallet>(walletRegistrationResponse);

            var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
            walletsList.Add(newWallet);
        }

        [When(@"I create a new subscription with Plan ID (.*) for wallet (.*)")]
        public async Task WhenICreateANewSubscriptionWithPlanIDBasic(string planId, string walletName)
        {
            var registeredWallets = _featureContext.Get<List<Wallet>>("RegisteredWallets");
            var wallet = registeredWallets.First(x => x.WalletName == walletName);
            var createSubscriptionRequest = new CreateSubscriptionRequest
            {
                Tier = (SubscriptionTier)Enum.Parse(typeof(SubscriptionTier), planId)
            };

            var uri = $"wallet/{wallet.WalletId}/subscription";
            var response = await _httpClient.PostAsJsonAsync(uri, createSubscriptionRequest);
            response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var createSubscriptionResponse = await response.Content.ReadAsStringAsync();
        var newSubscription = JsonSerializer.Deserialize<SubscriptionResponse>(createSubscriptionResponse);

            var subscriptions = (Dictionary<Guid, SubscriptionResponse>)_featureContext["Subscriptions"];
            subscriptions.Add(wallet.WalletId, newSubscription);
        }

        [When(@"I proceed to connect a special purpose account with the newly created wallet with (.*)")]
        public async Task WhenIProceedToConnectASpecialPurposeAccountWithTheNewlyCreatedWallet(string walletName)
        {
            var registeredWallets = _featureContext.Get<List<Wallet>>("RegisteredWallets");
            var wallet = registeredWallets.First<Wallet>(x => x.WalletName == walletName);
            string walletId = wallet.WalletId.ToString();
            var uri = $"wallet/{walletId}/account";
            var response = await _httpClient.PostAsJsonAsync(uri, new { });
            response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

            var walletAccountResponse = await response.Content.ReadAsStringAsync();
            var newWalletWithAccount = JsonSerializer.Deserialize<Wallet>(walletAccountResponse);

            var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
            var index = walletsList.FindIndex(x => x.WalletName == walletName);
            walletsList[index] = newWalletWithAccount;

        }

        [Then(@"the (.*) created is connected with a special purpose account")]
        public async Task ThenTheNewlyCreatedWalletIsConnectedWithASpecialPurposeAccount(string walletName)
        {
            var registeredWallets = _featureContext.Get<List<Wallet>>("RegisteredWallets");
            var wallet = registeredWallets.First<Wallet>(x => x.WalletName == walletName);
            wallet.WalletAccount.Should().NotBeNullOrWhiteSpace(); // check there is an account
            wallet.WalletAccountCreatedAt.Should().NotBeNull(); // check the creation date
        }

        [Then(@"there is an active subcription connected with the (.*) created wallet with Plan ID equal to (.*)")]
        public void ThenThereIsAnActiveSubcriptionConnectedWithTheFirstRegisteredWalletCreatedWalletWithPlanIDEqualToBasic(string walletName, string planId)
        {
            var registeredWallets = _featureContext.Get<List<Wallet>>("RegisteredWallets");
            var wallet = registeredWallets.First<Wallet>(x => x.WalletName == walletName);
            var subscriptions = _featureContext.Get<Dictionary<Guid, SubscriptionResponse>>("Subscriptions");
            var subscription = subscriptions[wallet.WalletId];
            subscription.Tier.Should().Be((SubscriptionTier)Enum.Parse(typeof(SubscriptionTier), planId)); // check correct planid
            subscription.EndDate.Should().BeAfter(DateTime.UtcNow); // check correct subscription expiration date
        }
    }
}
