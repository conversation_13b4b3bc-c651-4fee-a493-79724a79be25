﻿using System;
using System.Text;

namespace Nbg.Ewallet.Repository.Types;

public class RepoWalletPermission : IPermission
{
    public Guid Id { get; set; }

    public Guid WalletId { get; set; }

    public Guid TargetWalletId { get; set; }

    public bool Submit { get; set; }

    public bool Approve { get; set; }

    public bool Admin
    {
        get
        {
            return false;
        }
        set
        {

        }
    }

    public bool InheritsAuthorizations
    {
        get
        {
            return false;
        }
        set
        {

        }
    }

    public bool BalanceView { get; set; }

    public bool TransactionView { get; set; }

    public DateTime ExpirationDate { get; set; }

    public DateTime CreatedAt { get; set; }
    public string AssignedBy { get; set; }

    public string RevokedBy { get; set; }

    PermissionType IPermission.PermissionType => PermissionType.WALLET;

    public string ToLogString()
    {
        var sb = new StringBuilder();

        AppendLine(ref sb, "Id", Id);
        AppendLine(ref sb, "ExternalWalletId", TargetWalletId);
        AppendLine(ref sb, "WalletId", WalletId);
        AppendLine(ref sb, "Submit", Submit);
        AppendLine(ref sb, "Approve", Approve);
        AppendLine(ref sb, "BalanceView", BalanceView);
        AppendLine(ref sb, "TransactionView", TransactionView);
        AppendLine(ref sb, "ExpirationDate", ExpirationDate);
        AppendLine(ref sb, "CreatedAt", CreatedAt);
        AppendLine(ref sb, "AssignedBy", AssignedBy);
        AppendLine(ref sb, "RevokedBy", RevokedBy);

        return sb.ToString();
    }
    private static void AppendLine<T>(ref StringBuilder sb, ReadOnlySpan<char> label, T value)
    {
        sb.Append(label).Append(": ").Append(value).AppendLine();
    }
}
