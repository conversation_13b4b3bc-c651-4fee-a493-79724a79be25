﻿using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Admin;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IAdminService
{
    Task<ConfigureB2bRulesResponse> ConfigureB2BRules(ConfigureB2bRulesRequest request);
    Task<ConfigureB2ChannelsRequest> ConfigureB2Channels(ConfigureB2ChannelsRequest request);
    Task<CommonSuccessResult<object>> UploadTerms(Terms request);
}
