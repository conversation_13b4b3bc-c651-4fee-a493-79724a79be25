{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },

    "ConnectionStrings": {
        "EWallet": "Server=V0000A0013,2544;database=EWallet;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False;MultipleActiveResultSets=True",
        "Consents": "Server=V0000A0013,2544;database=BusinessConsent;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False",
        "AccountingEngine": "Server=V0000A0013,2544;DataBase=AccountingEngine;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False"
    },

    "CicsHttpConnector": {
        "Host": "https://************",
        "Port": 2512,
        "MaxConnectionsLimit": 10,
        "Timeout": 20000,
        "LocalCertificateFingerprint": "9cbe4cad3dfdd4c33d66efa42d85b8906e0277fa",
        "DangerousAcceptAnyServerCertificate": true
    },

    "AceConnectorOptions": {
        "Host": "http://acesit.ocpc.cosmos.nbg.gr/",
        "Port": 80,
        "MaxConnectionsLimit": 10,
        "Timeout": 20000,
        //"LocalCertificateFingerprint": "your-certificate-fingerprint",
        "DangerousAcceptAnyServerCertificate": true,
        "CheckCertificateRevocationList": false,
        "UseProxy": false,
        "UseSockets": true,
        //"ClientCertificate": null,
        "DefaultChannelCode": "PTRNS",
        "DefaultSecurityToken": "ptrns123sit!"
    },

    "ServiceAuditSettings": {
        "EnableAudit": true,
        "Application": "8B2D8AD6-83EB-4CAD-A384-22CA1B65F080",
        "PrefixServiceName": "",
        "Database": "EWallet"
    },

    "TPPApiClientSettings": {
        "Client": "tppApi",
        "Pay": "payments/pay",
        "Commission": "payments/commission",
        "Origin": {
            "NetworkId": "B4C6C6B7-F700-4597-A857-AA9FCDC3A466",
            "TransactionTerminalMachineId": "EWALLETMACHINE",
            "TransactionTerminalId": "724A6B2E-8807-456C-8145-F5B82FC08001",
            "TransactionTerminalSpotId": "DFCE19C8-7E48-45B1-8A9A-FD6834B50ACC",
            "UserId": "456277"
        }
    },

    "AccountsCoreClientSettings": {
        "Client": "accountsCore",
        "OpenAccount": "https://************:2512/JBOPAC"
    },

    "HttpClient": {
        "default": {
            "MaxConnectionsPerServer": 100,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:1:30",
            "UseProxy": false
        },

        "bigDataStetementsAzureApi": {
            "BaseAddress": "https://bigdataappsqa.az.nbg.gr/statements/api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "corporateApi": {
            "BaseAddress": "http://localhost/corporatemanagement.api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "accountsApi": {
            "BaseAddress": "http://localhost/accounts.api/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "tppApi": {
            "BaseAddress": "https://coregenericqa.nbg.gr/payments/Nbg.ThirdParty.Payments.Core.DB2R/",
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "accountsCore": {
            "BaseAddress": "", //TODO 
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        },

        "subscriptionApi": {
            "BaseAddress": "", //TODO 
            "MaxConnectionsPerServer": 20,
            "DangerousAcceptAnyServerCertificate": true,
            "Timeout": "00:01:00",
            "UseProxy": false,
            "ProxyUrl": "http://wsa.central.nbg.gr:8080"
        }
    },

    "ValidationsControlPolicy": {
        "ApplyPolicy": true,
        "Controls": [ "FullKnownCustomer", "LegalDoc", "FinancialProfile", "ActiveIdentity" ],
        "ValidAccountProductCodes": [
            "21201",
            "41201",
            "41287",
            "71201BA",
            "71201PR",
            "71201EL",
            "41277",
            "41343",
            "41000",
            "71000BA",
            "50101",
            "50102",
            "50201",
            "50301",
            "50302",
            "50401",
            "50402",
            "50403",
            "50404",
            "50405",
            "50406",
            "50406",
            "50407",
            "50408",
            "50409",
            "50413",
            "50410",
            "50411",
            "50412",
            "50501",
            "50502",
            "50503",
            "50504",
            "50505",
            "50506",
            "50507",
            "50601",
            "50602",
            "50701",
            "50702",
            "50703",
            "50704",
            "50801",
            "50802",
            "50803",
            "50803",
            "50804",
            "50901",
            "50902",
            "50903",
            "51001",
            "51102",
            "51103",
            "51104",
            "51105",
            "51106",
            "51107",
            "51108",
            "51109",
            "51110",
            "51111",
            "51112",
            "51113",
            "51201",
            "51202",
            "51203",
            "51204",
            "51205",
            "51206",
            "51301",
            "51302",
            "51303",
            "51304",
            "51305",
            "51306",
            "51401",
            "51402",
            "51501",
            "51601",
            "51701",
            "51702",
            "51703"
        ]
    },

    "AccountingEngine": {
        "ConnectionStringName": "AccountingEngine" //must match the name given in ConnectionStrings section
    },

    // TODO : Update this
    "UrlSettings": {
        "AuthorizationUrl": "https://myqa.nbg.gr/identity/connect/authorize?consent_id={{consent_id}}&client_id={{client_id}}&scope=ewallet-api-v1&redirect_uri={{redirect_uri}}&response_type=code"
    },

    "RepositorySettings": {
        "ConnectionName": "EWallet",
        "ApplicationConsentTemplateId": "3d8d527b-8477-4e61-a593-8fc401b8cb17",
        "UseAceConnector": true
    },

    "CoreApisUrls": {

    },

    "HostCommonConfigurationSettings": {
        "Branch": "700",
        "SubBranch": "4",
        "LegalEntityProductCode": "50301", //41201W",
        "PhysicalPersonProductCode": "51002" //41000W"
    },

    "CreateAccountSettings": {
        "enableValidations": false
    }
}
