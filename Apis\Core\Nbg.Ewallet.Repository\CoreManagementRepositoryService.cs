﻿//using System.Net.Http;
//using System.Text;
//using Microsoft.Extensions.Logging;
//using Microsoft.Extensions.Options;
//using Nbg.Ewallet.Api.Implementation;
//using Nbg.Ewallet.Repository.Interfaces;
//using Newtonsoft.Json;

//namespace Nbg.Ewallet.Repository;

////TODO: remove this if possible and move it on cics connector
//public class CoreManagementRepositoryService : ICoreManagementInterfaceService
//{
//    private readonly IHttpClientFactory _httpClientFactory;
//    private readonly ILogger<WalletService> _logger;
//    private readonly IHostCommonConfigurationService _hostCommonConfigurationService;
//    private readonly UrlSettings _urlSettings;

//    public CoreManagementRepositoryService(
//        IHttpClientFactory httpClientFactory,
//        ILogger<WalletService> logger,
//        IOptions<UrlSettings> urlOptions,
//        IHostCommonConfigurationService hostCommonConfigurationService)
//    {
//        _httpClientFactory = httpClientFactory;
//        _logger = logger;
//        _hostCommonConfigurationService = hostCommonConfigurationService;
//        _urlSettings = urlOptions.Value;
//    }

//    internal class PayloadEncapsulator<T>
//    {
//        public T objPayload { get; set; }

//        public PayloadEncapsulator(T aPayLoad)
//        {
//            objPayload = aPayLoad;
//        }

//        public StringContent ToJson()
//        {
//            // JerryC
//            //anonymoys obj with payLoad Property to encapsulate the T with....
//            var encobject = new { payload = objPayload };
//            string jsonString = JsonConvert.SerializeObject(encobject, Formatting.Indented);
//            return new StringContent(jsonString, Encoding.UTF8, "application/json");
//        }
//    }
//}
