﻿namespace IntegrationTests.Helpers;

public static class AuthorizationConstants
{
    //public const string ClientId = "33946386-1363-4FD9-BFA2-28E1E4643874";
    //public const string ClientSecret = "5C68667E-2129-4BAC-8232-BF598CFB4D8A";
    //public const string TokenEndpoint = "https://myqa.nbg.gr/identity/connect/token";
    //public const string Scope = "sandbox-ewallet-api-v1 sandbox-ewallet-management";
    //public const string ManagementScope = "sandbox-ewallet-management";

    //public const string AuthorizationUrl = "https://myqa.nbg.gr/identity/connect/authorize";
    //public const string RedirectUri = "http://localhost:52694/callback/";


    public const string ClientId = "9E7EBA58-3A10-4675-948F-903228854E25";
    public const string ClientSecret = "1148C59F-0F80-4317-A4A3-E866B26DB2B7";
    public const string TokenEndpoint = "https://myqa.nbg.gr/identity/connect/token";
    public const string Scope = "qa-i-bank-erp-api-v1 bill-payments-api-qa ewallet-api-v1";
    public const string ManagementScope = "";

    public const string AuthorizationUrl = "https://myqa.nbg.gr/identity/connect/authorize";
    public const string RedirectUri = "http://localhost:52694/callback/";
}
