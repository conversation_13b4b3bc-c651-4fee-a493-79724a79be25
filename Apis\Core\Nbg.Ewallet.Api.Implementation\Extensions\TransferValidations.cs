﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Nbg.Ewallet.Api.Implementation.Extensions
{
    public static class TransferValidations
    {
        private static readonly Regex _greekRegex = new Regex(@"^[\p{IsGreek}\d\s\p{P}]+$", RegexOptions.Compiled);
        private static readonly Regex _latinRegex = new Regex(@"^[A-Za-z\d\s\p{P}]+$", RegexOptions.Compiled);
        private static readonly Regex _onlyDigitsRegex = new Regex(@"^\d+$", RegexOptions.Compiled);

        public static void ValidateBeneficiaryAndReason(string beneficiaryName, string reason)
        {
            if (string.IsNullOrWhiteSpace(beneficiaryName) || string.IsNullOrWhiteSpace(reason))
            {
                throw new ValidationException("BeneficiaryName and Reason must be provided.");
            }

            bool isBeneficiaryGreek = _greekRegex.IsMatch(beneficiaryName);
            bool isReasonGreek = _greekRegex.IsMatch(reason);
            if (isBeneficiaryGreek && isReasonGreek) return;

            bool isBeneficiaryLatin = _latinRegex.IsMatch(beneficiaryName);
            bool isReasonLatin = _latinRegex.IsMatch(reason);
            if (isBeneficiaryLatin && isReasonLatin) return;

            bool beneficiaryHasDigits = _onlyDigitsRegex.IsMatch(beneficiaryName);
            if (beneficiaryHasDigits && !isReasonGreek)
            {
                throw new ValidationException("If the beneficiary name contains only digits, the reason must be in Greek.");
            }

            bool reasonIsOnlyDigits = _onlyDigitsRegex.IsMatch(reason);
            if (reasonIsOnlyDigits && !isBeneficiaryGreek)
            {
                throw new ValidationException("If the reason contains only digits, the beneficiary name must be in Greek.");
            }

            throw new ValidationException("Beneficiary and Reason must both be in Greek or both in Latin.");
        }
    }
}
