﻿using System.Text.Json.Serialization;

namespace Nbg.Ewallet.Repository.Types;

/// <summary>
/// Defined LimitTransactionTypes
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum LimitTransactionType
{
    /// <summary>
    /// Transfer
    /// </summary>
    WalletToWallet,
    WalletToNBG,
    WalletToIBAN,
    /// <summary>
    /// Payment
    /// </summary>
    Payment,
}
