﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <SignAssembly>True</SignAssembly>
    <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
    <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.11.4" />
    <!--<PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />-->
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Nbg.Ewallet.Api.Core.Types\Nbg.Ewallet.Api.Types.csproj" />
    <ProjectReference Include="..\Nbg.Ewallet.Repository.Types\Nbg.Ewallet.Repository.Types.csproj" />
  </ItemGroup>
</Project>