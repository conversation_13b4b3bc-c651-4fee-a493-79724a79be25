﻿using Nbg.Ewallet.Api.Types.Subscriptions;

public static class Indications
{
    public static Indication Free
    {
        get
        {
            return new Indication
            {
                Type = 8200072,
                Value = 8300238
            };
        }
    }

    public static Indication Trial
    {
        get
        {
            return new Indication
            {
                Type = 8200072,
                Value = 8300239
            };
        }
    }

    public static Indication Basic
    {
        get
        {
            return new Indication
            {
                Type = 8200072,
                Value = 8300236
            };
        }
    }

    public static Indication Premium
    {
        get
        {
            return new Indication
            {
                Type = 8200072,
                Value = 8300237
            };
        }
    }
}
