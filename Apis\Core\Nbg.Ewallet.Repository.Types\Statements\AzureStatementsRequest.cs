﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types;

/// <summary>
/// 
/// </summary>
[DataContract]
public class AzureStatementsRequest
{

    /// <summary>
    /// 
    /// </summary>
    [DataMember(Name = "account")]
    public string Account { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [DataMember(Name = "channel")]
    public string Channel { get; set; }

    /// <summary>
    /// Format dd.MM.yyyy
    /// </summary>
    [DataMember(Name = "dateFrom")]
    public string DateFrom { get; set; }

    /// <summary>
    /// Format dd.MM.yyyy
    /// </summary>
    [DataMember(Name = "dateTo")]
    public string DateTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [DataMember(Name = "timestampFrom")]
    public string TimestampFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [DataMember(Name = "timestampTo")]
    public string TimestampTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [DataMember(Name = "timestamp")]
    public string Timestamp { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [DataMember(Name = "scrollAction")]
    public string ScrollAction { get; set; }

    /// <summary>
    /// The system to query for statements ('E' for deposits, 'S' for foreign currency accounts)
    /// </summary>
    [DataMember(Name = "system")]
    public string System { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserId { get; set; }

    /// <summary>
    /// Last Row Key
    /// </summary>
    [DataMember(Name = "lastRowKey")]
    public string LastRowKey { get; set; }

    /// <summary>
    /// Limit
    /// </summary>
    [DataMember(Name = "limit")]
    public int? Limit { get; set; }
}
