﻿using System;
using Microsoft.AspNetCore.Http;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.NetCore.Utilities;

namespace nbg.ewallet.repository;

public class HttpContextRepositoryService : IHttpContextRepositoryService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public HttpContextRepositoryService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string GetCustomerCode()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("Customer-Code").Clear()?.TrimStart('0')
               ?? _httpContextAccessor.HttpContext.GetClaimsValue("customer_code").Clear()?.TrimStart('0');
    }

    public string GetUserId()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("UserId").Clear()
               ?? _httpContextAccessor.HttpContext.GetClaimsValue("userId").Clear();
    }

    public int GetNumberOfApprovals()
    {
        var stringValue = _httpContextAccessor.HttpContext.GetHeaderValue("Number_Of_Approvals").Clear() ??
                          _httpContextAccessor.HttpContext.GetClaimsValue("number_of_approvals").Clear();
        if (stringValue is null)
        {
            return 0;
        }

        return !int.TryParse(stringValue, out var numberOfApprovals) ? 0 : numberOfApprovals;
    }

    public string GetAuthorizationLevel()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("Authorization_Level").Clear() ??
               _httpContextAccessor.HttpContext.GetClaimsValue("authorization_level").Clear();
    }

    public string GetCompanyName()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("CompanyName").Clear() ??
               _httpContextAccessor.HttpContext.GetClaimsValue("CompanyName").Clear();
    }

    public Guid GetClientId()
    {
        return Guid.Parse(_httpContextAccessor.HttpContext.GetHeaderValue("Client-Id").Clear()
                          ?? _httpContextAccessor.HttpContext.GetClaimsValue("client_id").Clear());
    }

    public string GetConsentId()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("Consent-Id").Clear()
               ?? _httpContextAccessor.HttpContext.GetClaimsValue("consent_id");
    }

    public string GetSub()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("Sub").Clear()
               ?? _httpContextAccessor.HttpContext.GetClaimsValue("sub").Clear();
    }

    public string? GetCallbackUrl()
    {
        var callbackUrl = _httpContextAccessor.HttpContext.GetHeaderValue("Callback-Url");
        return callbackUrl.Clear();
    }

    public string? GetSandboxId()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("sandboxid")?.Clear();
    }
}
