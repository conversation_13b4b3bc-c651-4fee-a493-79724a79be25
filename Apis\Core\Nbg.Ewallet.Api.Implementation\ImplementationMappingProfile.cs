﻿using System;
using System.Globalization;
using System.Linq;
using AutoMapper;
using ibank.ThirdParty.Types;
using ibank.ThirdParty.Types.Core;
using nbg.ewallet.repository.types;
using nbg.netcore.consent.repository.types;
using nbg.netcore.consent.types;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Payments;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.NetCore.Services.Cics.Http.Contract.Jbcrer;
using Limit = Nbg.Ewallet.Api.Types.Wallet.Limit;
using Transaction = Nbg.Ewallet.Api.Types.Transfers.Transaction;

namespace Nbg.Ewallet.Api.Implementation;

public class ImplementationMappingProfile : Profile
{
    public ImplementationMappingProfile()
    {
        CreateMap<CustomerAccount, Account>();
        CreateMap<CustomerData, AccountsResponse>();
        CreateMap<EWalletConsent, RepoConsent>();
        CreateMap<RepoConsent, nbg.netcore.consent.types.CreateConsentRequest>();
        CreateMap<RepoConsent, nbg.netcore.consent.types.UpdateConsentRequest>();
        CreateMap<RepoConsent, Nbg.OpenBanking.ConsentTypes.Consent>();
        CreateMap<Nbg.OpenBanking.ConsentTypes.Consent, RepoConsentFull>();
        CreateMap<Consent, RepoConsentFull>();
        CreateMap<RepoConsentFull, EWalletConsent>();
        CreateMap<Wallet, RepoWallet>()
            .ForMember(rw => rw.WalletId, opt => opt.MapFrom(src => src.WalletId));

        CreateMap<RepoWallet, Wallet>();
        CreateMap<RepoUserPermission, UserPermission>()
            .ForMember(x => x.CreationDate, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(rw => rw.WalletId, opt => opt.MapFrom(src => src.WalletId))
            .ForMember(rw => rw.Id, opt => opt.MapFrom(src => src.Id));

        CreateMap<UserPermission, RepoUserPermission>()
             .ForMember(x => x.CreatedAt, opt => opt.MapFrom(src => src.CreationDate))
             .ForMember(x => x.Id, opt => opt.MapFrom(src => src.Id));

        CreateMap<RepoLimit, Limit>();

        CreateMap<WalletLite, RepoWallet>();
        CreateMap<RepoWallet, WalletLite>();

        CreateMap<Limit, RepoLimit>();

        CreateMap<RepoAuthorizationRequest, RequestorWalletAuthorization>()
            .ForMember(x => x.WalletId, opt => opt.MapFrom(src => src.RequestorWalletId));
        CreateMap<RepoAuthorizationRequest, TargetWalletAuthorization>()
            .ForMember(x => x.WalletId, opt => opt.MapFrom(src => src.TargetWalletId));

        CreateMap<RepoTransaction, Transaction>();
        CreateMap<Transaction, RepoTransaction>();

        CreateMap<WalletPermission, RepoWalletPermission>()
            .ForMember(x => x.CreatedAt, opt => opt.MapFrom(src => src.CreationDate));
        CreateMap<RepoWalletPermission, WalletPermission>()
            .ForMember(x => x.CreationDate, opt => opt.MapFrom(src => src.CreatedAt));

        CreateMap<RepoWallet, WalletLite>();
        CreateMap<RequestorWalletAuthorization, RepoAuthorizationRequest>()
            .ForMember(x => x.RequestorWalletId, opt => opt.MapFrom(src => src.WalletId));

        CreateMap<TargetWalletAuthorization, RepoAuthorizationRequest>()
            .ForMember(x => x.TargetWalletId, opt => opt.MapFrom(src => src.WalletId));

        CreateMap<JbcrerResponsePayload, TransfersResponse>()
            .ForMember(x => x.DebitAccount, opt => opt.MapFrom(src => src.TransactionData[0].DebtorIban))
            .ForMember(x => x.TransferAmount, opt => opt.MapFrom(src => src.AmountTo))
            .ForMember(x => x.AvailableBalance, opt => opt.MapFrom(src => src.TransactionData[0].AvailableBalance))
            .ForMember(x => x.LedgerBalance, opt => opt.MapFrom(src => src.TransactionData[0].LedgerBalance))
            .ForMember(x => x.ReferenceNumber, opt => opt.MapFrom(src => src.TransactionData[0].OrderNo))
            //todo masked beneficiaries
            .ForMember(x => x.Beneficiaries, opt => opt.MapFrom(src => src.TransactionData.Select(t => t.CreditorName1)
            .Concat(src.TransactionData.Select(t => t.CreditorName2))
            .Concat(src.TransactionData.Select(t => t.CreditorName3))
            .Where(c => !string.IsNullOrEmpty(c))
            .ToList()))
            .ForMember(x => x.NetAmountOut, opt => opt.MapFrom(src => src.TransactionData[0].NetAmount))
            .ForMember(x => x.SumCommissionOut, opt => opt.MapFrom(src => src.TransactionData[0].SumCommission))
            .ForMember(x => x.DebitAmountOut, opt => opt.MapFrom(src => src.TransactionData[0].DebitAmount))
            .ForMember(x => x.EteCommissionOut, opt => opt.MapFrom(src => src.TransactionData[0].CommissionNbg))
            .ForMember(x => x.DeptExpensesOut, opt => opt.MapFrom(src => src.TransactionData[0].DeptExpenses))
            .ForMember(x => x.NonStpExpensesOut, opt => opt.MapFrom(src => src.TransactionData[0].NonStpExpenses))
            .ForMember(x => x.UrgentExpensesOut, opt => opt.MapFrom(src => src.TransactionData[0].UrgentExpenses))
            .ForMember(x => x.OnlineExpensesOut, opt => opt.MapFrom(src => src.TransactionData[0].OnlineExpenses))
            .ForMember(x => x.NetAmountOut, opt => opt.MapFrom(src => src.TransactionData[0].NetAmount))
            .ForMember(x => x.DebtorIBAN, opt => opt.MapFrom(src => src.TransactionData[0].DebtorIban));

        CreateMap<AzureStatementsResponse, WalletStatementsResponse>()
            .ForMember(x => x.Statements, opt => opt.MapFrom(src => src.Transactions));

        CreateMap<AzureStatementsTransaction, Statement>()
            .ForMember(x => x.SerialNum, opt => opt.MapFrom(src => src.SerialNum.ToString()))
            .ForMember(x => x.TransDescription, opt => opt.MapFrom(src => src.Description))
            .ForMember(x => x.Date, opt => opt.MapFrom(src => TryParseDate(src.Date)))
            .ForMember(x => x.Valeur, opt => opt.MapFrom(src => TryParseDate(src.Valeur)))
            .ForMember(x => x.Timestamp, opt => opt.MapFrom(src => TryParseDateTime(src.Timestamp)));

        CreateMap<TransfersResponseBase, TransfersResponse>();

        CreateMap<PaymentResponse, ExtendedPaymentResponse>();
        CreateMap<ExtendedPaymentResponse, PaymentResponse>();

        CreateMap<TransferExpensesCommissionsResponse, CalculateFeesResponse>();
        CreateMap<CalculateFeesResponse, TransferExpensesCommissionsResponse>();

        CreateMap<TransferExpensesCommissionsResponse, BatchCalculateFeesResponse>();
        CreateMap<BatchCalculateFeesResponse, TransferExpensesCommissionsResponse>();

        CreateMap<CommissionResponse, CommissionResponseBatch>();
        CreateMap<CommissionResponseBatch, CommissionResponse>();



        CreateMap<TransfersRequestBase, TransfersRequest>();
        CreateMap<PaymentCoreRequest, ExtendedPaymentsRequest>();
    }

    private static DateTime TryParseDateTime(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return default;

        string[] formats = [
        "dd.MM.yyyy",
        "dd.MM.yyyy HH:mm:ss",
        "dd.MM.yyyyHH.mm.ss",
        "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ss",
        "M/d/yyyy h:mm:ss tt", // Added format for 5/22/2025 1:46:18 PM,
        "O"
        ];

        if (DateTime.TryParseExact(input, formats, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal, out var result))
        {
            return result.ToUniversalTime();
        }

        return default;
    }

    private static DateTime TryParseDate(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return default;

        string[] formats = {
            "dd.MM.yyyy",
            "dd.MM.yyyy HH:mm:ss",
            "dd.MM.yyyyHH.mm.ss",
            "yyyy-MM-ddTHH:mm:ss",
            "yyyy-MM-dd HH:mm:ss"
        };

        if (DateTime.TryParseExact(input, formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var result))
        {
            return result.Date; // Strip time part
        }

        return default;
    }
}
