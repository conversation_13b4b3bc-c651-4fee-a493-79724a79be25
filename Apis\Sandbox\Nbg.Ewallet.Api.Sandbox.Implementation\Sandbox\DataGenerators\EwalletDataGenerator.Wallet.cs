﻿using System;
using System.Collections.Generic;
using Nbg.Ewallet.Api.Sandbox.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator
{
    private static List<SandboxWallet> GenerateWallets()
    {
        return new List<SandboxWallet>
        {
            {
                new SandboxWallet
                {
                    WalletId = Guid.NewGuid(),
                    WalletName = "Built-in Wallet",
                    WalletAccount = SandBoxRandomDataHelper.GenerateIBAN(),
                    OrganizationName = "Organization name " + SandBoxRandomDataHelper.GenerateRandomText(),
                    OwnerUserId = "1183831",
                    RegistrationDate = DateTime.Now,
                    VatNumber = SandBoxRandomDataHelper.GenerateRandomNumber(9),
                    ConnectedIban = SandBoxRandomDataHelper.GenerateIBAN(),
                    WalletAccountCreatedAt = DateTime.Now
                }
            }
        };
    }
}
