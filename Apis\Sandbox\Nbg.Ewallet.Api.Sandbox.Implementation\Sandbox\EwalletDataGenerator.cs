﻿using System.Collections.Generic;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.NetCore.ApiSandbox.Interfaces;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

public partial class EwalletDataGenerator : ISandboxDataGenerator
{

    public EwalletDataGenerator()
    {
    }

    public ISandboxDataModel GenerateDataModel(string sandboxId)
    {
        return new EwalletDataModel
        {
            SandboxId = sandboxId,
            Version = 1,
            EwalletSandbox = GenerateEwalletData()
        };
    }

    //GENERATE DESIRED DOMAIN DATA HERE
    private static EwalletSandbox GenerateEwalletData()
    {
        var wallets = GenerateWallets();
        var userPermissions = GenerateUserPermissions(wallets);
        var walletsSubscriptions = GenerateCreateSubscriptionRequest(wallets);
        var terms = GenerateTerms();
        var transactions = GenerateTransactions(wallets);
        var walletsPermissions = new List<SandboxWalletPermission>();
        var walletAuthorizationRequests = new List<WalletAuthorizationResponse>();
        var limits = GenerateLimits(walletsPermissions, userPermissions);
        var accounts = GenerateAvailableAccounts(wallets);

        return new EwalletSandbox()
        {
            Wallets = wallets,
            UserPermissions = userPermissions,
            WalletsSubscriptions = walletsSubscriptions,
            Terms = terms,
            Transactions = transactions,
            WalletAuthorizationRequests = walletAuthorizationRequests,
            WalletsPermissions = walletsPermissions,
            Limits = limits,
            AvailableAccounts = accounts
        };
    }
}
