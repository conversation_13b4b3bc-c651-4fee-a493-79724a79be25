sequenceDiagram
    autonumber
    Client->>EWallet.Proxy: /wallet/[walletId]/my-transactions GET
    EWallet.Proxy->>EWallet.Core: /wallet/[walletId]/my-transactions GET
    EWallet.Core->>EWallet.DB: Query Transactions Table
    note over EWallet.DB: DB Table:  Transactions
    EWallet.DB->>EWallet.Core: return retrieved Transactions if any
    EWallet.Core->>EWallet.Proxy: return constructed List<Transaction>
    EWallet.Proxy->>Client: return List<Transaction> response