﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <DeleteExistingFiles>True</DeleteExistingFiles>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <EnvironmentName>Production</EnvironmentName>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <PublishProvider>FileSystem</PublishProvider>
    <PublishUrl>C:\publish\Production\nbg.ewallet.core.api</PublishUrl>
    <WebPublishMethod>FileSystem</WebPublishMethod>
    <SiteUrlToLaunchAfterPublish />
    <TargetFramework>net8.0</TargetFramework>
    <ProjectGuid>2ca5a744-a80e-4a06-8086-e7775366186c</ProjectGuid>
    <SelfContained>false</SelfContained>
  </PropertyGroup>

  <ItemGroup>
    <Content Update="appsettings.*.json" CopyToPublishDirectory="Never" />
    <Content Update="nlog.*.config" CopyToPublishDirectory="Never" />

    <ResolvedFileToPublish Include="appsettings.Production.json">
      <RelativePath>appsettings.Production.json</RelativePath>
    </ResolvedFileToPublish>

    <ResolvedFileToPublish Include="nlog.production.config">
      <RelativePath>nlog.production.config</RelativePath>
    </ResolvedFileToPublish>

  </ItemGroup>
</Project>
