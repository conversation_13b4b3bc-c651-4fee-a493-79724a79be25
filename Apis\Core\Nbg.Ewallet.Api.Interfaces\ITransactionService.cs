﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Interfaces;

public interface ITransactionService
{
    Task<TResponse> ExecuteAsync<TRequest, TResponse, TCommissionResponse>(ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider, TRequest request, string walletId);
    Task<Transaction> ApproveAsync(Guid walletId, string transactionId);
    Task<Transaction> RejectAsync(Guid walletId, string transactionId);
    Task<TransactionSubType> GetTransactionSubTypeAsync(string receiverAccount);
    Task<TCommissionResponse> GetCommissionAsync<TRequest, TResponse, TCommissionResponse>(ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider, TRequest request, Guid walletId);
    Task<TResponse> ExecuteForecastAsync<TRequest, TResponse, TCommissionResponse>(ITransactionRequestProvider<TRequest, TResponse, TCommissionResponse> provider, TRequest request, string walletId);
}
