﻿using System;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using Nbg.Ewallet.Api.Types.TimeConverters;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Represents a financial transaction statement.
/// </summary>
[DataContract]
public class Statement
{
    /// <summary>
    /// The serial number of this entry.
    /// </summary>
    [DataMember(Name = "serialNum")]
    public string SerialNum { get; set; }

    /// <summary>
    /// The date of this transaction.
    /// </summary>
    [JsonConverter(typeof(SystemTextJsonDateConverter))]
    [DataMember(Name = "date")]
    public DateTime Date { get; set; }

    /// <summary>
    /// The bank's branch.
    /// </summary>
    [DataMember(Name = "branch")]
    public string Branch { get; set; }

    /// <summary>
    /// Code for transaction type, e.g., "10" for "Κατάθεση".
    /// </summary>
    [DataMember(Name = "trans")]
    public string Trans { get; set; }

    /// <summary>
    /// Description of the transaction type.
    /// </summary>
    [DataMember(Name = "transDescription")]
    public string TransDescription { get; set; }

    /// <summary>
    /// The amount of this transaction in the currency of the transaction.
    /// </summary>
    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }

    /// <summary>
    /// The currency of the transaction.
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>
    /// The amount of this transaction in the account's currency.
    /// </summary>
    [DataMember(Name = "amountEquivalent")]
    public decimal AmountEquivalent { get; set; }

    /// <summary>
    /// Indicates whether the transaction is a "Credit" or "Debit".
    /// </summary>
    [DataMember(Name = "creditDebit")]
    public string CreditDebit { get; set; }

    /// <summary>
    /// The date when the transaction amount was debited or credited.
    /// </summary>
    [JsonConverter(typeof(SystemTextJsonDateConverter))]
    [DataMember(Name = "valeur")]
    public DateTime Valeur { get; set; }

    /// <summary>
    /// A description of this transaction.
    /// </summary>
    [DataMember(Name = "description")]
    public string Description { get; set; }

    /// <summary>
    /// The accounting balance after this transaction.
    /// </summary>
    [DataMember(Name = "accountingBalance")]
    public decimal AccountingBalance { get; set; }

    /// <summary>
    /// The reference number of the transaction, which is unique when combined with "externalSystem".
    /// </summary>
    [DataMember(Name = "reference")]
    public string Reference { get; set; }

    /// <summary>
    /// Indicates the system where the remittance is sent: "RNB" for NBG, "ROT" for other banks.
    /// </summary>
    [DataMember(Name = "externalSystem")]
    public string ExternalSystem { get; set; }

    /// <summary>
    /// Related account number, which varies based on whether the transaction is a debit or credit.
    /// </summary>
    [DataMember(Name = "relatedAccount")]
    public string RelatedAccount { get; set; }

    /// <summary>
    /// Name of the counterparty.
    /// </summary>
    [DataMember(Name = "relatedName")]
    public string RelatedName { get; set; }

    /// <summary>
    /// The full date and time of the transaction, precise to milliseconds.
    /// </summary>
    [JsonConverter(typeof(SystemTextJsonDateTimeConverter))]
    [DataMember(Name = "timestamp")]
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// The account of the user that initiated the transaction.
    /// </summary>
    [DataMember(Name = "account")]
    public string Account { get; set; }

    /// <summary>
    /// Name of the counterparty bank.
    /// </summary>
    [DataMember(Name = "counterpartyBank")]
    public string CounterpartyBank { get; set; }

    /// <summary>
    /// Additional information related to the transaction.
    /// </summary>
    [DataMember(Name = "additionalInfo")]
    public string AdditionalInfo { get; set; }
}
