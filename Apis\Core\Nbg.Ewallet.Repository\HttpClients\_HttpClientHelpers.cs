﻿using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Repository.HttpClients;

public static class HttpClientHelpers
{
    public static Request<TPayload> Wrap<TPayload>(this TPayload payload) where TPayload : new()
    {
        return new Request<TPayload>
        {
            Header = new RequestHeader { ID = Guid.NewGuid().ToString() },
            Payload = payload
        };
    }

    private static readonly JsonSerializerOptions _jsonSerializerOptions = new()
    {
        WriteIndented = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };

    static HttpClientHelpers()
    {
        _jsonSerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase, allowIntegerValues: true));
    }
    /// <summary>
    /// </summary>
    /// <typeparam name="TPayload"></typeparam>
    /// <param name="request"></param>
    /// <param name="url"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static HttpRequestMessage ToHttpRequestMessage<TPayload>(this Request<TPayload> request, string url) where TPayload : new()
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(url, nameof(url));

        return new HttpRequestMessage(HttpMethod.Post, url)
        {
            Content = new StringContent(JsonSerializer.Serialize(request, _jsonSerializerOptions), Encoding.UTF8, "application/json")
        };
    }

    public static HttpRequestMessage ToHttpRequestMessage<TPayload>(this TPayload request, string url) where TPayload : new()
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(url, nameof(url));

        return new HttpRequestMessage(HttpMethod.Post, url)
        {
            Content = new StringContent(JsonSerializer.Serialize(request, _jsonSerializerOptions), Encoding.UTF8, "application/json")
        };
    }
}
