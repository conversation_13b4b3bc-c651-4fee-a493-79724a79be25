﻿using System;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Implementation.Extensions;

public static class LimitExtension
{
    public static RepoLimit ToRepoLimit(this Limit @this, Guid PermissionId)
    {
        return new RepoLimit
        {
            Amount = @this.Amount,
            TimeFrame = @this.Timeframe,
            TransactionType = @this.TransactionType,
            Id = @this.Id,
            PermissionId = PermissionId
        };
    }
}
