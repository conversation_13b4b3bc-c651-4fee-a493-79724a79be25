﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

public class GetMassPaymentResponse
{
    /// <summary>
    /// Debtor Name
    /// </summary>
    [DataMember(Name = "debtorName")]
    public string DebtorName { get; set; }

    /// <summary>
    /// Debtor Iban
    /// </summary>
    [DataMember(Name = "debtorIban")]
    public string DebtorIban { get; set; }

    /// <summary>
    /// Debtor Telephone
    /// </summary>
    [DataMember(Name = "debtorTelephone")]
    public string DebtorTelephone { get; set; }

    /// <summary>
    /// List of Payments
    /// </summary>
    [DataMember(Name = "payments")]
    public List<Payment> Payments { get; set; }

    /// <summary>
    /// Mass Transaction Id
    /// </summary>
    [DataMember(Name = "massTransactionId")]
    public Guid MassTransactionId { get; set; }
}
