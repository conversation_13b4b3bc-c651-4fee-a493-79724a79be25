using System;
using System.Collections.Concurrent;

public static class AuthorizationRuleCache
{
    private static readonly ConcurrentDictionary<Enum, IAuthorizationRule> _ruleCache = new();

    public static IAuthorizationRule GetRule(Enum permission)
    {
        return _ruleCache.GetOrAdd(permission, p =>
        {
            var typeName = $"{p}AuthorizationRule";
            var type = Type.GetType(typeName)
            ?? throw new InvalidOperationException($"AuthorizationRule type not found: {typeName}");

            var instance = Activator.CreateInstance(type) as IAuthorizationRule
            ?? throw new InvalidCastException($"Type {typeName} does not implement IAuthorizationRule");

            return instance;
        });
    }
}
