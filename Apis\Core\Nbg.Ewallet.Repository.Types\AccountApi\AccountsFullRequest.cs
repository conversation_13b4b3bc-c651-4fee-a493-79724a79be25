﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

/// <summary>
/// Request to get list of user's accounts
/// </summary>
[DataContract]
[Serializable]
public class AccountsFullRequest
{
    /// <summary>
    /// Username of the logged in user
    /// </summary>
    [Required]
    [DataMember(Name = "userId")]
    public string UserID { get; set; }

    /// <summary>
    /// Connected user id
    /// </summary>
    [DataMember(Name = "connectedUserId")]
    public string ConnectedUserID { get; set; }

    /// <summary>
    /// User own accounts
    /// </summary>
    [DataMember(Name = "ownAccounts")]
    public bool? OwnAccounts { get; set; }

    /// <summary>Indicator if user is corporate </summary>
    [DataMember(Name = "isCorporateUser")]
    public bool IsCorporateUser { get; set; }

    /// <summary>Include list of Beneficiaries for each account</summary>
    [DataMember(Name = "includeBeneficiaries")]
    public bool IncludeBeneficiaries { get; set; }

    /// <summary>Return info only for the specified Account (AccountNumber / IBAN)</summary>
    [DataMember(Name = "account")]
    public string Account { get; set; }

    /// <summary>
    /// IncludeStatements
    /// </summary>
    [DataMember(Name = "includeStatements")]
    public bool IncludeStatements { get; set; }

    /// <summary>
    /// If true , CanConnectToLoan flag in AccountsFullResponse is returned true or false
    /// </summary>
    [DataMember(Name = "checkConnectToLoan")]
    public bool CheckConnectToLoan { get; set; }
}
