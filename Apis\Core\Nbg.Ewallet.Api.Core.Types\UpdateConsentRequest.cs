﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Authorize / Reject consent object
/// </summary>
[DataContract]
public class UpdateConsentRequest
{
    /// <summary>
    /// User id
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserID { get; set; }

    /// <summary>
    /// Consent id
    /// </summary>
    [DataMember(Name = "consentId")]
    public string ConsentId { get; set; }

    /// <summary>
    /// IBAN
    /// </summary>
    [DataMember(Name = "iban")]
    public string Iban { get; set; }

    /// <summary>
    /// WalletName
    /// </summary>
    [DataMember(Name = "walletName")]
    public string WalletName { get; set; }

    /// <summary>
    /// TAN number
    /// </summary>
    [DataMember(Name = "tanNumber")]
    public string TanNumber { get; set; }

    public bool? IsSmsOtp { get; set; } = true;

    public string GetCategory() => "nbg.ewallet.consents.updateconsent";
}
