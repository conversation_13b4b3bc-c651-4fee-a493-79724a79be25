﻿using System;

namespace Nbg.Ewallet.Repository.Types.Extensions;

public static class StringExtension
{
    public static bool TrimEqual(this string a, string b)
    {
        if (ReferenceEquals(a, b)) return true;
        if (a == null || b == null) return false;

        ReadOnlySpan<char> spanA = a;
        ReadOnlySpan<char> spanB = b;

        var endA = spanA.Length - 1;
        var endB = spanB.Length - 1;

        while (endA >= 0 && char.IsWhiteSpace(spanA[endA])) endA--;
        while (endB >= 0 && char.IsWhiteSpace(spanB[endB])) endB--;

        if (endA != endB) return false;

        for (var i = 0; i <= endA; i++)
        {
            if (spanA[i] != spanB[i]) return false;
        }

        return true;
    }
}
