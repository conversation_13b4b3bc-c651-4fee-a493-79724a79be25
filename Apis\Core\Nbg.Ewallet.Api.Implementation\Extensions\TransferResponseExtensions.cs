﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Api.Implementation.Extensions;

public static class TransferResponseExtensions
{
    public static async Task HandleResultAsync(this Response<TransfersResponseBase> response, RepoTransaction transaction, ITransactionRepositoryService transactionRepositoryService)
    {
        if (response.Exception != null)
        {
            transaction.UpdateTransactionFailure(response.Exception.Description);
            _ = await transactionRepositoryService.SaveAsync(transaction);

            throw response.Exception.Code switch
            {
                "020" => new TransferExecutionExceptionInput(response.Exception.Description),
                "728" or "067" => new TransferExecutionExceptionBalance(response.Exception.Description),
                _ => new TransferExecutionException(response.Exception.Description),
            };
        }

        transaction.UpdateTransactionSuccess();
        transaction.Reference = response.Payload.ReferenceNumber;
        transaction.DebtorName = response.Payload.DebtorName ?? transaction.DebtorName;
        transaction.Valeur = response.Payload.Valeur;
        transaction.Commission = response.Payload.SumComissionOut;
        transaction.CreditorName = response.Payload.HolderName ?? transaction.CreditorName;
        await transactionRepositoryService.SaveAsync(transaction);
    }

    public static async Task<TransfersResponse> HandleResultNoExceptionAsync(this Response<TransfersResponseBase> response, TransfersRequest transfer, RepoTransaction transaction, ITransactionRepositoryService transactionRepositoryService)
    {
        TransfersResponse transferResponse;
        if (response.Exception != null)
        {
            transaction.UpdateTransactionFailure(response.Exception.Description);
            _ = await transactionRepositoryService.SaveAsync(transaction);

            transferResponse = transfer.EnrichResponse(new TransfersResponse());
            transferResponse.ErrorReason = response.Exception.Description;
            transferResponse.TransactionId = transaction.TransactionId;
            return transferResponse;
        }
        transaction.UpdateTransactionSuccess();
        transaction.Reference = response.Payload.ReferenceNumber;
        transaction.Valeur = response.Payload.Valeur;
        transaction.DebtorName = response.Payload.DebtorName ?? transaction.DebtorName;
        transaction.Commission = response.Payload.SumComissionOut;
        await transactionRepositoryService.SaveAsync(transaction);
        transferResponse = response.Payload.ToTransfersResponse();
        transferResponse.TransactionId = transaction.TransactionId;
        return transferResponse;
    }

    public static async Task<TransfersResponse> HandleCommissionExceptionAsync(this TransfersRequest transfer, Exception exception, RepoTransaction transaction, ITransactionRepositoryService transactionRepositoryService)
    {
        transaction.UpdateTransactionFailure(exception.Message);
        _ = await transactionRepositoryService.SaveAsync(transaction);

        var transferResponse = transfer.EnrichResponse(new TransfersResponse());
        transferResponse.ErrorReason = exception.Message;
        return transferResponse;
    }
}
