﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Implementation;

public class WalletAuthorizationRequestsService : IWalletAuthorizationRequestsService
{
    private readonly ILogger<WalletAuthorizationRequestsService> _logger;
    private readonly IMapper _mapper;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly IWalletPermissionsRepositoryService _walletPermissionsRepositoryService;
    private readonly IAuthorizationRepositoryService _authorizationRepositoryService;

    public WalletAuthorizationRequestsService(
        ILogger<WalletAuthorizationRequestsService> logger,
        IMapper mapper,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletRepositoryService walletRepositoryService,
        IWalletPermissionsRepositoryService walletPermissionsRepositoryService,
        IAuthorizationRepositoryService authorizationRepositoryService
    )
    {
        _logger = logger;
        _mapper = mapper;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletRepositoryService = walletRepositoryService;
        _walletPermissionsRepositoryService = walletPermissionsRepositoryService;
        _authorizationRepositoryService = authorizationRepositoryService;
    }

    public async Task<WalletAuthorizationResponse> RequestAuthorization(Guid walletId, WalletAuthorizationRequest request)
    {
        var userId = _httpContextRepositoryService.GetUserId();

        var pendingAuthRequests = await _authorizationRepositoryService.RepoAuthorizationRequestFindAllByRequestorWalletIdAndTargetWalletIdAndStatusAsync(
            walletId,
            request.TargetWalletId,
            RequestStatus.Pending);

        if (pendingAuthRequests.Count() != 0)
        {
            _logger.LogError("User {UserId} already has a pending authorization request for wallet {WalletId} to wallet {TargetWalletId}", userId, walletId,
                request.TargetWalletId);
            throw new AuthorizationRequestExistsException();
        }

        //Check that targetwalletId exists
        var targetWallet = await _walletRepositoryService.FindOneByIdAsync(request.TargetWalletId.ToString());

        //Check that there is not already permission for these two wallets
        // The targetWalletId for the Authorization Request is the source wallet id for Wallet Permissions table.
        // The wallet Id is the Target Wallet Id for the Wallet permissions table
        var currentPermissions = await _walletPermissionsRepositoryService.FindAllActiveByWalletIdAndTargetWalletIdAsync(targetWallet.WalletId, walletId);
        if (currentPermissions.Count() > 0)
        {
            _logger.LogError("User {UserId} already has a permission for wallet {WalletId} to wallet {TargetWalletId}", userId, walletId, targetWallet.WalletId);
            throw new PermissionAlreadyExistsException();
        }

        //Check that I am not asking permission to my own wallet
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);
        if (request.TargetWalletId == myWallet.WalletId)
        {
            _logger.LogError("User {UserId} is trying to request authorization to his own wallet {WalletId}", userId, myWallet.WalletId);
            throw new OwnWalletAuthorizationException();
        }

        var repoAuthRequest = new RepoAuthorizationRequest()
        {
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow,
            Id = Guid.NewGuid(),
            Status = RequestStatus.Pending,
            TargetWalletId = request.TargetWalletId,
            RequestorWalletId = walletId
        };

        // add request authorization to db
        await _authorizationRepositoryService.RepoAuthorizationRequestSaveAsync(repoAuthRequest);

        var targetWalletAuth = _mapper.Map<TargetWalletAuthorization>(repoAuthRequest);
        targetWalletAuth.OrganizationName = targetWallet.OrganizationName;
        targetWalletAuth.WalletName = targetWallet.WalletName;
        targetWalletAuth.VatNumber = targetWallet.VatNumber;
        targetWalletAuth.WalletAccount = targetWallet.WalletAccount;

        var authorizationResponse = new WalletAuthorizationResponse
        {
            Id = repoAuthRequest.Id,
            CreatedAt = repoAuthRequest.CreatedAt,
            UpdatedAt = repoAuthRequest.UpdatedAt,
            ExpiresAt = repoAuthRequest.ExpiresAt,
            Status = repoAuthRequest.Status,
            RequestorWallet = new RequestorWalletAuthorization
            {
                OrganizationName = myWallet.OrganizationName,
                VatNumber = myWallet.VatNumber,
                WalletAccount = myWallet.WalletAccount,
                WalletId = myWallet.WalletId,
                WalletName = myWallet.WalletName,
            },
            TargetWallet = targetWalletAuth
        };
        return authorizationResponse;
    }

    public async Task<List<WalletAuthorizationResponse>> GetAuthorizationRequests(Guid walletId, RequestStatus? status)
    {
        var authorizationRequests = await _authorizationRepositoryService.RepoAuthorizationRequestFindAllByWalletIdAndRequestStatusAsync(walletId, status);

        var response = new List<WalletAuthorizationResponse>();
        foreach (var authorizationRequest in authorizationRequests)
        {
            //TODO: maybe change to use join.
            var targetWallet = await _walletRepositoryService.FindOneByIdAsync(authorizationRequest.TargetWalletId.ToString());
            var requestorWallet = await _walletRepositoryService.FindOneByIdAsync(authorizationRequest.RequestorWalletId.ToString());
            var walletAuthorizationResponse = new WalletAuthorizationResponse
            {
                Id = authorizationRequest.Id,
                CreatedAt = authorizationRequest.CreatedAt,
                ExpiresAt = authorizationRequest.ExpiresAt,
                Status = authorizationRequest.Status,
                UpdatedAt = authorizationRequest.UpdatedAt,
                RequestorWallet = new RequestorWalletAuthorization
                {
                    OrganizationName = requestorWallet.OrganizationName,
                    VatNumber = requestorWallet.VatNumber,
                    WalletAccount = requestorWallet.WalletAccount,
                    WalletId = requestorWallet.WalletId,
                    WalletName = requestorWallet.WalletName,
                },
                TargetWallet = new TargetWalletAuthorization
                {
                    OrganizationName = targetWallet.OrganizationName,
                    VatNumber = targetWallet.VatNumber,
                    WalletAccount = targetWallet.WalletAccount,
                    WalletId = targetWallet.WalletId,
                    WalletName = targetWallet.WalletName,
                }
            };
            response.Add(walletAuthorizationResponse);
        }

        return response;
    }

    public async Task<WalletAuthorizationResponse> RejectAuthorizationRequest(Guid walletId, Guid authRequestId)
    {
        var userId = _httpContextRepositoryService.GetUserId();

        var authRequest = await _authorizationRepositoryService.RepoAuthorizationRequestFindOneByWalletIdAndAuthorizationRequestId(walletId, authRequestId)
            ?? throw new RequestNotFoundException();

        //Check that we are the targetwalletid of the request authoriazation
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);
        if (myWallet.WalletId != authRequest.TargetWalletId)
        {
            throw new InsufficientUserRightsException();
        }

        var requestorWallet = await _walletRepositoryService.FindOneByIdAsync(authRequest.RequestorWalletId.ToString());

        authRequest.Status = RequestStatus.Rejected;
        authRequest.UpdatedAt = DateTime.UtcNow;

        //Update Authorization Request status to rejected
        await _authorizationRepositoryService.RepoAuthorizationRequestUpdateAuthorizationRequestAsync(authRequest);

        //Return WalletAuthorizationResponse
        var requestWalletAuthorization = _mapper.Map<RequestorWalletAuthorization>(authRequest);
        requestWalletAuthorization.WalletAccount = requestorWallet.WalletAccount;
        requestWalletAuthorization.VatNumber = requestorWallet.VatNumber;
        requestWalletAuthorization.WalletName = requestorWallet.WalletName;
        requestWalletAuthorization.OrganizationName = requestorWallet.OrganizationName;

        return new WalletAuthorizationResponse
        {
            Id = authRequest.Id,
            CreatedAt = authRequest.CreatedAt,
            Status = authRequest.Status,
            ExpiresAt = authRequest.ExpiresAt,
            UpdatedAt = authRequest.UpdatedAt,
            RequestorWallet = requestWalletAuthorization,
            TargetWallet = new TargetWalletAuthorization
            {
                OrganizationName = myWallet.OrganizationName,
                WalletAccount = myWallet.WalletAccount,
                VatNumber = myWallet.VatNumber,
                WalletName = myWallet.WalletName,
                WalletId = myWallet.WalletId
            }
        };
    }
}
