﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Wallet;

/// <summary>
/// Consent response
/// </summary>
[DataContract]
public class CreateConsentResponse
{
    /// <summary>
    /// Consent id
    /// </summary>
    [DataMember(Name = "consentId")]
    public string ConsentId { get; set; }

    /// <summary>
    /// Consent status
    /// </summary>
    [DataMember(Name = "status")]
    public string Status { get; set; }

    /// <summary>
    /// Consent authorization url
    /// </summary>
    [DataMember(Name = "authorizationUrl")]
    public string AuthorizationUrl { get; set; }
}
