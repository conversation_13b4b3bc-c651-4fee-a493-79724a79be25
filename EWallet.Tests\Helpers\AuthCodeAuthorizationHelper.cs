﻿using System.Collections.Concurrent;
using System.Reflection;
using System.Text.Json;
using System.Web;
using IntegrationTests.Helpers;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using SeleniumExtras.WaitHelpers;


namespace specflow.tests.Helpers;

public static class AuthCodeAuthorizationHelper
{
    private static readonly HttpClient _authCodeHttpClient = new HttpClient();

    private static readonly ConcurrentDictionary<string, string> _scenarioAuthCodes = [];

    private static readonly ConcurrentDictionary<string, AutoResetEvent> _scenarioSignals = [];

    private static readonly ConcurrentDictionary<string, string> _tokens = [];

    private static readonly CallbackListener AuthCodeCallbackListener = CallbackListener.Create(AuthorizationConstants.RedirectUri, context =>
    {
        var qs = HttpUtility.ParseQueryString(context.Request.RawUrl);
        var authCode = qs["/callback/?code"];
        var scenarioId = qs["state"];

        _scenarioAuthCodes[scenarioId] = authCode;
        _scenarioSignals[scenarioId].Set();

        context.Response.StatusCode = 200;
        context.Response.StatusDescription = "OK";
        context.Response.Close();
    });

    public static async Task<string> GetAuthCodeAuthorizationHeaderAsync(string userName, string passWord)
    {
        _tokens.TryGetValue(userName, out var existingToken);
        if (existingToken != null)
        {
            return existingToken;
        }

        AuthCodeCallbackListener.AddTask();

        var scenarioId = Guid.NewGuid().ToString();
        _scenarioAuthCodes[scenarioId] = string.Empty;
        _scenarioSignals[scenarioId] = new AutoResetEvent(false);

        IWebDriver? webDriver = null;
        ChromeDriverService? chromeDriverService = null;

        try
        {
            var idsUrl = CreateNavigationUrl(scenarioId, null);

            var chromeDriverPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            chromeDriverService = ChromeDriverService.CreateDefaultService(chromeDriverPath);
            chromeDriverService.HideCommandPromptWindow = true;
            chromeDriverService.SuppressInitialDiagnosticInformation = true;

            var options = new ChromeOptions();
            options.AddArguments("incognito");
            //options.AddArguments("headless");

            webDriver = new ChromeDriver(chromeDriverService, options, TimeSpan.FromMinutes(2000))
            {
                Url = idsUrl
            };

            ExecuteLogin(webDriver, userName, passWord);

        }
        catch (Exception ex)
        {
            webDriver?.Close();
            chromeDriverService?.Dispose();
            throw;
        }

        _scenarioSignals[scenarioId].WaitOne(TimeSpan.FromSeconds(5));

        webDriver.Close();
        chromeDriverService.Dispose();

        var authCode = _scenarioAuthCodes[scenarioId];
        if (authCode?.Trim() == null)
            throw new Exception("Could not get AuthCode");

        using var request = GetAuthCodeRequestMessage(authCode);
        using var response = await _authCodeHttpClient.SendAsync(request);
        var content = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode) throw new Exception(content);

        using var doc = JsonDocument.Parse(content);
        var root = doc.RootElement;

        if (!root.TryGetProperty("access_token", out var tokenElement))
        {
            throw new Exception("Could not get IdentityServer token (null token)");
        }

        var token = tokenElement.GetString();

        if (token?.Trim() == null)
            throw new Exception("Could not get IdentityServer token (null token)");

        _scenarioSignals[scenarioId].Close();
        _scenarioSignals.TryRemove(scenarioId, out _);
        _scenarioAuthCodes.TryRemove(scenarioId, out _);
        _tokens.TryAdd(userName, token);
        return token;
    }

    private static HttpRequestMessage GetAuthCodeRequestMessage(string authCode)
    {
        var nameValueCollection = new Dictionary<string, string>
        {
            {"client_id", AuthorizationConstants.ClientId},
            {"client_secret", AuthorizationConstants.ClientSecret},
            {"scope", $"{AuthorizationConstants.Scope} {AuthorizationConstants.ManagementScope}"},
            {"grant_type", "authorization_code"},
            {"code", authCode},
            {"cache-control", "no-cache"},
            {"redirect_uri", AuthorizationConstants.RedirectUri}
        };

        return new HttpRequestMessage(HttpMethod.Post, AuthorizationConstants.TokenEndpoint)
        {
            Content = new FormUrlEncodedContent(nameValueCollection)
        };
    }

    private static string CreateNavigationUrl(string scenarioId)
    {
        var idsUrl = $@"{AuthorizationConstants.AuthorizationUrl}?client_id={AuthorizationConstants.ClientId}&response_type=code&scope={AuthorizationConstants.Scope}&state={scenarioId}&redirect_uri={AuthorizationConstants.RedirectUri}";
        return idsUrl;
    }

    private static string CreateNavigationUrl(string scenarioId, string? consentId)
    {
        if (consentId?.Trim() == null)
            return CreateNavigationUrl(scenarioId);
        var idsUrl = $@"{AuthorizationConstants.AuthorizationUrl}?consent_id={consentId}&client_id={AuthorizationConstants.ClientId}&response_type=code&scope={AuthorizationConstants.Scope}&state={scenarioId}&redirect_uri=https://79.131.254.229:10031/callback/";
        return idsUrl;
    }

    private static void ExecuteLogin(IWebDriver webDriver, string userId, string passWord)
    {
        const string ibbutton = @"/html/body/index-page/div/div/div[2]/sign-in/div/div/sign-in-form/div/div/div[4]/div[4]/div/div[1]/div";
        var wait = new WebDriverWait(webDriver, TimeSpan.FromSeconds(5));
        var ibElement = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath(ibbutton)));
        ibElement.Click();

        const string userNameElementPath = @"/html/body/index-page/div/div/div[2]/ibank-sign-in/div/div/div/div/div/div[2]/div[1]/div/material-input/div[2]/input";
        //const string userNameElementPath = @"/html/body/index-page/div/div/div[2]/sign-in/div/div/sign-in-form/div/div/div[4]/div[2]/div[1]/div/material-input/div[2]/input";
        const string passwordElementPath = @"/html/body/index-page/div/div/div[2]/ibank-sign-in/div/div/div/div/div/div[2]/div[2]/div/material-input/div[2]/input";
        //const string passwordElementPath = @"/html/body/index-page/div/div/div[2]/sign-in/div/div/sign-in-form/div/div/div[4]/div[2]/div[2]/div/material-input/div[2]/input";
        //const string loginButtonElementPath = @"/html/body/index-page/div/div/div[2]/sign-in/div/div/sign-in-form/div/div/div[4]/div[2]/button";
        const string loginButtonElementPath = @"/ html/body/index-page/div/div/div[2]/ibank-sign-in/div/div/div/div/div/div[2]/button";

        const string agreeButtonXPath = "/html/body/index-page/div/div/div[2]/consents-form/div/div/div/div[2]/div[2]/div[2]/button";
        var usernameElement = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath(userNameElementPath)));
        usernameElement.Click();
        usernameElement.Clear();
        usernameElement.SendKeys(userId);

        var passwordElement = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath(passwordElementPath)));
        passwordElement.Click();
        passwordElement.Clear();
        passwordElement.SendKeys(passWord);

        var loginButtonElement = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath(loginButtonElementPath)));
        loginButtonElement.Click();

        try
        {
            var agreeWait = new WebDriverWait(webDriver, TimeSpan.FromSeconds(2));
            var agreeButtonElement = agreeWait.Until(ExpectedConditions.ElementToBeClickable(By.XPath(agreeButtonXPath)));
            var agreeElement = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath(agreeButtonXPath)));
            agreeElement.Click();
        }
        catch (Exception) { }
    }

    private static void MouseClick(IWebDriver webDriver, IWebElement element)
    {
        var executor = (IJavaScriptExecutor)webDriver;
        executor.ExecuteScript("arguments[0].click();", element);
    }
}
