﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
///
/// </summary>
public class BatchPaymentsCommissionRequest
{
    /// <summary>
    /// Mass Transaction Id
    /// </summary>
    [DataMember(Name = "batchId")]
    public Guid BatchId { get; set; }

    /// <summary>
    /// List of payment requests
    /// </summary>
    [DataMember(Name = "batch")]
    public List<ExtendedPaymentsRequest> Batch { get; set; }
}
