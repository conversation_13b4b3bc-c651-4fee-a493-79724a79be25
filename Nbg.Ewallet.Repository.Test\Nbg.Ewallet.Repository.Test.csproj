﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Library</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <LangVersion>latest</LangVersion>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.18" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="Nbg.AspNetCore.Http.Extensions" Version="8.0.5.1" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
        <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />

    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Apis\Core\Nbg.Ewallet.Api.Controllers\Nbg.Ewallet.Api.Controllers.csproj" />
        <ProjectReference Include="..\Apis\Core\Nbg.Ewallet.Api.Core.Types\Nbg.Ewallet.Api.Types.csproj" />
        <ProjectReference Include="..\Apis\Core\Nbg.Ewallet.Repository.Interfaces\Nbg.Ewallet.Repository.Interfaces.csproj" />
        <ProjectReference Include="..\Apis\Core\Nbg.Ewallet.Repository\Nbg.Ewallet.Repository.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
