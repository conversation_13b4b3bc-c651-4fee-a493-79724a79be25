﻿using AutoMapper;
using nbg.netcore.consent.repository.types;
using nbg.netcore.consent.types;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.NetCore.HttpExceptions;
using Nbg.NetCore.Utilities;
using Nbg.OpenBanking.Utilities;
using System.Threading.Tasks;

namespace Nbg.Ewallet.Api.Implementation;

public partial class ManagementService : IManagementService
{
    private readonly IMapper _mapper;
    private readonly IMainFrameConnector _mainFrameConnector;
    private readonly IConsentsRepositoryService _consentsRepositoryService;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly IUserPermissionsRepositoryService _userPermissionsRepositoryService;
    private readonly ICreateAccountService _createAccountService;
    private readonly ICorporateApiClientService _corporateApiClientService;
    private readonly ISubscriptionApiClientService _subscriptionApiClientService;
    private readonly IValidationService _validationService;

    public ManagementService(
        IMapper mapper,
        IMainFrameConnector mainFrameConnector,
        IConsentsRepositoryService consentsRepositoryService,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletRepositoryService walletRepositoryService,
        IUserPermissionsRepositoryService userPermissionsRepositoryService,
        ICreateAccountService createAccountService,
        ICorporateApiClientService corporateApiClientService,
        ISubscriptionApiClientService subscriptionApiClientService,
        IValidationService validationService)
    {
        _mapper = mapper;
        _mainFrameConnector = mainFrameConnector;
        _consentsRepositoryService = consentsRepositoryService;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletRepositoryService = walletRepositoryService;
        _userPermissionsRepositoryService = userPermissionsRepositoryService;
        _createAccountService = createAccountService;
        _corporateApiClientService = corporateApiClientService;
        _subscriptionApiClientService = subscriptionApiClientService;
        _validationService = validationService;
    }

    public async Task<AccountsResponse> GetUserAccountsAsync(AccountsRequest request)
    {
        var customerCode = _httpContextRepositoryService.GetCustomerCode();
        var userId = _httpContextRepositoryService.GetUserId();

        //TODO Check if user already exists
        // var issuerExists = false;
        //
        // if (issuerExists) {
        //
        //     return new AccountsResponse() { ExistingIssuer = true };
        // }
        // #if DEBUG
        //             customerCode = "**********";
        //             //var customerCode = "**********"; // FP but not business
        //             //var customerCode = "**********"; // FP
        //             //customerCode = "**********";
        // #endif

        var customerData = await _mainFrameConnector.GetCustomerDataAsync(customerCode);
        var authCustomerData = await _mainFrameConnector.GetAuthorizationLevelAsync(userId);
        var customerProductDetails = await _mainFrameConnector.GetCustomerProductDetailsAsync(customerCode);
        var validatedCustomerData = await _validationService.ValidateCustomerDataAsync(customerData, customerProductDetails, authCustomerData);
        var response = _mapper.Map<AccountsResponse>(validatedCustomerData);

        return response;
    }

    public async Task<ConsentResponse> GetConsentAsync(RetrieveConsentRequest request)
    {
        if (request.ConsentId.Clear() == null)
        {
            throw new BadRequestException("Incorrect JSON Format");
        }

        var repoConsentFull = await _consentsRepositoryService.GetConsentAsync(request.ConsentId);
        var consent = _mapper.Map<EWalletConsent>(repoConsentFull);

        return ToConsentResponse(consent);
    }

    public async Task<ConsentResponse> AuthorizeConsentAsync(Types.Wallet.UpdateConsentRequest request)
    {
        ValidateRequest(request);
        var (customerCode, userId) = GetUserContext();
        var (customerData, authCustomerData, customerProductDetails) = await GetCustomerData(customerCode, userId);
        var validatedCustomerData = await ValidateCustomerData(customerData, customerProductDetails, authCustomerData);
        var consent = await GetAndValidateConsent(request.ConsentId);
        await CheckTaxIdAndWallets(userId, customerCode);
        CheckCustomerEligibility(validatedCustomerData);
        await CheckWalletExists(userId);
        var newWallet = await CreateAndSaveWallet(userId, consent, validatedCustomerData, customerData);
        await AssignAdminPermissions(userId, newWallet.WalletId);
        var account = await CreateAndConnectAccount(newWallet, customerCode, userId);
        await UpdateWalletAccount(newWallet.WalletId, account);
        var subscriptionResponse = await CreateSubscription(consent, newWallet.WalletId);
        return await UpdateConsentAndReturnResponse(request, consent);
    }


    public async Task<ConsentResponse> RejectConsentAsync(Types.Wallet.UpdateConsentRequest request)
    {
        if (request.ConsentId.Clear() == null)
        {
            throw new BadRequestException("Incorrect JSON Format");
        }

        var repoConsentFull = await _consentsRepositoryService.GetConsentAsync(request.ConsentId);
        var consent = _mapper.Map<EWalletConsent>(repoConsentFull);

        if (consent.Status != ConsentStatus.AwaitingAuthorization)
        {
            throw new BadRequestException($"Can not reject consent with status {consent.Status.EnumToString()}");
        }

        consent.Status = ConsentStatus.Rejected;
        var consentData = new EWalletConsentData { DefaultIban = request.Iban.Clear() ?? string.Empty };
        consent.ConsentData = consentData.GetSerializedObject();

        var updatingRepoConsent = _mapper.Map<RepoConsent>(consent);
        var updatedRepoConsentFull = await _consentsRepositoryService.UpdateConsentAsync(request.ConsentId, updatingRepoConsent);
        var updatedConsent = _mapper.Map<EWalletConsent>(updatedRepoConsentFull);

        return ToConsentResponse(updatedConsent);
    }

    private static ConsentResponse ToConsentResponse(EWalletConsent consent)
    {
        var extraData = consent.ExtraData.GetDeserializedObject<EWalletConsentExtraData>();
        consent.Data = consent.ConsentData.GetDeserializedObject<EWalletConsentData>();

        return new ConsentResponse
        {
            ConsentId = consent.Id.ToString(),
            SandboxId = extraData.SandboxId,
            DefaultIban = consent.Data.DefaultIban,
            WalletName = consent.Data.WalletName,
            PlanId = consent.Data.PlanId,
        };
    }
}
