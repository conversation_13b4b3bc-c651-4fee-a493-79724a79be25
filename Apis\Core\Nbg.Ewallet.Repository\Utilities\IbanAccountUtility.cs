﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Repository.Interfaces;

namespace Nbg.Ewallet.Repository.Utilities;

public class IbanAccountUtility : IIbanAccountUtility
{
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _expiration;
    private readonly IMainFrameConnector _accountApi;
    private readonly ILogger<IbanAccountUtility> _logger;

    public IbanAccountUtility(
        IMainFrameConnector mainFrameConnector,
        IMemoryCache memoryCache,
        ILogger<IbanAccountUtility> logger,
        TimeSpan? expiration = null)
    {
        _cache = memoryCache;
        _expiration = expiration ?? TimeSpan.FromHours(1);
        _accountApi = mainFrameConnector;
        _logger = logger;
    }

    public async Task<string> GetAccountFromIbanAsync(string iban)
    {
        ArgumentException.ThrowIfNullOrEmpty(iban, nameof(iban));

        iban = iban.Trim();
        var ibanKey = GetIbanKey(iban);

        if (_cache.TryGetValue<string>(ibanKey, out var account))
        {
            _logger.LogDebug("Cache hit for IBAN: {IBAN}", iban);
            return account;
        }

        var keyLock = GetOrCreateLock(ibanKey);
        await keyLock.WaitAsync();
        try
        {
            if (_cache.TryGetValue(ibanKey, out account))
            {
                _logger.LogDebug("Cache hit after wait for IBAN: {IBAN}", iban);
                return account;
            }

            _logger.LogDebug("Cache miss, calling API for IBAN: {IBAN}", iban);
            account = await _accountApi.GetAccountFromIBAN(iban);

            _cache.Set(ibanKey, account, _expiration);
            _cache.Set(GetAccountKey(account), iban, _expiration);

            return account;
        }
        finally
        {
            keyLock.Release();
        }
    }

    public async Task<string> GetIbanFromAccountAsync(string account)
    {
        ArgumentException.ThrowIfNullOrEmpty(account, nameof(account));

        account = account.Trim();
        var accountKey = GetAccountKey(account);
        if (_cache.TryGetValue<string>(accountKey, out var iban))
        {
            _logger.LogDebug("Cache hit for Account: {Account}", account);
            return iban;
        }

        var keyLock = GetOrCreateLock(accountKey);
        await keyLock.WaitAsync();
        try
        {
            if (_cache.TryGetValue(accountKey, out iban))
            {
                _logger.LogDebug("Cache hit after wait for Account: {Account}", account);
                return iban;
            }

            _logger.LogDebug("Cache miss, calling API for Account: {Account}", account);
            iban = await _accountApi.GetIBANFromAccount(account);

            _cache.Set(accountKey, iban, _expiration);
            _cache.Set(GetIbanKey(iban), account, _expiration);

            return iban;
        }
        finally
        {
            keyLock.Release();
        }
    }

    private static string GetIbanKey(string iban) => $"IBAN:{iban}";
    private static string GetAccountKey(string account) => $"account:{account}";
    private SemaphoreSlim GetOrCreateLock(string key)
    {
        return _cache.GetOrCreate($"lock:{key}", entry =>
        {
            entry.SetSlidingExpiration(TimeSpan.FromMinutes(10));
            return new SemaphoreSlim(1, 1);
        });
    }

}
