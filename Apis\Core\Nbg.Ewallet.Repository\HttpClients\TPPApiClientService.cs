﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using ibank.ThirdParty.Types;
using ibank.ThirdParty.Types.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.CoreApis;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Repository.HttpClients;

public class TPPApiClientService : AuditableClientService, ITPPApiClientService
{
    private readonly TPPApiClientSettings _configuration;
    private readonly HttpClient _httpClient;
    private readonly ILogger<TPPApiClientService> _logger;

    public TPPApiClientService(IHttpClientFactory httpClientFactory,
        IOptions<TPPApiClientSettings> configuration,
        IServiceAuditRepositoryService serviceAuditRepositoryService,
        ILogger<TPPApiClientService> logger)
        : base(serviceAuditRepositoryService, logger)
    {
        _configuration = configuration.Value;
        _httpClient = httpClientFactory.CreateClient(_configuration.Client);
        _logger = logger;
    }

    public override HttpClient WithHttpClient()
    {
        return _httpClient;
    }

    public async Task<Response<PaymentResponse>> PayAsync(PaymentCoreRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);
        var result = new Response<PaymentResponse>();
        try
        {
            request.Origin = GetSpotMachineOrigin(request.UserID);
            var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.Pay);
            var response = await SendAsync<PaymentResponse>(httpRequestMessage);
            result.Payload = response;
        }
        catch (CoreGenericException e)
        {
            _logger.LogError(e, "Error occurred while processing payment request.");
            result.Exception = new ResponseMessage { Code = e.Code, Description = e.Message, };
        }

        return result;
    }

    public async Task<CommissionResponse> GetCommissionAsync(PaymentCoreRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);
        request.Origin = GetSpotMachineOrigin(request.UserID);
        var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.Commission);
        var response = await SendAsync<CommissionResponse>(httpRequestMessage);
        return response;
    }

    private SpotMachineOrigin GetSpotMachineOrigin(string userId)
    {
        return new SpotMachineOrigin
        {
            NetworkId = _configuration.Origin.NetworkId,
            SpotMachineId = _configuration.Origin.TransactionTerminalId,
            SpotId = _configuration.Origin.TransactionTerminalSpotId,
            MachineId = _configuration.Origin.TransactionTerminalMachineId,
            NetworkMode = NetworkMode.TPP4USERS.ToString(),
            MachineType = SpotMachineType.TransactionTerminal.ToString(),
            UserType = UserIdTypes.IBUSER.ToString(),
            UserId = userId
        };
    }
}
