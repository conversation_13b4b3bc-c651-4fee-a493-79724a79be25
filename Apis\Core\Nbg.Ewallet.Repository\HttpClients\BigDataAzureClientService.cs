﻿using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.CoreApis;
using Nbg.Ewallet.Repository.HttpClients;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Repository;

public class BigDataAzureClientService : AuditableClientService, IBigDataAzureClientService
{
    private readonly HttpClient _client;
    private readonly BigDataAzureClientSettings _configuration;
    private readonly ILogger<BigDataAzureClientService> _logger;
    private readonly JsonSerializerOptions _options;

    public BigDataAzureClientService(
        IHttpClientFactory httpClientFactory,
        IOptions<BigDataAzureClientSettings> configuration,
        ILogger<BigDataAzureClientService> logger,
        IServiceAuditRepositoryService serviceAuditRepositoryService)
        : base(serviceAuditRepositoryService, logger)
    {
        _configuration = configuration.Value;
        _client = httpClientFactory.CreateClient(_configuration.Client);
        _logger = logger;

        _options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
        _options.Converters.Add(new JsonStringEnumConverter());
    }

    public override HttpClient WithHttpClient()
    {
        return _client;
    }

    public async Task<AzureStatementsResponse> GetStatementsAsync(AzureStatementsRequest request)
    {
        try
        {
            var req = WrapRequest(request);
            var json = JsonSerializer.Serialize(req, _options);
            using var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, _configuration.GetStatements)
            {
                Content = new StringContent(json, Encoding.UTF8, "application/json")
            };
            var result = await SendAsync<AzureStatementsResponse>(httpRequestMessage);
            return result;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error in GetStatementsAsync: {Message}", e.Message);
            throw new CoreGenericException($"{e.Message}");
        }
    }

    #region [ Helpers ]

    private Request<AzureStatementsRequest> WrapRequest(AzureStatementsRequest request)
    {
        return new Request<AzureStatementsRequest>
        {
            Header = new RequestHeader { ID = Guid.NewGuid().ToString() },
            Payload = request
        };
    }

    #endregion
}
