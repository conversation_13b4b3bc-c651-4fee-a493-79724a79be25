﻿namespace nbg.ewallet.repository.dbQueries;

internal static class WalletQueries
{
    internal const string GetWalletByOwnerUserId =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE OwnerUserId = @OwnerUserId";

    internal const string GetWalletById =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE WalletId = @Id";

    internal const string InsertWallet =
        "INSERT INTO [EWallet].[dbo].[Wallets] " +
        "(WalletId, WalletName, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, OwnerUserId, " +
        "OwnerCustomerCode, IsCorporateUser, TenantId) " +
        "VALUES " +
        "(@WalletId, @WalletName, @RegistrationDate, @VatNumber, @OrganizationName, @walletAccount, @WalletAccountCreatedAt, @OwnerUserId, " +
        "@OwnerCustomerCode, @IsCorporateUser, @TenantId)";

    internal const string UpdateWalletAccount =
        "UPDATE [EWallet].[dbo].[Wallets] " +
        "SET WalletAccount = @walletaccount, WalletAccountCreatedAt = @walletCreationTime " +
        "where WalletId = @walletid";

    internal const string UpdateWalletSubscription =
        "UPDATE [EWallet].[dbo].[Wallets] " +
        "SET ActiveSubscriptionId = @subscriptionId" +
        "where WalletId = @walletid";

    internal const string SearchsWallet =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE 1=1";

    internal const string WithWalletAccount = " AND [WalletAccount] LIKE @Account";
    internal const string WithWalletName = " AND [WalletName] LIKE @WalletName";
    internal const string WithVatNumber = " AND [VatNumber] LIKE @VatNumber";
    internal const string WithOrganizationName = " AND [OrganizationName] LIKE @OrganizationName";

    internal const string GetWalletByAccount =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE WalletAccount = @WalletAccount";

    internal const string EditWalletName =
        "UPDATE [EWallet].[dbo].[Wallets] " +
        "SET WalletName = @walletName " +
        "where WalletId = @walletId";
}
