﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types;

[DataContract]
public class CompanyUserProfiles
{
    /// <summary>
    /// List of company users
    /// </summary>
    [DataMember(Name = "userProfiles")]
    public List<CompanyUserProfile> UserProfiles { get; set; }
}

[DataContract]
public class CompanyUserProfile
{
    /// <summary>User id </summary>
    [DataMember(Name = "userId")]
    public string UserId { get; set; }

    /// <summary>Friendly name </summary>
    [DataMember(Name = "alias")]
    public string Alias { get; set; }

    /// <summary>User's authorization level eg A </summary>
    [DataMember(Name = "authorizationLevel")]
    public string AuthorizationLevel { get; set; }

    /// <summary>User's number of Approvals</summary>
    [DataMember(Name = "numberOfApprovals")]
    public string NumberOfApprovals { get; set; }

    /// <summary>Mobile number for sms OTP service </summary>
    [DataMember(Name = "smsOtpMobile")]
    public string SmsOtpMobile { get; set; }

    [DataMember(Name = "isScaUnlocked")]
    public bool? IsScaUnlocked { get; set; }

    [DataMember(Name = "isUserLocked")]
    public bool? IsUserLocked { get; set; }

    [DataMember(Name = "transactionLimit")]
    public decimal? TransactionLimit { get; set; }

    [DataMember(Name = "approvalLimit")]
    public decimal? ApprovalLimit { get; set; }
}
