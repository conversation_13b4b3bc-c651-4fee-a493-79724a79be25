using FluentAssertions;
using SandboxTest.Infrastructure;
using SandboxTest.Models;
using System.Net;
using Xunit;
using Xunit.Abstractions;

namespace SandboxTest.Tests
{
    /// <summary>
    /// Comprehensive tests for Payment functionality in the Sandbox API
    ///
    /// Test Logic Overview:
    /// This test class covers all payment-related operations including:
    /// 1. Payment Creation - Creating new payments to various payees
    /// 2. Payment Execution - Processing and completing payments
    /// 3. Payment Status Tracking - Monitoring payment progress
    /// 4. Payment Statements - Retrieving payment history and details
    /// 5. Payment Cancellation - Canceling pending payments
    ///
    /// Each test validates both the payment operation and the resulting
    /// state changes to ensure payments are properly processed and recorded.
    /// </summary>
    public class PaymentTests : SandboxTestBase
    {
        public PaymentTests(ITestOutputHelper output) : base(output)
        {
        }
        [Fact(DisplayName = "Create Payment - Should create payment successfully")]
        public async Task CreatePayment_ShouldCreatePayment_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load funds
             * 2. Create a payment to a specific payee
             * 3. Verify the payment creation succeeds
             * 4. Verify payment details are correct
             * 5. Verify payment can be retrieved
             * 
             * Expected Result:
             * - Payment is created with valid ID
             * - Payment amount and details match request
             * - Payment status is set appropriately
             * - Payment can be retrieved using payment ID
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Create payment request
            var paymentAmount = TestDataHelper.GenerateAmount(100, 300);
            var payeeCode = "PAYEE001";
            var paymentRequest = TestDataHelper.CreatePaymentRequest(paymentAmount, payeeCode);

            // Act
            var paymentResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", paymentRequest);

            // Assert
            var payment = await AssertSuccessAndDeserialize<PaymentResponse>(paymentResponse);
            
            payment.PaymentId.Should().NotBeNullOrEmpty("payment should have a valid ID");
            payment.Amount.Should().Be(paymentAmount, "payment amount should match request");
            payment.Currency.Should().Be("EUR", "payment currency should be EUR");
            payment.Status.Should().BeOneOf("Created", "Pending", "Processing", "payment status should be valid");
            payment.PaymentDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5), "payment date should be recent");

            // Verify payment can be retrieved
            var getPaymentResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/payments/{payment.PaymentId}");
            var retrievedPayment = await AssertSuccessAndDeserialize<PaymentResponse>(getPaymentResponse);
            
            retrievedPayment.PaymentId.Should().Be(payment.PaymentId, "retrieved payment should match created payment");
            retrievedPayment.Amount.Should().Be(paymentAmount, "retrieved payment amount should match");
        }

        [Fact(DisplayName = "Execute Payment - Should process payment and update wallet balance")]
        public async Task ExecutePayment_ShouldProcessPayment_AndUpdateWalletBalance()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load funds
             * 2. Create a payment
             * 3. Execute the payment
             * 4. Verify payment execution succeeds
             * 5. Verify wallet balance is updated correctly
             * 
             * Expected Result:
             * - Payment execution returns success
             * - Payment status is updated to completed/processed
             * - Wallet balance decreases by payment amount
             * - Payment is recorded in transaction history
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Get initial balance
            var initialBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var initialBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(initialBalanceResponse);

            // Create payment
            var paymentAmount = TestDataHelper.GenerateAmount(100, 300);
            var payeeCode = "PAYEE002";
            var paymentRequest = TestDataHelper.CreatePaymentRequest(paymentAmount, payeeCode);
            
            var paymentResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", paymentRequest);
            var payment = await AssertSuccessAndDeserialize<PaymentResponse>(paymentResponse);

            // Act
            var executeResponse = await Client.PostAsync($"wallet/{createdWallet.WalletId}/payments/{payment.PaymentId}/execute", null);

            // Assert
            executeResponse.StatusCode.Should().Be(HttpStatusCode.OK, "payment execution should succeed");

            // Verify payment status is updated
            var updatedPaymentResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/payments/{payment.PaymentId}");
            var updatedPayment = await AssertSuccessAndDeserialize<PaymentResponse>(updatedPaymentResponse);
            
            updatedPayment.Status.Should().BeOneOf("Completed", "Processed", "Executed", "payment status should indicate completion");

            // Verify balance is updated
            var updatedBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var updatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(updatedBalanceResponse);
            
            updatedBalance.Balance.Should().Be(initialBalance.Balance - paymentAmount, 
                "wallet balance should decrease by payment amount");
        }

        [Fact(DisplayName = "Get Payment Status - Should return current payment status")]
        public async Task GetPaymentStatus_ShouldReturnCurrentPaymentStatus()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and payment
             * 2. Retrieve the payment status
             * 3. Verify status information is complete
             * 4. Verify status reflects current payment state
             * 
             * Expected Result:
             * - Payment status is returned
             * - Status includes all relevant details
             * - Status reflects the current state of the payment
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Create payment
            var paymentAmount = TestDataHelper.GenerateAmount(100, 300);
            var payeeCode = "PAYEE003";
            var paymentRequest = TestDataHelper.CreatePaymentRequest(paymentAmount, payeeCode);
            
            var paymentResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", paymentRequest);
            var payment = await AssertSuccessAndDeserialize<PaymentResponse>(paymentResponse);

            // Act
            var statusResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/payments/{payment.PaymentId}");

            // Assert
            var paymentStatus = await AssertSuccessAndDeserialize<PaymentResponse>(statusResponse);
            
            paymentStatus.PaymentId.Should().Be(payment.PaymentId, "payment ID should match");
            paymentStatus.Amount.Should().Be(paymentAmount, "amount should match");
            paymentStatus.Status.Should().NotBeNullOrEmpty("status should be provided");
            paymentStatus.PaymentDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5), "payment date should be recent");
        }

        [Fact(DisplayName = "Get Payment History - Should return all payments for wallet")]
        public async Task GetPaymentHistory_ShouldReturnAllPayments_ForWallet()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load funds
             * 2. Create multiple payments
             * 3. Retrieve payment history
             * 4. Verify all payments are included in history
             * 5. Verify payment details are complete
             * 
             * Expected Result:
             * - All payments are returned in history
             * - Payment details include all required information
             * - Payments are properly ordered (typically by date)
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(1000, 2000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Create multiple payments
            var payment1Amount = TestDataHelper.GenerateAmount(100, 200);
            var payment2Amount = TestDataHelper.GenerateAmount(150, 250);
            
            var payment1Request = TestDataHelper.CreatePaymentRequest(payment1Amount, "PAYEE004");
            var payment2Request = TestDataHelper.CreatePaymentRequest(payment2Amount, "PAYEE005");
            
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", payment1Request);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", payment2Request);

            // Act
            var historyResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/payments");

            // Assert
            var paymentHistory = await AssertSuccessAndDeserialize<List<PaymentResponse>>(historyResponse);
            
            paymentHistory.Should().NotBeEmpty("payment history should contain payments");
            paymentHistory.Should().HaveCountGreaterOrEqualTo(2, "history should include both test payments");
            
            foreach (var payment in paymentHistory)
            {
                payment.PaymentId.Should().NotBeNullOrEmpty("payment should have payment ID");
                payment.Amount.Should().BeGreaterThan(0, "payment amount should be positive");
                payment.Currency.Should().Be("EUR", "currency should be EUR");
                payment.Status.Should().NotBeNullOrEmpty("status should be provided");
                payment.PaymentDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromHours(1), "payment date should be recent");
            }
        }

        [Fact(DisplayName = "Cancel Payment - Should cancel pending payment successfully")]
        public async Task CancelPayment_ShouldCancelPendingPayment_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and payment
             * 2. Cancel the payment before execution
             * 3. Verify cancellation succeeds
             * 4. Verify payment status is updated to cancelled
             * 5. Verify wallet balance is not affected
             * 
             * Expected Result:
             * - Cancellation operation succeeds
             * - Payment status is updated to cancelled
             * - Wallet balance remains unchanged
             * - Cancelled payment appears in history with cancelled status
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Get initial balance
            var initialBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var initialBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(initialBalanceResponse);

            // Create payment
            var paymentAmount = TestDataHelper.GenerateAmount(100, 300);
            var payeeCode = "PAYEE006";
            var paymentRequest = TestDataHelper.CreatePaymentRequest(paymentAmount, payeeCode);
            
            var paymentResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", paymentRequest);
            var payment = await AssertSuccessAndDeserialize<PaymentResponse>(paymentResponse);

            // Act
            var cancelResponse = await Client.PostAsync($"wallet/{createdWallet.WalletId}/payments/{payment.PaymentId}/cancel", null);

            // Assert
            cancelResponse.StatusCode.Should().Be(HttpStatusCode.OK, "payment cancellation should succeed");

            // Verify payment status is updated
            var updatedPaymentResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/payments/{payment.PaymentId}");
            var updatedPayment = await AssertSuccessAndDeserialize<PaymentResponse>(updatedPaymentResponse);
            
            updatedPayment.Status.Should().BeOneOf("Cancelled", "Canceled", "payment status should indicate cancellation");

            // Verify balance remains unchanged
            var updatedBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var updatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(updatedBalanceResponse);
            
            updatedBalance.Balance.Should().Be(initialBalance.Balance, 
                "wallet balance should remain unchanged after cancellation");
        }

        [Fact(DisplayName = "Get Payment Statements - Should return detailed payment statements")]
        public async Task GetPaymentStatements_ShouldReturnDetailedPaymentStatements()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and execute payments
             * 2. Retrieve payment statements
             * 3. Verify statements include all payment transactions
             * 4. Verify statement details are complete and accurate
             * 
             * Expected Result:
             * - Payment statements are returned
             * - Statements include all payment transactions
             * - Statement details include amounts, dates, and descriptions
             * - Statements are properly formatted and ordered
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load funds into wallet
            var loadAmount = TestDataHelper.GenerateAmount(1000, 2000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Create and execute a payment
            var paymentAmount = TestDataHelper.GenerateAmount(100, 300);
            var payeeCode = "PAYEE007";
            var paymentRequest = TestDataHelper.CreatePaymentRequest(paymentAmount, payeeCode);
            
            var paymentResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", paymentRequest);
            var payment = await AssertSuccessAndDeserialize<PaymentResponse>(paymentResponse);
            
            await Client.PostAsync($"wallet/{createdWallet.WalletId}/payments/{payment.PaymentId}/execute", null);

            // Act
            var statementsResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/statements");

            // Assert
            if (statementsResponse.StatusCode == HttpStatusCode.OK)
            {
                var statements = await DeserializeResponseAsync<object>(statementsResponse);
                statements.Should().NotBeNull("statements should be returned");
                
                // Note: The exact structure would depend on the API implementation
                // This test validates that the endpoint is accessible and returns data
            }
            else
            {
                // Some implementations might not have statements functionality implemented yet
                statementsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotImplemented, HttpStatusCode.NotFound);
            }
        }

        [Fact(DisplayName = "Payment Validation - Should validate payment amount and payee")]
        public async Task PaymentValidation_ShouldValidatePaymentAmount_AndPayee()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet with limited funds
             * 2. Attempt to create payment exceeding available balance
             * 3. Verify validation error is returned
             * 4. Attempt to create payment with invalid payee
             * 5. Verify appropriate validation errors
             * 
             * Expected Result:
             * - Payment exceeding balance is rejected
             * - Invalid payee is rejected
             * - Appropriate error messages are returned
             * - Wallet balance remains unchanged
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load limited funds into wallet
            var loadAmount = 100m;
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Act - Try to create payment exceeding balance
            var excessiveAmount = loadAmount + 50m;
            var excessivePaymentRequest = TestDataHelper.CreatePaymentRequest(excessiveAmount, "PAYEE008");
            var excessivePaymentResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", excessivePaymentRequest);

            // Assert
            await AssertErrorResponse(excessivePaymentResponse, HttpStatusCode.BadRequest);

            // Act - Try to create payment with invalid payee (empty)
            var invalidPayeeRequest = TestDataHelper.CreatePaymentRequest(50m, "");
            var invalidPayeeResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/payments", invalidPayeeRequest);

            // Assert
            await AssertErrorResponse(invalidPayeeResponse, HttpStatusCode.BadRequest);
        }
    }
}
