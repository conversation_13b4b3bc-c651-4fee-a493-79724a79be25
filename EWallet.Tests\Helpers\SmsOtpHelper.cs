﻿using System.Text.Json;

namespace EWallet.Tests.Helpers;

public class SmsOtp
{
    public Guid Id { get; set; }
    public Guid ServiceAudit { get; set; }
    public DateTime OtpTimestamp { get; set; }
    public DateTime OtpExpiration { get; set; }
    public string Otp { get; set; }
    public string OtpTanCheck { get; set; }
    public bool IsUsed { get; set; }
    public string PhoneNumber { get; set; }
    public string UserId { get; set; }
    public string ServiceName { get; set; }
    public string RequestTextData { get; set; }
}

public static class SmsOtpHelper
{
    private const string Env_V21 = "DEV";
    private const string Env_Qa = "QA";
    private const string Env_Qa_Ips = "QA-ips";
    private static string url = "https://coreapplayerqa.nbg.gr/sms.otp.api/api/smsotp/user/";
    public static string GetApiUrl(string selectedEnv)
    {
        if (selectedEnv.Equals(Env_V21))
        {
            return "http://v000080121/sms.otp.api";
        }
        else if (selectedEnv.Equals(Env_Qa_Ips))
        {
            return "https://coreapplayerqa.nbg.gr/sms.otp.api.ips";
        }
        else
        {
            return "https://coreapplayerqa.nbg.gr/sms.otp.api";
        }
    }

    public static async Task<SmsOtp> GetSmsOtp(string userId)
    {
        var client = new HttpClient();

        var response = await client.GetAsync(new Uri(url + userId));
        var responseContent = await response.Content.ReadAsStringAsync();
        var smss = JsonSerializer.Deserialize<List<SmsOtp>>(responseContent);
        client.Dispose();
        return smss.OrderByDescending(x => x.OtpExpiration).FirstOrDefault();
    }


    //public static async Task<List<SmsOtp>> FetchOtps(string apiUrl, string userId)
    //{
    //    string url = $"{apiUrl}/api/smsotp";
    //    if (!string.IsNullOrEmpty(userId)) url += $"/user/{userId}";

    //    try
    //    {
    //        var requestMessage = new HttpRequestMessage(HttpMethod.Get, url);
    //        requestMessage.Headers.TryAddWithoutValidation("Client-Id", "31c0f2fc-6053-49af-b7de-ef5ede9697a7");

    //        var response = await GetHttpClient().SendAsync(requestMessage);
    //        if (!response.IsSuccessStatusCode)
    //        {
    //            //MessageBox.Show("Request failed with Error Message" + response.ReasonPhrase);
    //        }
    //        string responseContent = await response.Content.ReadAsStringAsync();
    //        return Newtonsoft.Json.JsonConvert.DeserializeObject<List<SmsOtp>>(responseContent);
    //    }
    //    catch (Exception e)
    //    {
    //        //MessageBox.Show("Exception while executing web request:" +
    //        //    $"{Environment.NewLine}{e.Message}" +
    //        //    $"{Environment.NewLine}{(e.InnerException?.Message ?? "")}");
    //        return new List<SmsOtp>();
    //    }
    //}
}
