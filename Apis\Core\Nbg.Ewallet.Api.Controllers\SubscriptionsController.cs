﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Subscriptions;

namespace Nbg.Ewallet.Api.Controllers
{
    /// <summary>
    /// This Controller provides functionality for EWallet Payments
    /// </summary>
    [Produces("application/json")]
    [Consumes("application/json")]
    [ApiExplorerSettings(GroupName = "Subscriptionplans")]
    public class SubscriptionsController : BaseController
    {
        private readonly ISubscriptionPlansService _subscriptionsPlansService;

        public SubscriptionsController(ISubscriptionPlansService subscriptionsPlansService, ILogger<SubscriptionsController> logger) : base(logger)
        {
            _subscriptionsPlansService = subscriptionsPlansService;
        }

        /// <summary>
        /// get subscription plans
        /// </summary>
        [HttpGet]
        [Route("subscription-plans", Name = "subscription-plans")]
        public async Task<ActionResult<List<SubscriptionPlan>>> GetSubscriptionPlans()
        {
            return await _subscriptionsPlansService.GetAvailableSubscriptionPlans();
        }
    }
}
