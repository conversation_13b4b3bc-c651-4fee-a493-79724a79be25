﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxWalletRepositoryService : IWalletRepositoryService
{
    private ISandBoxRepositoryService _sandboxRepositoryService;
    private IMapper _mapper;
    //private IHttpContextAccessor _httpContextAccessor;

    public SandboxWalletRepositoryService(
        ISandBoxRepositoryService sandboxRepositoryService,
        IMapper mapper)//,
                       // IHttpContextAccessor httpContextAccessor)
    {
        _mapper = mapper;
        _sandboxRepositoryService = sandboxRepositoryService;
        //_httpContextAccessor = httpContextAccessor;
    }

    public async Task<RepoWallet> FindOneByIdAsync(string walletId)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();
        var wallet = model.Wallets.FirstOrDefault(x => x.WalletId.ToString() == walletId)
                     ?? throw new WalletNotFoundException();
        return _mapper.Map<RepoWallet>(wallet);
    }

    public async Task<RepoWallet> UpdateWalletNameByWalletIdAsync(Guid walletId, string walletName)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();

        var wallet = model.Wallets.FirstOrDefault(x => x.WalletId == walletId)
                     ?? throw new WalletNotFoundException();
        wallet.WalletName = walletName;
        await _sandboxRepositoryService.UpdateSandboxData(model);

        return _mapper.Map<RepoWallet>(wallet);
    }

    public async Task<RepoWallet> FindOneByWalletAccountAsync(string account)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();
        var wallet = model.Wallets.FirstOrDefault(x => x.WalletAccount?.ToLower() == account.Trim().ToLower()); //do not throw here
        if (wallet != null)
        {
            return _mapper.Map<RepoWallet>(wallet);
        }

        return null;
    }

    public async Task<RepoWallet> FindOneByOwnerUserIdAsync(string userId)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();

        var ret = model.Wallets.FirstOrDefault(x => x.OwnerUserId == userId) ?? throw new WalletNotFoundException();

        RepoWallet repoWallet = new()
        {
            WalletId = ret.WalletId,
            WalletName = ret.WalletName,
            VatNumber = ret.VatNumber,
            WalletAccount = ret.WalletAccount,
            WalletAccountCreatedAt = ret.WalletAccountCreatedAt,
            RegistrationDate = ret.RegistrationDate,
            OrganizationName = ret.OrganizationName,
            OwnerUserId = ret.OwnerUserId,
            IsCorporateUser = ret.IsCorporateUser,
            ActiveSubscriptionId = ret.ActiveSubscriptionId,
            OwnerCustomerCode = ret.OwnerCustomerCode,
        };
        return repoWallet;
    }

    public async Task SaveAsync(RepoWallet wallet)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();

        SandboxWallet mWallet = _mapper.Map<SandboxWallet>(wallet);

        var existingwallet = model.Wallets.FirstOrDefault(x => x.WalletId == wallet.WalletId);
        if (existingwallet != null)
        {
            throw new WalletAlreadyExistsException();
        }

        model.Wallets.Add(mWallet);
        await _sandboxRepositoryService.UpdateSandboxData(model);
    }

    public async Task<List<RepoWallet>> FindAllByParamsAsync(string vatNumber, string walletAccount, string walletName, string organizationName)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();
        var query = model.Wallets.AsQueryable();

        if (!string.IsNullOrEmpty(walletName))
        {
            query = query.Where(w => w.WalletName != null && w.WalletName.Contains(walletName, StringComparison.CurrentCultureIgnoreCase));
        }

        if (!string.IsNullOrEmpty(vatNumber))
        {
            query = query.Where(w => w.VatNumber != null && w.VatNumber.Contains(vatNumber, StringComparison.CurrentCultureIgnoreCase));
        }

        if (!string.IsNullOrEmpty(walletAccount))
        {
            query = query.Where(w => w.WalletAccount != null && w.WalletAccount.Contains(walletAccount, StringComparison.CurrentCultureIgnoreCase));
        }

        if (!string.IsNullOrEmpty(organizationName))
        {
            query = query.Where(w => w.OrganizationName != null && w.OrganizationName.Contains(organizationName, StringComparison.CurrentCultureIgnoreCase));
        }

        var result = query.Select(w => new WalletLite
        {
            OrganizationName = w.OrganizationName,
            WalletName = w.WalletName,
            WalletId = w.WalletId,
            VatNumber = w.VatNumber,
            WalletAccount = w.WalletAccount
        }).ToList();

        return _mapper.Map<List<RepoWallet>>(result);
    }

    public async Task UpdateWalletAccountByWalletIdAsync(string walletid, string WalletAccount)
    {
        var sandbox = await _sandboxRepositoryService.GetSandBoxModel();
        var wallet = sandbox.Wallets.FirstOrDefault(x => x.WalletId.ToString() == walletid)
                     ?? throw new WalletNotFoundException();
        wallet.WalletAccount = WalletAccount;
        wallet.WalletAccountCreatedAt = DateTime.Now;

        var availableAccount = sandbox.AvailableAccounts.FirstOrDefault(x => x.IBAN == wallet.WalletAccount);
        if (availableAccount is not null)
        {
            return;
        }

        availableAccount = new SandboxAvailableAccount { AvailableBalance = 10_000M, LedgerBalance = 10_000m, Currency = "070", IBAN = wallet.WalletAccount };
        sandbox.AvailableAccounts.Add(availableAccount);
        await _sandboxRepositoryService.UpdateSandboxData(sandbox);
        return;
    }

    public async Task<bool> ExistsByOwnerUserIdAsync(string userId)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();
        var wallet = model.Wallets.FirstOrDefault(x => x.OwnerUserId == userId);
        return wallet != null;
    }

    public async Task UpdateWalletSubscriptionByWalletIdAsync(Guid walletid, Guid subscriptionId)
    {
        var sandbox = await _sandboxRepositoryService.GetSandBoxModel();
        var wallet = sandbox.Wallets.FirstOrDefault(x => x.WalletId == walletid)
                     ?? throw new WalletNotFoundException();
        wallet.ActiveSubscriptionId = subscriptionId;
        await _sandboxRepositoryService.UpdateSandboxData(sandbox);
        return;
    }
}
