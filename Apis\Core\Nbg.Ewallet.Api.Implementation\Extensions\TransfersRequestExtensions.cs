﻿using System;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Extensions;
using Nbg.Ewallet.Repository.Types.Records;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Implementation.Extensions;

public static class TransfersRequestExtensions
{
    public static RepoTransaction ToPendingRepoTransaction(this TransfersRequest @this, RepoWallet wallet, string userId, TransactionSubType transactionType)
    {
        var transaction = new RepoTransaction
        {
            TransactionId = @this.TransactionId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Amount = @this.Amount,
            Status = TransactionStatus.PENDING_APPROVAL,
            TransactionSubType = transactionType,
            TransactionType = TransactionType.Transfer,
            WalletId = wallet.WalletId,
            CreditorIban = @this.ReceiverAcc ?? string.Empty,
            CreditorName = @this.ReceiverGR ?? string.Empty,
            Currency = @this.Currency ?? "EUR",
            DebtorIban = @this.DebitAccount,
            DebtorName = @this.DebtorName ?? wallet.OrganizationName,
            Reason = @this.Reason ?? string.Empty,
            SubmittedBy = userId,
            IsInstant = @this.InstantSend,
            TransfersRequest = @this
        };
        return transaction;
    }

    public static TransfersResponse EnrichResponse(this TransfersRequest @this, TransfersResponse transfersResponse)
    {
        transfersResponse.RemType = @this.ReceiverAcc.GetRemType();
        transfersResponse.Currency = "EUR";
        transfersResponse.TransferAmount = @this.Amount;
        transfersResponse.DebitAccount = @this.DebitAccount;
        //if (!string.IsNullOrWhiteSpace(@this.ReceiverGR)) transfersResponse.Beneficiaries = new string[] { @this.ReceiverGR };
        return transfersResponse;
    }

    [Obsolete("This method is not used now", true)]
    public static void ValidateAccountRegisteredOnWallet(this TransfersRequest @this, RepoWallet wallet)
    {
        var isNotSameAccount = wallet.WalletAccount.TrimEqual(@this.DebitAccount);
        if (isNotSameAccount) throw new AccountNotRegisteredOnWalletException();
    }

    /// <summary>
    /// Validates if the debit account specified in the transfer request is available to the user.
    /// If the account is the same as the wallet account, returns a result indicating so.
    /// Otherwise, checks account availability via the Accounts API and returns a result indicating the account is valid but not the wallet account.
    /// Throws <see cref="AccountIsNotAvailableToUser"/> if the account is not available to the user.
    /// </summary>
    /// <param name="this">The transfer request containing the debit account information.</param>
    /// <param name="wallet">The wallet associated with the user.</param>
    /// <param name="userId">The user ID to validate account ownership.</param>
    /// <param name="accountsApiClientService">The accounts API client service used for validation.</param>
    /// <returns>A <see cref="ValidateRequestResult"/> indicating if the account is the wallet account or a valid user account.</returns>
    /// <exception cref="AccountIsNotAvailableToUser">Thrown if the account is not available to the user.</exception>
    public static async Task<ValidateRequestResult> ValidateAccountAvailableToUser(this TransfersRequest @this, RepoWallet wallet, string userId, IAccountsApiClientService accountsApiClientService)
    {
        if (@this.IsAccountLinkedToWallet(wallet))
        {
            // The account is the same as the wallet account, no need to validate..
            return new ValidateRequestResult(true, @this.DebitAccount);
        }

        //the user may have only subbmit rights so he may not own the wallet.
        if (wallet.OwnerUserId == userId)
        {
            var availableToUser = await accountsApiClientService.ValidateUserAccount(userId, @this.DebitAccount);
            if (!availableToUser)
            {
                throw new AccountIsNotAvailableToUser();
            }
        }

        //Valid, but the account is not the same as the wallet account.
        return new ValidateRequestResult(false, @this.DebitAccount);
    }

    /// <summary>
    /// Determines whether the IBAN of the debit account in the transfer request matches the wallet's account.
    /// Throws <see cref="ArgumentNullException"/> if the debtor IBAN is null or empty.
    /// Throws <see cref="ArgumentException"/> if the wallet account is null or whitespace.
    /// </summary>
    public static bool IsAccountLinkedToWallet(this TransfersRequest @this, RepoWallet wallet)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(@this.DebitAccount);
        ArgumentException.ThrowIfNullOrWhiteSpace(wallet.WalletAccount);
        return wallet.WalletAccount.TrimEqual(@this.DebitAccount);
    }

    // Generic Transfer also validates these.
    public static void ValidateAccountBICTransferType(this TransfersRequest @this)
    {
        if (string.IsNullOrWhiteSpace(@this.ReceiverAcc)) throw new IbanNotFoundException();
        if (AccountHelpers.IbanIsNbg(@this.ReceiverAcc) && @this.BeneficiaryBic != AccountHelpers.NbgBic) throw new BicAccountMismatchException();
        if (!@this.TransferType.HasValue) return;
        switch (@this.TransferType.Value)
        {
            case TransferType.NBG:
            case TransferType.OWN:
                if (!AccountHelpers.IbanIsNbg(@this.ReceiverAcc)) throw new TransferTypeAccountMismatchException();
                if (@this.BeneficiaryBic != AccountHelpers.NbgBic) throw new TransferTypeBicMismatchException();
                return;
            case TransferType.OTHER:
                if (AccountHelpers.IbanIsNbg(@this.ReceiverAcc)) throw new TransferTypeAccountMismatchException();
                if (@this.BeneficiaryBic == AccountHelpers.NbgBic) throw new TransferTypeBicMismatchException();
                return;
            case TransferType.UNKNOWN:
            default:
                return;
        }
    }

    public static TransferExpensesCommissionsRequest ToTransferExpensesCommissionsRequest(this TransfersRequestBase @this, string userId)
    {
        if (@this == null) return null;
        return new TransferExpensesCommissionsRequest
        {
            Amount = @this.Amount,
            BeneficiaryBic = @this.BeneficiaryBic,
            DebitAccount = @this.DebitAccount,
            DebitAccountCurrency = "EUR",
            Emergency = @this.Emergency,
            Expenses = @this.Expenses, //this should contain a value
            InstantSend = @this.InstantSend,
            MassFlag = @this.MassFlag,
            OnlineSend = false, //this must be always false
            ReceiverAcc = @this.ReceiverAcc,
            ReceiverAccType = ReceiverAccType.IBAN,
            RemCategory = @this.RemCategory,
            RemCurrency = "EUR",
            RemType = @this.ReceiverAcc.GetRemType(),
            SystBic = @this.SystBic,
            SystCode = @this.SystCode,
            SystCountry = @this.SystCountry,
            SystType = @this.SystType,
            UserID = userId
        };
    }

    public static async Task<TransactionSubType> GetTransactionSubType(this TransfersRequest request, IWalletRepositoryService walletRepository)
    {
        var receiverWallet = await walletRepository.FindOneByWalletAccountAsync(request.ReceiverAcc);
        if (receiverWallet != null) return TransactionSubType.WalletToWallet;
        if (AccountHelpers.IbanIsNbg(request.ReceiverAcc)) return TransactionSubType.WalletToNBG;
        else return TransactionSubType.WalletToIBAN;
    }
}

public static class TransfersResponseExtensions
{
    public static TransfersResponse ToTransfersResponse(this TransfersResponseBase @this)
    {
        var response = new TransfersResponse
        {
            AvailableBalance = @this.AvailableBalance,
            BankTitle = @this.BankTitle,
            Beneficiaries = @this.Beneficiaries,
            Currency = @this.Currency,
            DebitAccount = @this.DebitAccount,
            DebitAmountOut = @this.DebitAmountOut,
            DebtorIBAN = @this.DebtorIBAN,
            DebtorName = @this.DebtorName,
            DeptExpensesOut = @this.DeptExpencesOut,
            EteCommissionOut = @this.EteComissionOut,
            ExchangeProfitOut = @this.ExchangeProfitOut,
            ExchangeRateOut = @this.ExchangeRateOut,
            IsDuplicate = @this.IsDuplicate,
            LedgerBalance = @this.LedgerBalance,
            NetAmountOut = @this.NetAmountOut,
            NonStpExpensesOut = @this.NonStpExpencesOut,
            OnlineExpensesOut = @this.OnlineExpensesOut,
            ReferenceNumber = @this.ReferenceNumber,
            RefNo = @this.RefNo,
            RemType = @this.RemType,
            RequiresApproval = @this.RequiresApproval,
            SumCommissionOut = @this.SumComissionOut,
            TanCheck = @this.TanCheck,
            TransactionDate = @this.TransactionDate,
            TransferAmount = @this.TransferAmount,
            UrgentExpensesOut = @this.UrgentExpencesOut,
            Valeur = @this.Valeur
        };
        return response;
    }
}
