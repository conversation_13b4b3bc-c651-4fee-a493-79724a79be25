﻿using System;
using Microsoft.AspNetCore.Http;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandBoxHttpContextRepositoryService : IHttpContextRepositoryService
{
    public SandBoxHttpContextRepositoryService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    private readonly IHttpContextAccessor _httpContextAccessor;

    public string GetCustomerCode()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("Customer-Code").Clear() ??
               _httpContextAccessor.HttpContext.GetClaimsValue("customer_code").Clear();
    }

    public string GetUserId()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("UserId").Clear() ??
               _httpContextAccessor.HttpContext.GetClaimsValue("preferred_username").Clear();
    }

    public int GetNumberOfApprovals()
    {
        var stringValue = _httpContextAccessor.HttpContext.GetClaimsValue("number_of_approvals");
        if (stringValue is null)
        {
            return 0;
        }

        return !int.TryParse(stringValue, out var numberOfApprovals) ? 0 : numberOfApprovals;
    }

    public string GetAuthorizationLevel()
    {
        return _httpContextAccessor.HttpContext.GetClaimsValue("authorization_level");
    }

    public string GetCompanyName()
    {
        return _httpContextAccessor.HttpContext.GetClaimsValue("company_name");
    }

    public Guid GetClientId()
    {
        return Guid.Parse(_httpContextAccessor.HttpContext.GetHeaderValue("Client-Id").Clear()
                          ?? _httpContextAccessor.HttpContext.GetClaimsValue("client_id").Clear()
                          ?? Guid.NewGuid().ToString());
    }

    public string GetConsentId()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("Consent-Id").Clear()
               ?? _httpContextAccessor.HttpContext.GetClaimsValue("consent_id");
    }

    public string GetSub()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("Sub").Clear()
               ?? _httpContextAccessor.HttpContext.GetClaimsValue("sub").Clear();
    }

    public string? GetCallbackUrl()
    {
        var callbackUrl = _httpContextAccessor.HttpContext.GetHeaderValue("Callback-Url");
        return callbackUrl.Clear();
    }

    public string? GetSandboxId()
    {
        return _httpContextAccessor.HttpContext.GetHeaderValue("sandboxid")?.Clear();
    }
}
