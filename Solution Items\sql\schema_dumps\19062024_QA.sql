USE [EWallet]
GO
/****** Object:  Table [dbo].[ApproveTypes]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ApproveTypes](
	[id] [uniqueidentifier] NOT NULL,
	[typeid] [smallint] NOT NULL,
	[description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_ApproveTypes] PRIMARY KEY CLUSTERED 
(
	[typeid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[AuthorizationRequests]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AuthorizationRequests](
	[Id] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[TargetWalletId] [uniqueidentifier] NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[ExpiresAt] [datetime2](7) NOT NULL,
	[UpdatedAt] [datetime2](7) NULL,
	[Status] [smallint] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DiscountTransactions]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DiscountTransactions](
	[DiscountTransactionId] [uniqueidentifier] NOT NULL,
	[TransactionType] [smallint] NOT NULL,
	[Value] [decimal](10, 2) NOT NULL,
	[SubscriptionPlanType] [smallint] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Limits]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Limits](
	[Id] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[UserId] [nvarchar](250) NOT NULL,
	[TransactionType] [smallint] NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[TimeFrame] [smallint] NOT NULL,
	[Active] [bit] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Payments]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Payments](
	[PaymentId] [uniqueidentifier] NOT NULL,
	[CreationDate] [datetime2](7) NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[DebtorIban] [nvarchar](25) NOT NULL,
	[DebtorName] [nvarchar](50) NOT NULL,
	[DebtorTelephone] [nvarchar](15) NOT NULL,
	[CreditorIban] [nvarchar](25) NOT NULL,
	[CreditorName] [nvarchar](50) NOT NULL,
	[PaymentCode] [nvarchar](25) NOT NULL,
	[TransactionDate] [datetime2](7) NOT NULL,
	[BookingDate] [datetime2](7) NOT NULL,
	[isSmart] [bit] NOT NULL,
	[Status] [smallint] NOT NULL,
 CONSTRAINT [PK_Payments] PRIMARY KEY CLUSTERED 
(
	[PaymentId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionBundles]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionBundles](
	[Id] [uniqueidentifier] NOT NULL,
	[SubscriptionId] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[TransactionType] [smallint] NOT NULL,
	[Value] [int] NOT NULL,
 CONSTRAINT [PK_SubscriptionBundles] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionPlans]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionPlans](
	[SubscriptionPlanid] [uniqueidentifier] NOT NULL,
	[SubscriptionPlanType] [smallint] NOT NULL,
 CONSTRAINT [PK_SubscriptionPlans] PRIMARY KEY CLUSTERED 
(
	[SubscriptionPlanType] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionPlanTypes]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionPlanTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[SubscriptionPlanTypeId] [smallint] NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_SubscriptionPlanTypes] PRIMARY KEY CLUSTERED 
(
	[SubscriptionPlanTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Subscriptions]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Subscriptions](
	[SubscriptionId] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[PlanId] [smallint] NOT NULL,
	[AllowedUsers] [smallint] NOT NULL,
	[StartDate] [datetime2](7) NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
 CONSTRAINT [PK_Subscriptions] PRIMARY KEY CLUSTERED 
(
	[SubscriptionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriptionsCommission]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriptionsCommission](
	[Id] [uniqueidentifier] NOT NULL,
	[PlanId] [smallint] NOT NULL,
	[CommissionId] [nvarchar](250) NOT NULL,
 CONSTRAINT [PK_SubscriptionsCommission] PRIMARY KEY CLUSTERED 
(
	[PlanId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TimeFrameTypes]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TimeFrameTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[TimeFrameTypeId] [smallint] NOT NULL,
	[Description] [nchar](50) NOT NULL,
 CONSTRAINT [PK_TimeFrameTypes] PRIMARY KEY CLUSTERED 
(
	[TimeFrameTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transactions]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transactions](
	[TransactionId] [uniqueidentifier] NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[UpdatedAt] [datetime2](7) NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[TransferType] [nvarchar](25) NULL,
	[TransactionType] [nvarchar](25) NULL,
	[Currency] [nvarchar](3) NOT NULL,
	[DebtorIban] [nvarchar](50) NOT NULL,
	[DebtorName] [nvarchar](50) NOT NULL,
	[Reference] [nvarchar](50) NULL,
	[CreditorIban] [nvarchar](50) NOT NULL,
	[CreditorName] [nvarchar](50) NOT NULL,
	[TransactionDate] [datetime2](7) NULL,
	[Reason] [nvarchar](25) NOT NULL,
	[Status] [nvarchar](25) NOT NULL,
	[Result] [nvarchar](25) NULL,
	[ResultReason] [nvarchar](100) NULL,
	[BatchId] [uniqueidentifier] NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[SubmittedBy] [nvarchar](25) NOT NULL,
	[ApprovedBy] [nvarchar](25) NULL,
	[ExecutedBy] [nvarchar](25) NULL,
 CONSTRAINT [PK_Transactions] PRIMARY KEY CLUSTERED 
(
	[TransactionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TransactionStatuses]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TransactionStatuses](
	[id] [uniqueidentifier] NOT NULL,
	[TransactionStatusId] [smallint] NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_TransactionStatuses] PRIMARY KEY CLUSTERED 
(
	[TransactionStatusId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TransactionTypes]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TransactionTypes](
	[id] [uniqueidentifier] NOT NULL,
	[TransactionTypeId] [smallint] NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_TransactionTypes] PRIMARY KEY CLUSTERED 
(
	[TransactionTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserPermissions]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserPermissions](
	[Id] [uniqueidentifier] NOT NULL,
	[UserId] [nvarchar](250) NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[Submit] [bit] NOT NULL,
	[Approve] [smallint] NOT NULL,
	[BalanceView] [bit] NOT NULL,
	[TransactionView] [bit] NOT NULL,
	[Admin] [bit] NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[AssignedBy] [nvarchar](250) NOT NULL,
	[RevokedBy] [nvarchar](250) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WalletPermissions]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WalletPermissions](
	[Id] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[ExternalWalletId] [uniqueidentifier] NOT NULL,
	[Submit] [bit] NOT NULL,
	[Approve] [smallint] NOT NULL,
	[BalanceView] [bit] NOT NULL,
	[TransactionView] [bit] NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[CreationDate] [datetime2](7) NOT NULL,
	[AssignedBy] [nvarchar](250) NOT NULL,
	[RevokedBy] [nvarchar](250) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Wallets]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Wallets](
	[WalletId] [uniqueidentifier] NOT NULL,
	[ConnectedIban] [nvarchar](40) NULL,
	[VatNumber] [nvarchar](25) NOT NULL,
	[WalletAccount] [nvarchar](40) NULL,
	[WalletName] [nvarchar](250) NOT NULL,
	[RegistrationDate] [datetime2](7) NOT NULL,
	[WalletAccountCreatedAt] [datetime2](7) NULL,
	[OwnerUserId] [nvarchar](250) NOT NULL,
	[OrganizationName] [nvarchar](250) NULL,
 CONSTRAINT [PK_Wallets] PRIMARY KEY CLUSTERED 
(
	[WalletId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WalletSubscriptions]    Script Date: 19/6/2024 9:59:55 πμ ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WalletSubscriptions](
	[SubscriptionId] [uniqueidentifier] NOT NULL,
	[WalletId] [uniqueidentifier] NOT NULL,
	[Amount] [decimal](10, 2) NOT NULL,
	[Duration] [smallint] NOT NULL,
	[PlanId] [smallint] NOT NULL,
	[AllowedUsers] [smallint] NOT NULL,
	[StartDate] [datetime2](7) NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
 CONSTRAINT [PK_WalletSubscriptions] PRIMARY KEY CLUSTERED 
(
	[SubscriptionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[ApproveTypes] ADD  CONSTRAINT [DF_ApproveTypes_id]  DEFAULT (newid()) FOR [id]
GO
ALTER TABLE [dbo].[DiscountTransactions] ADD  CONSTRAINT [DF_DiscountTransactions_DiscountTransactionId]  DEFAULT (newid()) FOR [DiscountTransactionId]
GO
ALTER TABLE [dbo].[Limits] ADD  CONSTRAINT [DF_Limits_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[Limits] ADD  DEFAULT ((1)) FOR [Active]
GO
ALTER TABLE [dbo].[SubscriptionPlans] ADD  CONSTRAINT [DF_SubscriptionPlans_SubscriptionPlanid]  DEFAULT (newid()) FOR [SubscriptionPlanid]
GO
ALTER TABLE [dbo].[SubscriptionPlanTypes] ADD  CONSTRAINT [DF_SubscriptionPlanTypes_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[TimeFrameTypes] ADD  CONSTRAINT [DF_TimeFrameTypes_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[TransactionStatuses] ADD  CONSTRAINT [DF_TransactionStatuses_id]  DEFAULT (newid()) FOR [id]
GO
ALTER TABLE [dbo].[TransactionTypes] ADD  CONSTRAINT [DF_TransactionTypes_id]  DEFAULT (newid()) FOR [id]
GO
ALTER TABLE [dbo].[UserPermissions] ADD  CONSTRAINT [DF_UserPermissions_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[WalletPermissions] ADD  CONSTRAINT [DF_WalletPermissions_Id]  DEFAULT (newid()) FOR [Id]
GO
ALTER TABLE [dbo].[Wallets] ADD  CONSTRAINT [DF_Wallets_walletId]  DEFAULT (newid()) FOR [WalletId]
GO
