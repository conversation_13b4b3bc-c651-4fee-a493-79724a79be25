﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Nbg.Ewallet.Api.Types.Payments;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Payments Response
/// </summary>
[DataContract]
public class PaymentsResponse
{
    /// <summary>
    /// Mass Transaction Id
    /// </summary>
    [DataMember(Name = "batchId")]
    public Guid BatchId { get; set; } = Guid.Empty;

    /// <summary>
    /// List of payments
    /// </summary>
    [DataMember(Name = "payments")]
    public List<ExtendedPaymentResponse> Payments { get; set; } = [];
}
