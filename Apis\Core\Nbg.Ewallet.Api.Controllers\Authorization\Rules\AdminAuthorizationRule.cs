﻿using Microsoft.AspNetCore.Mvc.Filters;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;

public class AdminAuthorizationRule : BaseAuthorizationRule
{
    public override void EnsureAuthorized(AuthorizationContext authContext, AuthorizationFilterContext context)
    {
        CheckAuthorizationContext(authContext);
        if (!authContext.ActivePermission.Admin) throw new PermissionNotFoundException();
        CheckWalletId(authContext, context);
    }
}
