﻿using System.Threading.Tasks;
using nbg.ewallet.repository.types;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.CosmosConnector.Types.Customer;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;

namespace Nbg.Ewallet.Api.Interfaces;

public interface IValidationService
{
    Task<CustomerData> ValidateCustomerDataAsync(
        Customer customerData,
        CustomerProductDetails customerProductDetails,
        CustomerAuthorizationLevel authCustomerData);
}
