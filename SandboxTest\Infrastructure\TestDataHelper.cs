using SandboxTest.Models;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Helper class for generating test data
    /// </summary>
    public static class TestDataHelper
    {
        private static readonly Random Random = new();

        /// <summary>
        /// Generates a unique wallet name for testing
        /// </summary>
        public static string GenerateUniqueWalletName()
        {
            return $"Test Wallet {Guid.NewGuid().ToString()[..8]}";
        }

        /// <summary>
        /// Generates a unique organization name for testing
        /// </summary>
        public static string GenerateUniqueOrganizationName()
        {
            return $"Test Organization {Guid.NewGuid().ToString()[..8]}";
        }

        /// <summary>
        /// Generates a random VAT number for testing
        /// </summary>
        public static string GenerateVatNumber()
        {
            return $"EL{Random.Next(*********, ********9)}";
        }

        /// <summary>
        /// Generates a random IBAN for testing
        /// </summary>
        public static string GenerateIban()
        {
            var accountNumber = Random.Next(********, ********);
            return $"GR{Random.Next(10, 99)}{Random.Next(1000, 9999)}{accountNumber}{Random.Next(100, 999)}";
        }

        /// <summary>
        /// Generates a random user ID for testing
        /// </summary>
        public static string GenerateUserId()
        {
            return Random.Next(1000000, 9999999).ToString();
        }

        /// <summary>
        /// Generates a random amount for testing
        /// </summary>
        public static decimal GenerateAmount(decimal min = 1, decimal max = 10000)
        {
            return Math.Round((decimal)(Random.NextDouble() * (double)(max - min) + (double)min), 2);
        }

        /// <summary>
        /// Creates a wallet registration request with test data
        /// </summary>
        public static object CreateWalletRegistrationRequest(string? walletName = null)
        {
            return new
            {
                WalletName = walletName ?? GenerateUniqueWalletName()
            };
        }

        /// <summary>
        /// Creates a user permission request with test data
        /// </summary>
        public static object CreateUserPermissionRequest(string userId, Guid walletId)
        {
            return new
            {
                UserId = userId,
                WalletId = walletId,
                Admin = true,
                Approve = true,
                BalanceView = true,
                Submit = true,
                TransactionView = true,
                InheritsAuthorizations = true,
                ExpirationDate = DateTime.UtcNow.AddYears(1),
                Limits = new[]
                {
                    new
                    {
                        Amount = GenerateAmount(100, 1000),
                        Timeframe = "DAILY",
                        TransactionType = "WalletToIBAN"
                    }
                }
            };
        }

        /// <summary>
        /// Creates a subscription request with test data
        /// </summary>
        public static object CreateSubscriptionRequest(string tier = "Premium")
        {
            return new
            {
                Tier = tier
            };
        }

        /// <summary>
        /// Creates a transfer request with test data
        /// </summary>
        public static object CreateTransferRequest(decimal amount, string targetIban)
        {
            return new
            {
                Amount = amount,
                Currency = "EUR",
                TargetIban = targetIban,
                Description = "Test transfer"
            };
        }

        /// <summary>
        /// Creates a payment request with test data
        /// </summary>
        public static object CreatePaymentRequest(decimal amount, string payeeCode)
        {
            return new
            {
                Amount = amount,
                Currency = "EUR",
                PayeeCode = payeeCode,
                Description = "Test payment"
            };
        }

        /// <summary>
        /// Creates a wallet load request with test data
        /// </summary>
        public static object CreateWalletLoadRequest(decimal amount)
        {
            return new
            {
                Amount = amount,
                Currency = "EUR",
                Description = "Test wallet load"
            };
        }

        /// <summary>
        /// Creates a wallet permission request with test data
        /// </summary>
        public static object CreateWalletPermissionRequest(Guid targetWalletId)
        {
            return new
            {
                ExternalWalletId = targetWalletId,
                Admin = false,
                Approve = true,
                BalanceView = true,
                Submit = true,
                TransactionView = true,
                ExpirationDate = DateTime.UtcNow.AddYears(1)
            };
        }
    }
}
