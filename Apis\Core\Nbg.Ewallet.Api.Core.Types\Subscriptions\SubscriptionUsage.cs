﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Subscriptions;

/// <summary>
/// SubscriptionUsage definition
/// </summary>
public class SubscriptionUsage
{
    /// <summary>
    /// Subscription Plan Id
    /// </summary>
    [DataMember(Name = "subscriptionId")]
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// List of free transactions per transaction type with their remaining balance
    /// </summary>
    [DataMember(Name = "usage")]
    public List<FreeTransaction> Usage { get; set; }
}
