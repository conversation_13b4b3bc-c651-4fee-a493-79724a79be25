﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.CorporateApi;

[DataContract]
public class AvailableProductsResponse
{
    /// <summary>List of product info</summary>
    [DataMember(Name = "products")]
    public List<ProductInfoResponse> Products { get; set; }
}

[DataContract]
public class ProductInfoResponse
{
    /// <summary>Product type</summary>
    [DataMember(Name = "productCode")]
    public string ProductCode { get; set; }

    /// <summary> Values </summary>
    [DataMember(Name = "values")]
    public List<CorpUserProductDetails> Values { get; set; }
}

[DataContract]
public class CorpUserProductDetails
{
    /// <summary>Product Number (eg Account Number, Loan Reference Number etc)</summary>
    [DataMember(Name = "number")]
    public string Number { get; set; }

    /// <summary>Currency</summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>CMS field "cardType" of Card Products</summary>
    [DataMember(Name = "cardType")]
    public string CardType { get; set; }
}
