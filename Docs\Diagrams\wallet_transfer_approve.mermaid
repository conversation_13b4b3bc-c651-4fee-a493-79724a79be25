sequenceDiagram
	autonumber
	Client->>EWallet.Proxy: /transfers/{walletId}/approve POST
	EWallet.Proxy->>EWallet.Core: /transfers/{walletId}/approve POST
    EWallet.Core->>EWallet.DB: SELECT
    note over EWallet.DB: DB Table:  Wallets
    EWallet.DB->>EWallet.Core: {Wallet}
    EWallet.Core->>EWallet.DB: SELECT
    note over EWallet.DB: DB Table:  Transactions
    EWallet.DB->>EWallet.Core: {transaction}
    EWallet.Core->>EWallet.DB: CHECK Approval Limit
    note over EWallet.DB: DB Table:  Limit, Transactions
	EWallet.Core->>CICS:  {JBCRER request}
	CICS->>EWallet.Core: {JBCRER response}
	EWallet.Core->>EWallet.Proxy: {transfer response}
	EWallet.Proxy->>Client: {transfer response}