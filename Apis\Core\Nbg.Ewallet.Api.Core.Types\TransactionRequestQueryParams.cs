using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types;

public class TransactionRequestQueryParams
{
    [DataMember(Name = "batchId")]
    public Guid? BatchId { get; set; }

    [DataMember(Name = "status")]
    public TransactionStatus? Status { get; set; }

    [DataMember(Name = "pageNumber")]
    public int PageNumber { get; set; } = 0;

    [DataMember(Name = "pageSize")]
    public int PageSize { get; set; } = 100;
}
