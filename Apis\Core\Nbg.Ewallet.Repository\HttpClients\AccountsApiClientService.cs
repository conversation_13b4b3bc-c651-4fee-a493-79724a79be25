﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ibank.Types;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.CoreApis;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.SubscriptionApi;
using Nbg.EWallet.Repository.Types.coreApis;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Repository.HttpClients;

public class AccountsApiClientService : AuditableClientService, IAccountsApiClientService
{
    private readonly AccountsApiClientSettings _configuration;
    private readonly IHostCommonConfigurationService _configurationService;
    private readonly IIbanAccountUtility _ibanAccountUtility;
    private readonly ILogger<AccountsApiClientService> _logger;
    private readonly HttpClient _httpClient;

    public AccountsApiClientService(
        IOptions<AccountsApiClientSettings> configuration,
        IHttpClientFactory httpClientFactory,
        IServiceAuditRepositoryService serviceAuditRepositoryService,
        IHostCommonConfigurationService configurationService,
        IIbanAccountUtility ibanAccountUtility,
        ILogger<AccountsApiClientService> logger)
        : base(serviceAuditRepositoryService, logger)
    {
        _configurationService = configurationService;
        _ibanAccountUtility = ibanAccountUtility;
        _logger = logger;
        _configuration = configuration.Value;
        _httpClient = httpClientFactory.CreateClient(_configuration.Client);
    }

    public override HttpClient WithHttpClient()
    {
        return _httpClient;
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="request"></param>
    /// <param name="executedAs"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task<Response<TransfersResponseBase>> ExecuteTransactionAsync(TransfersRequestBase request)
    {
        ArgumentNullException.ThrowIfNull(request);

        var result = new Response<TransfersResponseBase>();

        try
        {
            var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.CallGenericTransfer);
            var response = await SendAsync<TransfersResponseBase>(httpRequestMessage);
            result.Payload = response;
        }
        catch (CoreGenericException e)
        {
            _logger.LogError(e, "Error in ExecuteTransactionAsync: {Message}", e.Message);
            result.Exception = new ResponseMessage { Description = e.Message, Code = e.Code };
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error in ExecuteTransactionAsync: {Message}", e.Message);
            result.Exception = new ResponseMessage { Description = e.Message, Code = "999" };
        }

        return result;
    }

    public async Task<GetAccountBalanceResponse> GetAccountBalanceAsync(GetAccountBalanceRequest request)
    {
        var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.GetAccountDetails);
        var response = await SendAsync<GetAccountBalanceResponse>(httpRequestMessage);
        return response;
    }

    public async Task<bool> ValidateUserAccount(string userId, string ibanAccount)
    {
        var response = await GetAccountsAsync(new BalancesRequest { UserID = userId, });
        //but why !!!!???
        //if (response == null) return true;
        var account = await _ibanAccountUtility.GetAccountFromIbanAsync(ibanAccount);
        return response.Accounts.Select(x => x.Account).Contains(account);
    }

    public async Task<BalancesResponse> GetAccountsAsync(BalancesRequest request)
    {
        var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.Accounts);
        var response = await SendAsync<BalancesResponse>(httpRequestMessage);
        return response;
    }

    public async Task<AccountsFullResponse> GetAccountsFullAsync(AccountsFullRequest request)
    {
        var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.AccountsFull);
        var response = await SendAsync<List<AccountFull>>(httpRequestMessage);
        return new() { Accounts = response };
    }

    public async Task<OpenAccountResponse> OpenAccountAsync(OpenAccountRequest request)
    {
        var productCode = await _configurationService.GetProductCodeAsync();
        var branch = await _configurationService.GetBranchAsync();
        request.AccountType = productCode;
        request.CustomerBranch = branch;
        request.ServiceBranch = branch;
        var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.OpenAccount);
        var response = await SendAsync<OpenAccountResponse>(httpRequestMessage);
        return response;
    }

    public async Task<TransferExpensesCommissionsResponse> CalculateTransferExpensesCommissionsAsync(TransferExpensesCommissionsRequest request)
    {
        var httpRequestMessage = request.Wrap().ToHttpRequestMessage(_configuration.CalculateTransferExpensesCommissions);
        var response = await SendAsync<TransferExpensesCommissionsResponse>(httpRequestMessage);
        return response;
    }

    public async Task<byte[]> RetrieveWalletStatementPdfExport(string userId, string account, StatementRequestQueryParams request)
    {
        var jsonContent = new StatementRequestQueryParams
        {
            DateFrom = request.DateFrom,
            DateTo = request.DateTo,
            Account = account,
            Currency = "EUR",
            UserId = userId
        };

        var wrappedRequest = jsonContent.Wrap();

        var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, _configuration.GetStatementsPdfExport)
        {
            Content = new StringContent(JsonSerializer.Serialize(wrappedRequest), Encoding.UTF8, "application/json")
        };

        var response = await SendAsync<byte[]>(httpRequestMessage);
        return response;
    }
}

public class SubscriptionApiClientService : AuditableClientService, ISubscriptionApiClientService
{
    private readonly SubscriptionApiClientSettings _configuration;
    private readonly HttpClient _httpClient;

    public SubscriptionApiClientService(
        IOptions<SubscriptionApiClientSettings> configuration,
        IHttpClientFactory httpClientFactory,
        IServiceAuditRepositoryService serviceAuditRepositoryService,
        ILogger<SubscriptionApiClientService> logger)
        : base(serviceAuditRepositoryService, logger)
    {
        _configuration = configuration.Value;
        _httpClient = httpClientFactory.CreateClient(_configuration.Client);
    }

    public override HttpClient WithHttpClient()
    {
        return _httpClient;
    }

    public async Task<SubscriptionApiResponse> CreateSubscriptionAsync(CreateSubscriptionRequest request)
    {
        //var httpRequestMessage = request.ToHttpRequestMessage(_configuration.CreateSubscription);
        //var response = await SendAsync<SubscriptionApiResponse>(httpRequestMessage);
        //return response;
        return MockResponse;
    }

    public async Task<SubscriptionApiResponse> GetSubscriptionAsync(Guid subscriptionId)
    {
        //var requestMessage = new HttpRequestMessage(HttpMethod.Get, $"{_configuration.CreateSubscription}/{subscriptionId}");
        //var response = await SendAsync<SubscriptionApiResponse>(requestMessage);
        //return response;
        return MockResponse;
    }

    public async Task<SubscriptionApiResponse> UpdateSubscriptionAsync(UpdateSubscriptionRequest request)
    {
        //var httpRequestMessage = request.ToHttpRequestMessage(_configuration.UpdateSubscription);
        //var response = await SendAsync<SubscriptionApiResponse>(httpRequestMessage);
        //return response;
        return MockResponse;
    }

    public async Task<SubscriptionApiResponse> OptOutSubscriptionAsync(OptOutSubscriptionRequest request)
    {
        //var httpRequestMessage = request.ToHttpRequestMessage(_configuration.OptOutSubscription);
        //var response = await SendAsync<SubscriptionApiResponse>(httpRequestMessage);
        //return response;
        return MockResponse;
    }

    public async Task<AvailableTiersApiResponse> GetAvailableSubscriptionTiersAsync()
    {
        //var requestMessage = new HttpRequestMessage(HttpMethod.Get, $"{_configuration.AvailableSubscriptionTiers}");
        //var response = await SendAsync<AvailableTiersApiResponse>(requestMessage);
        //return response;

        return new AvailableTiersApiResponse()
        {
            AvailableTiers = [
                new AvailableTier() { Tier = SubscriptionTier.Free, SubscriptionBundles = MockBundles(50, 30) },
                new AvailableTier() { Tier = SubscriptionTier.Basic, SubscriptionBundles = MockBundles(150, 70) },
                new AvailableTier() { Tier = SubscriptionTier.Premium, SubscriptionBundles = MockBundles(500, 300) },
            ]
        };
    }

    private SubscriptionApiResponse MockResponse => new SubscriptionApiResponse
    {
        Amount = 1000,
        StartDate = DateTime.Now,
        EndDate = DateTime.Now.AddMonths(6),
        DueAmount = 0,
        OptOut = false,
        PaymentDue = DateTime.Now.AddMonths(1),
        Status = SubscriptionStatus.Trial,
        SubscriptionId = Guid.NewGuid(),
        Tier = SubscriptionTier.Free,
        SubscriptionBundles =
            [
                new SubscriptionBundle
                {
                    Value = 70,
                    TransactionType = SubscriptionTransactionType.Transfer,
                },
                new SubscriptionBundle
                {
                    Value = 50,
                    TransactionType = SubscriptionTransactionType.Payment,
                }
            ]
    };

    private List<SubscriptionBundle> MockBundles(int value1, int value2) => [
                new SubscriptionBundle
                {
                    Value = value1,
                    TransactionType = SubscriptionTransactionType.Transfer,
                },
                new SubscriptionBundle
                {
                    Value = value2,
                    TransactionType = SubscriptionTransactionType.Payment,
                }
            ];
}
