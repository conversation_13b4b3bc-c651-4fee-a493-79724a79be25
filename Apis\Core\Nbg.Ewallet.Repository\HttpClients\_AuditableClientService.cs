﻿using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Repository.CoreApis;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Repository.HttpClients;

public abstract class AuditableClientService
{
    private readonly IServiceAuditRepositoryService _serviceAuditRepositoryService;
    private readonly ILogger _logger;

    protected AuditableClientService(IServiceAuditRepositoryService serviceAuditRepositoryService, ILogger logger)
    {
        _serviceAuditRepositoryService = serviceAuditRepositoryService;
        _logger = logger;
    }

    public abstract HttpClient WithHttpClient();

    /// <summary>
    ///
    /// </summary>
    /// <typeparam name="TResponse"></typeparam>
    /// <param name="httpRequestMessage"></param>
    /// <returns></returns>
    /// <exception cref="CoreGenericException"></exception>
    public async Task<TResponse> SendAsync<TResponse>(HttpRequestMessage httpRequestMessage)
    {
        var client = WithHttpClient();
        var auditEntry = await _serviceAuditRepositoryService.AuditBeforeExecAsync(httpRequestMessage);
        using var response = await client.SendAsync(httpRequestMessage);
        await _serviceAuditRepositoryService.AuditAfterExecAsync(auditEntry, response);

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogError("Request failed with status code {StatusCode}", response.StatusCode);
            throw new CoreGenericException($"Request Failed with status code {response.StatusCode}");
        }

        if (typeof(TResponse) == typeof(byte[]))
        {
            return (TResponse)(object)await response.Content.ReadAsByteArrayAsync();
        }

        var res = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<Response<TResponse>>(res);
        if (result.Exception == null)
            return result.Payload;
        _logger.LogError("API Error: {ExceptionCode} - {ExceptionDescription}", result.Exception.Code, result.Exception.Description);
        throw new CoreGenericException(result.Exception.Code, $"API Error: {result.Exception.Description}");
    }
}
