sequenceDiagram
    autonumber
    participant Client
    participant eWalletApi
    participant eWalletDB
    Client->>eWalletApi: GET /wallet/[walletId]/user-permissions
    eWalletApi-->>Client: { all user permissions }
    note left of Client: Client selects UserIDs<br/>to update permssions
    note over Client, eWalletApi: A list of permissions to update is sent
    Client->>eWalletApi: POST /wallet/[walletId]/user-permissions
    eWalletApi->>eWalletDB: Create new enrties for <br/>new set of permissions
    eWalletApi-->>Client: updated set of permissions
