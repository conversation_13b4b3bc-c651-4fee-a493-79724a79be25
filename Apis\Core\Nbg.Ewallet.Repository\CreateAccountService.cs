﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.EWallet.Repository.Types.coreApis;

namespace Nbg.Ewallet.Repository;

public class CreateAccountService : ICreateAccountService
{
    private readonly ILogger<CreateAccountService> _logger;
    private readonly IMainFrameConnector _mainFrameConnector;
    private readonly IAccountsApiClientService _accountsApiClientService;
    private readonly CreateAccountSettings _createAccountSettings;
    private readonly IHostCommonConfigurationService _configurationService;

    public CreateAccountService(
        ILogger<CreateAccountService> logger,
        IMainFrameConnector mainFrameConnector,
        IAccountsApiClientService accountsApiClientService,
        IOptions<CreateAccountSettings> createAccountOptions,
        IHostCommonConfigurationService configurationService)
    {
        _logger = logger;
        _mainFrameConnector = mainFrameConnector;
        _accountsApiClientService = accountsApiClientService;
        _createAccountSettings = createAccountOptions.Value;
        _configurationService = configurationService;
    }

    public async Task<OpenAccountResponse> OpenAccountAsync(OpenAccountRequest request)
    {
        //config enable/disable open account validations
        if (_createAccountSettings.EnableValidations)
        {
            var result = await _accountsApiClientService.OpenAccountAsync(request);
            return result;
        }

        var productCode = await _configurationService.GetProductCodeAsync();
        var createCaAccount = await _mainFrameConnector.CreateAccountAsync(request.UserCra, request.UserID, productCode);
        return new OpenAccountResponse
        {
            CustomerFullName = "",
            Iban = createCaAccount.AccountDetails.Iban,
            NewAccountNumber = createCaAccount.AccountDetails.AccountInfo.AccountId,
            OpenDate = DateTime.UtcNow
        };
    }
}
