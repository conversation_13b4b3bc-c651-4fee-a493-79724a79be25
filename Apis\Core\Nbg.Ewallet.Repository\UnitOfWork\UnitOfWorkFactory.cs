﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.NetCore.Data;

namespace Nbg.Ewallet.Repository.UnitOfWork;

public class UnitOfWorkFactory : IUnitOfWorkFactory
{
    private readonly IDbConnectorFactory _dbConnectorFactory;
    private readonly IOptions<UnitOfWorkOptions> _options;
    private readonly ILogger<UnitOfWork> _logger;

    public UnitOfWorkFactory(
        IDbConnectorFactory dbConnectorFactory,
        IOptions<UnitOfWorkOptions> options,
        ILogger<UnitOfWork> logger)
    {
        _dbConnectorFactory = dbConnectorFactory;
        _options = options;
        _logger = logger;
    }

    public IUnitOfWork Create()
    {
        return new UnitOfWork(_dbConnectorFactory, _options, _logger);
    }
}
