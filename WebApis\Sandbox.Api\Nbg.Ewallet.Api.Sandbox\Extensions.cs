﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Nbg.Ewallet.Api.Sandbox.Filters;
using Nbg.NetCore.Data;
using Nbg.NetCore.Healthchecks.Checks;
using Nbg.NetCore.HttpExceptions;
using Swashbuckle.AspNetCore.Swagger;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.SwaggerUI;

namespace Nbg.Ewallet.Api.Sandbox;

public static class Extensions
{
    public static string EnvironmentValue => Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLower();

    public static string SandboxScope => "sandbox-ewallet-api-v1";
    public static string ProductionScope => "e-wallet-api-v1";
    public static string ApiName => "E-Wallet";
    public static string SandboxServerUrl => "https://apis.nbg.gr/sandbox/ewallet/oauth2/v1";
    public static string ProductServerUrl => "https://services.nbg.gr/apis/ewallet/v1";

    public static Uri Append(this Uri uri, params string[] paths)
    {
        if (string.IsNullOrWhiteSpace(uri?.AbsoluteUri)) return null;

        if (paths == null || paths.Length == 0) return uri;

        return new Uri(paths.Aggregate(uri.AbsoluteUri, (current, path) => $"{current.TrimEnd('/')}/{path.TrimStart('/')}"));
    }

    public static MvcOptions SetMvcOptions(this MvcOptions options)
    {
        options.ReturnHttpNotAcceptable = true;

        //options.Conventions.Add(new AuthorizeFilterConvention());

        options.Filters.Add(new ProducesResponseTypeAttribute(typeof(ErrorResponse), StatusCodes.Status400BadRequest));
        options.Filters.Add(new ProducesResponseTypeAttribute(StatusCodes.Status401Unauthorized));
        options.Filters.Add(new ProducesResponseTypeAttribute(StatusCodes.Status403Forbidden));
        options.Filters.Add(new ProducesResponseTypeAttribute(StatusCodes.Status404NotFound));
        options.Filters.Add(new ProducesResponseTypeAttribute(StatusCodes.Status405MethodNotAllowed));
        options.Filters.Add(new ProducesResponseTypeAttribute(StatusCodes.Status406NotAcceptable));
        options.Filters.Add(new ProducesResponseTypeAttribute(typeof(ErrorResponse), StatusCodes.Status500InternalServerError));
        return options;
    }

    public static IServiceCollection RegisterGenericServices(this IServiceCollection services)
    {
        services.AddDbConnectorFactory();
        services.AddOptions();
        services.AddHttpClient("client");
        services.AddHealthChecks().AddDbConnectionCheck();
        services.AddEndpointsApiExplorer();

        services.AddHttpContextAccessor();
        services.AddMemoryCache();
        services.AddDistributedMemoryCache(options =>
        {
            options.ExpirationScanFrequency = TimeSpan.FromMinutes(10);
        });

        //services.AddiBankIdentityOAuth2();

        return services;
    }

    public static IServiceCollection AddSwaggerServiceCollection(this IServiceCollection services, WebApplicationBuilder builder)
    {
        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1.0",
                new OpenApiInfo
                {
                    Title = "NBG " + ApiName + " API Specification",
                    Version = "v1.0",
                    Description = LoadSwaggerDescription(builder),
                    Contact = new OpenApiContact
                    {
                        Name = "NBG",
                        Url = new Uri("https://developer.nbg.gr/"),
                        Email = "<EMAIL>"
                    }
                });
            options.AddSwaggerSecurityDefinitions(builder.Configuration);
            options.DocInclusionPredicate((name, api) => true);
            //options.OperationFilter<AddRequiredHeaderParameter>();
            options.OperationFilter<TagByApiExplorerSettingsOperationFilter>();
            //options.OperationFilter<SecurityOperationFilter>();

            options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Nbg.Ewallet.Api.Controllers.xml"));
            options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Nbg.Ewallet.Api.Types.xml"));
        });

        return services;
    }

    private static string LoadSwaggerDescription(WebApplicationBuilder webApplicationBuilder)
    {
        var webRootPath = webApplicationBuilder.Environment.WebRootPath;
        var swaggerFile = Path.Combine(webRootPath, "SwaggerDescription.md");

        using var streamReader = File.OpenText(swaggerFile);
        return streamReader.ReadToEnd();
    }

    private static void AddSwaggerSecurityDefinitions(this SwaggerGenOptions options, IConfiguration configuration)
    {
        options.AddSecurityDefinition("Client-Id", new OpenApiSecurityScheme
        {
            Type = SecuritySchemeType.ApiKey,
            Description = "Application's Id",
            Name = "Client-Id",
            In = ParameterLocation.Header
        });

        var authority = "https://my.nbg.gr/identity";

        options.AddSecurityDefinition("Authorization-Code-Token", new OpenApiSecurityScheme
        {
            Type = SecuritySchemeType.OAuth2,
            Description = "OAuth2 Authorization Code Flow",
            Flows = new OpenApiOAuthFlows
            {
                AuthorizationCode = new OpenApiOAuthFlow
                {
                    AuthorizationUrl = new Uri(authority).Append("/connect/authorize"),
                    TokenUrl = new Uri(authority).Append("/connect/token"),
                    Scopes = new Dictionary<string, string>
                    {
                        {SandboxScope + " offline_access", "Sandbox Scope"},
                        {ProductionScope + " offline_access", "Production Scope"}
                    }
                }
            }
        });

        options.AddSecurityDefinition("Client-Credentials-Token", new OpenApiSecurityScheme
        {
            Type = SecuritySchemeType.OAuth2,
            Description = "OAuth2 Client Credentials Flow",
            Flows = new OpenApiOAuthFlows
            {
                ClientCredentials = new OpenApiOAuthFlow
                {
                    TokenUrl = new Uri(authority).Append("/connect/token"),
                    Scopes = new Dictionary<string, string>
                    {
                        {SandboxScope, "Sandbox Scope"},
                        {ProductionScope, "Production Scope"}
                    }
                }
            }
        });
    }

    public static WebApplication UseSwaggerApp(this WebApplication app)
    {
        app.UseSwagger(SetSwaggerOptions);
        app.UseSwaggerUI(SetSwaggerUiOptions);

        return app;
    }

    private static void SetSwaggerOptions(SwaggerOptions options)
    {
        options.PreSerializeFilters.Add((swaggerDoc, httpReq) =>
        {
            swaggerDoc.Servers = new List<OpenApiServer>
            {
                new OpenApiServer
                {
                    Description = "Sandbox Server",
                    Url = SandboxServerUrl
                },
                new OpenApiServer
                {
                    Description = "Production Server",
                    Url = ProductServerUrl
                }
            };
        });
    }

    private static void SetSwaggerUiOptions(SwaggerUIOptions options)
    {
        options.SwaggerEndpoint("swagger/v1.0/swagger.yaml", "NBG " + ApiName + " API Specification");
        options.RoutePrefix = string.Empty;

        options.DefaultModelExpandDepth(1);
        options.DefaultModelRendering(ModelRendering.Model);
        options.DocExpansion(DocExpansion.List);
    }
}
