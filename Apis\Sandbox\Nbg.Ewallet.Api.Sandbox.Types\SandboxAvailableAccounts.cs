﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// Represents an available account.
/// </summary>
public class SandboxAvailableAccount
{
    [DataMember(Name = "userId")]
    public string UserId { get; set; }
    /// <summary>Account IBAN.</summary>
    [DataMember(Name = "iban")]
    public string IBAN { get; set; }
    /// <summary>Friendly name</summary>
    [DataMember(Name = "alias")]
    public string? Alias { get; set; }
    /// <summary>Available balance</summary>
    [DataMember(Name = "availableBalance")]
    public decimal AvailableBalance { get; set; }
    /// <summary>Ledger balance</summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }
    /// <summary>Currency of the account</summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }
}
