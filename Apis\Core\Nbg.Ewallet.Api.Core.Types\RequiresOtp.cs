﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Sms otp filter required fields
/// </summary>
[DataContract]
public class RequiresOtp
{
    /// <summary>
    /// The sms OTP number the user has keyed in
    /// </summary>
    [DataMember(Name = "tanNumber")]
    public string TanNumber { get; set; }

    /// <summary>
    /// The sms OTP flag
    /// </summary>
    [DataMember(Name = "isSmsOtp")]
    public bool? IsSmsOtp { get; set; } = true;
}
