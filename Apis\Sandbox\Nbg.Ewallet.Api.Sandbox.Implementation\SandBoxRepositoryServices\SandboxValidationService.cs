﻿using System.Threading.Tasks;
using nbg.ewallet.repository.types;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.CosmosConnector.Types.Customer;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxValidationService : IValidationService
{
    public async Task<CustomerData> ValidateCustomerDataAsync(
        Customer customerData,
        CustomerProductDetails customerProductDetails,
        CustomerAuthorizationLevel authCustomerData)
    {
        var vatNumber = SandBoxRandomDataHelper.GenerateVatNumber();
        var result = new CustomerData
        {
            IsActive = true,
            HasMissingInformation = false,
            VatNumber = vatNumber,
            Accounts =
            [
                new CustomerAccount
                {
                    Currency = "EUR", Description = "Test Account", Iban = "***************************"
                }
            ],
            Name = $"Sandbox Wallet {vatNumber}",
            ValidationControls = [],
            isCorporate = false,
            isFPbutNotBusiness = false
        };

        return await Task.FromResult(result);
    }
}
