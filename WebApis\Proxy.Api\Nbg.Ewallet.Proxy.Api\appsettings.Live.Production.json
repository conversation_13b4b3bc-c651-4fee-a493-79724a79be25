{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Warning"}}, "ConnectionStrings": {"Audit": "Server=IBPrdSqlSrv,2544;database=InternetBankingAudit;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False", "IBank": "Server=IBPrdSqlSrv,2544;database=InternetBanking;Integrated Security=SSPI;Persist Security Info=False;Encrypt=False", "uniqueMessage": "Server=S0000A1700,2544;initial catalog=BigDataIbank;Integrated Security=SSPI;Persist Security Info=False;Connection Timeout=3;Encrypt=false", "appSettings": "Server=IBPrdSqlSrv,2544;database=APIConfig;Integrated Security=SSPI;Persist Security Info=False;Encrypt=false"}, "AuditMiddleware": {"ConnectionName": "Audit", "RequestIdHeaderName": "request-id", "UseSqlServer": false, "UseBigDataClient": true, "SetPrimarySqlServer": false, "SetPrimaryBigDataClient": true, "ServiceNamePrefix": "nbg.ewallet.api/v1", "DefaultApplicationId": "2DBA0863-647C-481E-93E9-6543D17B0F74", "IgnoredPaths": ["/favicon.ico"], "RequestHttpHeaders": ["ByPassHeaders"], "ResponseHttpHeaders": ["ByPassHeaders"], "SensitiveProperties": []}, "MultiSinkAuditWriter": {"primarySink": "kafka", "sinks": {"kafka": {"kafkaClient": {"kafkaTopic": "prod-trlog-ibank-ingestion", "fallbackSqlConnectionString": "data source=IBPrdSqlSrv,2544;initial catalog=BigDataIbank;Integrated Security=SSPI;Persist Security Info=False;Connection Timeout=3;Encrypt=false", "producerOptions": {"bootstrap.servers": "lkc-d9z3w7-p8xdjo.westeurope.azure.glb.confluent.cloud", "security.protocol": "SASL_SSL", "sasl.username": "B4R75XEVQSJROMQQ", "sasl.mechanism": "PLAIN", "delivery.timeout.ms": "10000", "batch.size": "********", "message.max.bytes": "********", "acks": "1", "compression.type": "snappy", "linger.ms": "50"}}}}}, "Authentication": {"Authority": "https://my.nbg.gr/identity", "ApiName": "AD265730-82FF-42E8-8022-EF983B856C46", "ApiSecret": "1C0CD950-2616-4A06-B591-36B6010380BE", "RequiredScope": "ewallet-api-v1", "EnableCaching": true, "CacheDuration": "00:30:00"}, "ProxyMiddleware": {"Variables": {"CoreServiceUrl": "https://coreapplayergeneric.nbg.gr/nbg.ewallet.core.api"}}, "Healthcheck": {"CheckAllocatedMemory": true, "CheckUris": true, "UriCheckOptions": [{"Uri": "https://coreapplayergeneric.nbg.gr/nbg.ewallet.core.api/health", "HttpClientName": "default", "HttpMethod": "GET", "TimeOutMilliseconds": 100000, "ExpectedCodes": [200]}]}, "CorporateUserAuthorizationPolicy": {"ApplyPolicy": true}, "Sca": {"BaseAddress": "https://CoreAppLayer.nbg.gr", "ChallengeEndpoint": "/Nbg.NetCore.Sca.Core/sca/challenge", "ValidateEndpoint": "/Nbg.NetCore.Sca.Core/sca/validate", "DisableScaWhitelisting": false, "ScaTokenExpirationInSeconds": 300}, "SmsOtpSettings": {"EnableSmsOtp": true}, "UserIdRequestTransformer": {"HttpHeadersMappings": {"UserId": {"SourcePath": "ibank_user_id", "ActionToPerform": "Create", "ReplaceIfExists": true, "Source": "<PERSON><PERSON><PERSON>", "ThrowIfNotFoundFromSource": true}}}}