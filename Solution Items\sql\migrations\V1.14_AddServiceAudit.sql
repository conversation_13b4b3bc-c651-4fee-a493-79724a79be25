SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ServiceAudit](
	[Id] [uniqueidentifier] NOT NULL,
	[Timestamp] [datetime2](7) NOT NULL,
	[Host] [varchar](100) NOT NULL,
	[ServiceName] [varchar](400) NOT NULL,
	[Application] [uniqueidentifier] NULL,
	[ClientRequestPath] [varchar](200) NULL,
	[ClientSession] [varchar](100) NULL,
	[RequestTextData] [nvarchar](max) NULL,
	[ResponseTextData] [nvarchar](max) NULL,
	[ErrorData] [nvarchar](max) NULL,
	[ExecutionTime] [time](7) NULL,
	[EndTime] [datetime2](7) NULL,
	[ExtraTextData] [nvarchar](max) NULL,
 CONSTRAINT [PK_ServiceAudit_1] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Title', @value=N'ServiceAudit' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ServiceAudit'
GO

