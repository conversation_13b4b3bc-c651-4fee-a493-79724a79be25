﻿using System;
using System.Net;

namespace Nbg.Ewallet.Repository.Types.Exceptions;

public abstract class ClientErrorException : Exception
{
    private readonly string _errorCode = "UNKNOWN";
    public virtual string GetErrorCode() => _errorCode;

    private readonly HttpStatusCode _httpStatus = HttpStatusCode.BadRequest;
    public virtual HttpStatusCode GetHttpStatus() => _httpStatus;

    protected ClientErrorException(string message, string errorCode, HttpStatusCode httpStatus) : base(message)
    {
        _errorCode = errorCode;
        _httpStatus = httpStatus;
    }
}

public class InvalidInputException : ClientErrorException
{
    public InvalidInputException() : base("Invalid input", ExceptionErroCodes.EW100, HttpStatusCode.BadRequest) { }
}

public class GenericException : ClientErrorException
{
    public GenericException(string message = null) : base(message ?? "Generic Exception", ExceptionErroCodes.EW000, HttpStatusCode.InternalServerError) { }
}

public class NotFoundException : ClientErrorException
{
    public NotFoundException() : base("Resource not found", ExceptionErroCodes.EW001, HttpStatusCode.NotFound) { }
}

public class WalletNotFoundException : ClientErrorException
{
    public WalletNotFoundException() : base("Wallet not found", ExceptionErroCodes.EW002, HttpStatusCode.NotFound) { }
}

public class AccountNotFoundException : ClientErrorException
{
    public AccountNotFoundException() : base("Account not found on user accounts", ExceptionErroCodes.EW131, HttpStatusCode.NotFound) { }
}

public class IbanNotFoundException : ClientErrorException
{
    public IbanNotFoundException() : base("Credit IBAN not found", ExceptionErroCodes.EW004, HttpStatusCode.NotFound) { }
}

public class OrganizationNotFoundException : ClientErrorException
{
    public OrganizationNotFoundException() : base("Organization not found", ExceptionErroCodes.EW006, HttpStatusCode.NotFound) { }
}

public class PermissionNotFoundException : ClientErrorException
{
    public PermissionNotFoundException() : base("Permissions not found", ExceptionErroCodes.EW101, HttpStatusCode.Forbidden) { }
}

public class InvalidSubscriptionPlanException : ClientErrorException
{
    public InvalidSubscriptionPlanException() : base("Invalid subscription plan", ExceptionErroCodes.EW007, HttpStatusCode.BadRequest) { }
}

public class UserNotFoundException : ClientErrorException
{
    public UserNotFoundException() : base("User not found", ExceptionErroCodes.EW005, HttpStatusCode.NotFound) { }
}

public class InactiveSubscriptionException : ClientErrorException
{
    public InactiveSubscriptionException() : base("Inactive Subscription", ExceptionErroCodes.EW201, HttpStatusCode.BadRequest) { }
}

public class WalletAlreadyExistsException : ClientErrorException
{
    public WalletAlreadyExistsException() : base("Wallet already exists", ExceptionErroCodes.EW301, HttpStatusCode.BadRequest) { }
}

public class InsufficientUserRightsException : ClientErrorException
{
    public InsufficientUserRightsException() : base("Insufficient user rights", ExceptionErroCodes.EW102, HttpStatusCode.Unauthorized) { }
}

public class RegisteredSubscriptionNotFoundException : ClientErrorException
{
    public RegisteredSubscriptionNotFoundException() : base("Cannot Create account without a registered subscription", ExceptionErroCodes.EW104, HttpStatusCode.Unauthorized) { }
}

public class IncompleteWalletRegistrationException : ClientErrorException
{
    public IncompleteWalletRegistrationException() : base("Incomplete wallet registration", ExceptionErroCodes.EW105, HttpStatusCode.Unauthorized) { }
}

public class OnlyOnePermissionPerWalletIsAllowedException : ClientErrorException
{
    public OnlyOnePermissionPerWalletIsAllowedException() : base("Only one permission per walletid is allowed", ExceptionErroCodes.EW106, HttpStatusCode.Unauthorized) { }
}

public class OnlyOnePermissionPerUserIsAllowedException : ClientErrorException
{
    public OnlyOnePermissionPerUserIsAllowedException() : base("Only one permission per userid is allowed", ExceptionErroCodes.EW108, HttpStatusCode.Unauthorized) { }
}

public class RequestNotFoundException : ClientErrorException
{
    public RequestNotFoundException() : base("Authorization Request Not Found", ExceptionErroCodes.EW107, HttpStatusCode.BadRequest) { }
}

public class PermissionAlreadyExistsException : ClientErrorException
{
    public PermissionAlreadyExistsException() : base("Permissions already exists", ExceptionErroCodes.EW111, HttpStatusCode.Forbidden) { }
}

public class RegistrationElegibilityException : ClientErrorException
{
    public RegistrationElegibilityException() : base("User not eligible to register", ExceptionErroCodes.EW010, HttpStatusCode.Unauthorized) { }
}

public class PermissionToOwnWalletException : ClientErrorException
{
    public PermissionToOwnWalletException() : base("cannot add permission to own wallet", ExceptionErroCodes.EW115, HttpStatusCode.Unauthorized) { }
}

public class OnlyOneTransactionTypeInLimitsIsAllowed : ClientErrorException
{
    public OnlyOneTransactionTypeInLimitsIsAllowed() : base("Only One TransactionType in Limits is allowed", ExceptionErroCodes.EW116, HttpStatusCode.BadRequest) { }
}

public class CicsGenericExeption : ClientErrorException
{
    public CicsGenericExeption(string msg) : base($"Cics - {msg}", ExceptionErroCodes.EW117, HttpStatusCode.BadRequest) { }
}

public class OwnWalletAuthorizationException : ClientErrorException
{
    public OwnWalletAuthorizationException() : base("Cannot ask Permission for own wallet", ExceptionErroCodes.EW118, HttpStatusCode.Forbidden) { }
}

public class AuthorizationRequestExistsException : ClientErrorException
{
    public AuthorizationRequestExistsException() : base("an AuthorizationRequest for the target walletid Exists", ExceptionErroCodes.EW119, HttpStatusCode.Forbidden) { }
}

[Obsolete("This exception is not used now", true)]
public class AccountNotRegisteredOnWalletException : ClientErrorException
{
    public AccountNotRegisteredOnWalletException() : base("Account not registered on Wallet", ExceptionErroCodes.EW120, HttpStatusCode.BadRequest) { }
}

public class AccountIsNotAvailableToUser : ClientErrorException
{
    public AccountIsNotAvailableToUser() : base("Account is not available to User", ExceptionErroCodes.EW136, HttpStatusCode.BadRequest) { }
}

public class SourceAccountNotTheSame : ClientErrorException
{
    public SourceAccountNotTheSame() : base("All transactions should have the same source account", ExceptionErroCodes.EW137, HttpStatusCode.BadRequest) { }
}

public class EmptyBatch : ClientErrorException
{
    public EmptyBatch() : base("Batch must contain at least one item", ExceptionErroCodes.EW138, HttpStatusCode.BadRequest) { }
}

public class SourceAccountMissing : ClientErrorException
{
    public SourceAccountMissing() : base("Source account is missing or not set", ExceptionErroCodes.EW139, HttpStatusCode.BadRequest) { }
}

public class ApproveTransferPermissionException : ClientErrorException
{
    public ApproveTransferPermissionException() : base($"User not eligible to approve transfer or approve limit reached.", ExceptionErroCodes.EW121, HttpStatusCode.BadRequest) { }
}

public class ApproveTransferWrongStatusException : ClientErrorException
{
    public ApproveTransferWrongStatusException() : base($"Transaction status not on pending_approval", ExceptionErroCodes.EW122, HttpStatusCode.BadRequest) { }
}

public class TransferExecutionException : ClientErrorException
{
    public TransferExecutionException(string message) : base(message, ExceptionErroCodes.EW003, HttpStatusCode.BadRequest) { }
}

public class TransferExecutionExceptionBalance : ClientErrorException
{
    public TransferExecutionExceptionBalance(string message) : base(message, ExceptionErroCodes.EW008, HttpStatusCode.BadRequest) { }
}

public class TransferExecutionExceptionInput : ClientErrorException
{
    public TransferExecutionExceptionInput(string message) : base(message, ExceptionErroCodes.EW009, HttpStatusCode.BadRequest) { }
}

public class PaymentExecutionExceptionInput : ClientErrorException
{
    public PaymentExecutionExceptionInput(string message) : base(message, ExceptionErroCodes.EW011, HttpStatusCode.BadRequest) { }
}

public class SubmitLimitReached : ClientErrorException
{
    public SubmitLimitReached() : base("Submit limit reached for user. Transaction not submitted.", ExceptionErroCodes.EW123, HttpStatusCode.BadRequest) { }
}

public class BicAccountMismatchException : ClientErrorException
{
    public BicAccountMismatchException() : base("Bank BIC and Receiver Account do not match", ExceptionErroCodes.EW124, HttpStatusCode.BadRequest) { }
}

public class TransferTypeAccountMismatchException : ClientErrorException
{
    public TransferTypeAccountMismatchException() : base("TransferType and Receiver Account do not match", ExceptionErroCodes.EW125, HttpStatusCode.BadRequest) { }
}

public class TransferTypeBicMismatchException : ClientErrorException
{
    public TransferTypeBicMismatchException() : base("Bank BIC and TransferType do not match", ExceptionErroCodes.EW126, HttpStatusCode.BadRequest) { }
}

public class WalletAccountNotFoundException : ClientErrorException
{
    public WalletAccountNotFoundException() : base("Wallet account not found", ExceptionErroCodes.EW129, HttpStatusCode.BadRequest) { }
}

public class CannotRevokeOwnPermissionsxception : ClientErrorException
{
    public CannotRevokeOwnPermissionsxception() : base("Cannot revoke my own permissions", ExceptionErroCodes.EW130, HttpStatusCode.BadRequest) { }
}

public class InsufficientBalanceException : ClientErrorException
{
    public InsufficientBalanceException() : base("Not enough balance to complete the operation.", ExceptionErroCodes.EW132, HttpStatusCode.BadRequest) { }
}

public class InvalidOrganizationIdException : ClientErrorException
{
    public InvalidOrganizationIdException() : base("Invalid paymentOrgIdentificationId", ExceptionErroCodes.EW133, HttpStatusCode.BadRequest) { }
}

public class InvalidPaymentCodeOrAmountException : ClientErrorException
{
    public InvalidPaymentCodeOrAmountException() : base("Invalid payment code or amount", ExceptionErroCodes.EW134, HttpStatusCode.BadRequest) { }
}

public class ProductNotFoundException : ClientErrorException
{
    public ProductNotFoundException(string account) : base($"Product not found [{account}]", ExceptionErroCodes.EW135, HttpStatusCode.NotFound) { }
}

public static class ExceptionErroCodes
{
    public const string EW111 = "EW-111";
    public const string EW107 = "EW-107";
    public const string EW108 = "EW-108";
    public const string EW106 = "EW-106";
    public const string EW105 = "EW-105";
    public const string EW104 = "EW-104";
    public const string EW102 = "EW-102";
    public const string EW301 = "EW-301";
    public const string EW201 = "EW-201";
    public const string EW005 = "EW-005";
    public const string EW006 = "EW-006";
    public const string EW007 = "EW-007";
    public const string EW003 = "EW-003";
    public const string EW008 = "EW-008";
    public const string EW009 = "EW-009";
    public const string EW010 = "EW-010";
    public const string EW011 = "EW-011";
    public const string EW101 = "EW-101";
    public const string EW004 = "EW-004";
    public const string EW002 = "EW-002";
    public const string EW001 = "EW-001";
    public const string EW000 = "EW-000";
    public const string EW100 = "EW-100";
    public const string EW115 = "EW-115";
    public const string EW116 = "EW-116";
    public const string EW117 = "EW-117";
    public const string EW118 = "EW-118";
    public const string EW119 = "EW-119";
    public const string EW120 = "EW-120";
    public const string EW121 = "EW-121";
    public const string EW122 = "EW-122";
    public const string EW123 = "EW-123";
    public const string EW124 = "EW-124";
    public const string EW125 = "EW-125";
    public const string EW126 = "EW-126";
    public const string EW127 = "EW-127";
    public const string EW128 = "EW-128";
    public const string EW129 = "EW-129";
    public const string EW130 = "EW-130";
    public const string EW131 = "EW-131";
    public const string EW132 = "EW-132";
    public const string EW133 = "EW-133";
    public const string EW134 = "EW-134";
    public const string EW135 = "EW-135";
    /// <summary>
    /// Account is not available to User
    /// </summary>
    public const string EW136 = "EW-136";
    /// <summary>
    /// All transactions should have the same source account
    /// </summary>
    public const string EW137 = "EW-137";
    /// <summary>
    /// Batch must contain at least one item
    /// </summary>
    public const string EW138 = "EW-138";
    /// <summary>
    /// Source account is missing or not set
    /// </summary>
    public const string EW139 = "EW-139";
}
