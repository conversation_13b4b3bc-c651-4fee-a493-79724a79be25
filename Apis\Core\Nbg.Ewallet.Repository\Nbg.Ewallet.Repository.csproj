﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>true</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <Configurations>Debug;Release;Sandbox</Configurations>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="FastMember.NetCore" Version="1.1.0" />
        <PackageReference Include="Nbg.NetCore.Common.Types" Version="8.0.1" />
        <PackageReference Include="nbg.netcore.consent.implementation" Version="1.0.7" />
        <PackageReference Include="nbg.netcore.consent.repository.types" Version="1.0.2" />
        <PackageReference Include="nbg.netcore.consent.types" Version="1.0.2" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector" Version="1.0.53" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails" Version="1.0.3" />
        <PackageReference Include="Nbg.NetCore.CosmosConnector.Types.OpenSbAccount" Version="1.0.3" />
        <PackageReference Include="Nbg.NetCore.HttpExceptions" Version="8.0.0" />
        <PackageReference Include="Nbg.NetCore.Data" Version="8.0.3" />
        <PackageReference Include="Nbg.Services.Interfaces.CICS.NetCore" Version="1.0.1" />
        <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
        <PackageReference Include="System.Runtime.Handles" Version="4.3.0" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Nbg.Ewallet.Repository.Interfaces\Nbg.Ewallet.Repository.Interfaces.csproj" />
        <ProjectReference Include="..\Nbg.Ewallet.Repository.Types\Nbg.Ewallet.Repository.Types.csproj" />
    </ItemGroup>

</Project>