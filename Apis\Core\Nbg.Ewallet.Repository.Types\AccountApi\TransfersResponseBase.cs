﻿using System;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

/// <summary>
/// TransfersResponseBase definition
/// </summary>
public class TransfersResponseBase
{
    /// <summary>Τύπος συναλλαγής ROT / RNB. Χρησιμοποιείται κατά την κλήση της remittancequery
    /// If equals to "RNB" then the remittance is to NBG.If equals to "ROT" then the remittance is to other bank</summary>
    [DataMember(Name = "remType")]
    public string RemType { get; set; }

    /// <summary>The NBG account number (e.g. 11 digits) to debit (transfer from)</summary>
    [DataMember(Name = "debitAccount")]
    public string DebitAccount { get; set; }

    /// <summary>Currency, 3-letter code, e.g. <code>EUR</code>.</summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>Transfered amount.</summary>
    [DataMember(Name = "transferAmount")]
    public decimal TransferAmount { get; set; }

    /// <summary>Available balance</summary>
    [DataMember(Name = "availableBalance")]
    public decimal AvailableBalance { get; set; }

    //<summary>Holder Name
    [DataMember(Name = "holderName")]
    public string HolderName { get; set; }

    /// <summary>Ledger balance</summary>
    [DataMember(Name = "ledgerBalance")]
    public decimal LedgerBalance { get; set; }

    /// <summary>Reference number in *********** form</summary>
    [DataMember(Name = "referenceNumber")]
    public string ReferenceNumber { get; set; }

    ///// <summary>If equals to "RNB" then the remittance is to NBG.If equals to "ROT" then the remittance is to other bank</summary>
    //[DataMember(Name = "externalSystem")]
    //public string ExternalSystem { get; set; }

    /// <summary>Full bank title </summary>
    [DataMember(Name = "bankTitle")]
    public string BankTitle { get; set; }

    /// <summary>Νame of the debit's account beneficiary</summary>
    [DataMember(Name = "debtorName")]
    public string DebtorName { get; set; }

    /// <summary>Reference number in IFTI***********1 form
    /// Επιστρέφεται μόνο κατά την εκτέλεση εμβάσματος (εκτός Εθνικής)</summary>
    [DataMember(Name = "refNo")]
    public string RefNo { get; set; }

    /// <summary>Date when transaction amount debited or credited</summary>
    [DataMember(Name = "valeur")]
    public DateTime? Valeur { get; set; }

    [DataMember(Name = "exchangeRateOut")]
    public decimal ExchangeRateOut { get; set; }

    /// <summary>Transaction amount</summary>
    [DataMember(Name = "netAmountOut")]
    public decimal NetAmountOut { get; set; }

    /// <summary>Total comission amount</summary>
    [DataMember(Name = "sumComissionOut")]
    public decimal SumComissionOut { get; set; }

    /// <summary>Sum of transaction and total commision amounts</summary>
    [DataMember(Name = "debitAmountOut")]
    public decimal DebitAmountOut { get; set; }

    [DataMember(Name = "eteComissionOut")]
    public decimal EteComissionOut { get; set; }

    [DataMember(Name = "deptExpencesOut")]
    public decimal DeptExpencesOut { get; set; }

    [DataMember(Name = "nonStpExpencesOut")]
    public decimal NonStpExpencesOut { get; set; }

    [DataMember(Name = "urgentExpencesOut")]
    public decimal UrgentExpencesOut { get; set; }

    [DataMember(Name = "onlineExpensesOut")]
    public decimal OnlineExpensesOut { get; set; }

    [DataMember(Name = "exchangeProfitOut")]
    public decimal ExchangeProfitOut { get; set; }

    /// <summary>Credit account beneficiaries (as of now - 2014/03/24 - applies only to transfers inside NBG)</summary>
    [DataMember(Name = "beneficiaries")]
    public string[] Beneficiaries { get; set; }

    /// <summary>If <code>true</code> this transaction is deferred for approval.</summary>
    [DataMember(Name = "requiresApproval")]
    public bool RequiresApproval { get; set; }

    /// <summary>The confirmation code of the tanNumber </summary>
    [DataMember(Name = "tanCheck")]
    public string TanCheck { get; set; }

    /// <summary>The exact timestamp of this transaction, as the server perceived it.</summary>
    [DataMember(Name = "transactionDate")]
    public DateTime? TransactionDate { get; set; }

    /// <summary>Debit's account IBAN</summary>
    [DataMember(Name = "debtorIBAN")]
    public string DebtorIBAN { get; set; }

    /// <summary> True if transaction is duplicate </summary>
    [DataMember(Name = "isDuplicate")]
    public bool? IsDuplicate { get; set; }
}
