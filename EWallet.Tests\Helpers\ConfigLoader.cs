﻿using BoDi;
using Microsoft.Extensions.Configuration;
using TechTalk.SpecFlow;

namespace SpecFlowConfiguration;

public interface ISpecFlownfigurationService
{
    public EnvironmentSettings GetConfiguration();
}

public class SpecFlownfigurationService : ISpecFlownfigurationService
{
    private readonly IConfiguration _configuration;
    public SpecFlownfigurationService(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public EnvironmentSettings GetConfiguration()
    {
        var configuration = new EnvironmentSettings();

        var root = _configuration.GetSection("Environment");
        var section = _configuration.GetSection("Environments");

        _configuration.Bind(configuration);
        return configuration;
    }
}

[Binding]
public class SpecFlowConfiguration
{
    [BeforeFeature]
    public static void RegisterServices(IObjectContainer objectContainer)
    {
        var configuration = new ConfigurationBuilder()
        .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
        .AddJsonFile("appsettings.Development.json", optional: false, reloadOnChange: true)
        .Build();

        var specFlowConfigService = new SpecFlownfigurationService(configuration);
        objectContainer.RegisterInstanceAs<ISpecFlownfigurationService>(specFlowConfigService);
    }
}
