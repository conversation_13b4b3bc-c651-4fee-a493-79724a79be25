using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxTransactionRepositoryService : ITransactionRepositoryService
{
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;

    public SandboxTransactionRepositoryService(ISandBoxRepositoryService sandBoxRepositoryService)
    {
        _sandBoxRepositoryService = sandBoxRepositoryService;
    }

    public Task<RepoTransaction> FindOneByTransactionIdAsync(string transactionId)
    {
        throw new NotImplementedException();
    }

    public async Task SaveAllAsync(List<RepoTransaction> transactions)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        var sbxTransactions = model.Transactions;
        sbxTransactions.AddRange(transactions.Select(x => x.MapToSandboxTransaction()));
        await _sandBoxRepositoryService.UpdateSandboxData(model);
    }

    public async Task<List<RepoTransaction>> FindAllExecutedByWalletIdAndTransactionSubtypeAndDateFromAndDateToAsync(
        string walletId,
        TransactionSubType transactionSubType,
        DateTime dateFrom,
        DateTime dateTo)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        var transactions = model.Transactions
            .Where(x => x.WalletId == Guid.Parse(walletId))
            .Where(x => x.TransactionSubType == transactionSubType)
            .Where(x => x.Status == TransactionStatus.EXECUTED)
            .Where(x => x.TransactionDate != null)
            .Where(x => x.TransactionDate >= dateFrom)
            .Where(x => x.TransactionDate < dateTo)
            .Where(x => x.TransactionDate.Value.Month == DateTime.UtcNow.Month)
            .Where(x => x.TransactionDate.Value.Year == DateTime.UtcNow.Year);

        return transactions.Select(x => x.MapToRepoTransaction()).ToList();
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAsync(string walletId)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        var transactions = model.Transactions.Where(x => x.WalletId == Guid.Parse(walletId));
        return transactions.Select(t => t.MapToRepoTransaction()).ToList();
    }

    public async Task<RepoTransaction> FindOneByWalletIdAndTransactionIdAsync(string walletId, string transactionId)
    {
        return (await FindAllByWalletIdAsync(walletId)).FirstOrDefault(t => t.TransactionId == Guid.Parse(transactionId));
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndBatchIdAndTransactionStatusAsync(
        string walletId,
        Guid? batchId,
        TransactionStatus? status,
        int pageNumber = 0,
        int pageSize = 100)
    {
        IEnumerable<RepoTransaction> transactions = await FindAllByWalletIdAsync(walletId);

        var skip = pageNumber * pageSize;

        if (!string.IsNullOrWhiteSpace(batchId.ToString()))
        {
            transactions = transactions.Where(t => t.BatchId == batchId);
        }

        if (status.HasValue)
        {
            transactions = transactions.Where(t => t.Status == status.Value);
        }

        return transactions.Skip(skip).Take(pageSize).ToList();
    }

    public async Task<bool> SaveAsync(RepoTransaction transaction)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        var transactionToUpdate = model.Transactions.FirstOrDefault(t => t.TransactionId == transaction.TransactionId);

        if (transactionToUpdate != null)
        {
            // Update properties directly using the MapToTransaction results
            var mappedTransaction = transaction.MapToSandboxTransaction();

            // Assume Transaction has these properties to update. Adjust according to your actual model.
            transactionToUpdate.TransactionId = mappedTransaction.TransactionId;
            transactionToUpdate.WalletId = mappedTransaction.WalletId;
            transactionToUpdate.UserId = mappedTransaction.UserId;
            transactionToUpdate.Amount = mappedTransaction.Amount;
            transactionToUpdate.DebitIban = mappedTransaction.DebitIban;
            transactionToUpdate.DebitName = mappedTransaction.DebitName;
            transactionToUpdate.CreditIban = mappedTransaction.CreditIban;
            transactionToUpdate.CreditName = mappedTransaction.CreditName;
            transactionToUpdate.TransactionDate = mappedTransaction.TransactionDate;
            transactionToUpdate.Timestamp = mappedTransaction.Timestamp;
            transactionToUpdate.TransactionSubType = mappedTransaction.TransactionSubType;
            transactionToUpdate.Currency = mappedTransaction.Currency;
            transactionToUpdate.Reference = mappedTransaction.Reference;
            transactionToUpdate.Result = mappedTransaction.Result;
            transactionToUpdate.ResultReason = mappedTransaction.ResultReason;
            transactionToUpdate.ReasonInfo = mappedTransaction.ReasonInfo;
            transactionToUpdate.RelatedName = mappedTransaction.RelatedName;
            transactionToUpdate.RelatedAccount = mappedTransaction.RelatedAccount;
            transactionToUpdate.Status = mappedTransaction.Status;
            transactionToUpdate.Description = mappedTransaction.Description;
            transactionToUpdate.BatchId = mappedTransaction.BatchId;
            transactionToUpdate.BatchDescription = mappedTransaction.BatchDescription;
            transactionToUpdate.Valeur = mappedTransaction.Valeur;
            transactionToUpdate.ExecutedBy = mappedTransaction.ExecutedBy;
            transactionToUpdate.ApprovedBy = mappedTransaction.ApprovedBy;
            transactionToUpdate.RejectedBy = mappedTransaction.RejectedBy;
            transactionToUpdate.Commission = mappedTransaction.Commission;
        }

        await _sandBoxRepositoryService.UpdateSandboxData(model);
        return true;
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndTransactionSubTypeAndSubmittedByAsync(
        string walletId,
        IEnumerable<TransactionSubType> transactionSubTypes,
        string userId)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        _ = Guid.TryParse(walletId, out var walletID);
        var transactions = model.Transactions
            .Where(x => x.WalletId == walletID)
            .Where(x => transactionSubTypes.Contains(x.TransactionSubType.GetValueOrDefault()))
            .Where(x => x.SubmittedBy == userId);

        return transactions.Select(x => x.MapToRepoTransaction()).ToList();
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndTransactionSubTypeAndExecutedByAsync(string walletId, IEnumerable<TransactionSubType> transactionSubTypes, string userId)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        _ = Guid.TryParse(walletId, out var walletID);
        var transactions = model.Transactions
            .Where(x => x.WalletId == walletID)
            .Where(x => transactionSubTypes.Contains(x.TransactionSubType.GetValueOrDefault()))
            .Where(x => x.ExecutedBy == userId || x.ApprovedBy == userId);

        return transactions.Select(x => x.MapToRepoTransaction()).ToList();
    }

    public async Task<List<RepoTransaction>> FindAllByWalletIdAndSubmittedByOrExecutedByOrApprovedByAsync(string walletId, string userId)
    {
        var model = await _sandBoxRepositoryService.GetSandBoxModel();
        _ = Guid.TryParse(walletId, out var walletID);
        var transactions = model.Transactions
            .Where(x => x.WalletId == walletID)
            .Where(x => x.UserId == userId || x.ExecutedBy == userId || x.ApprovedBy == userId || x.SubmittedBy == userId || x.RejectedBy == userId);

        return transactions.Select(x => x.MapToRepoTransaction()).ToList();
    }
}
