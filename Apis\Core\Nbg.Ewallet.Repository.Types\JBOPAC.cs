﻿using System;
using System.Text.Json.Serialization;

namespace nbg.ewallet.repository.types;

/// <summary>
/// Request object for opening an account using the JBOPAC CORE API
/// encapsulates everything leaving only the constructor with parameters visible
/// </summary>
public class JBOPACRequest
{
    //public Payload(string CustomerId)
    public JBOPACRequest(string CustomerId, string userId, string productCode)
    {
        customerId = CustomerId;
        this.productCode = productCode;
        auditData = new AuditData(userId);
    }
    [JsonInclude]
    private string action { get; } = "";
    [JsonInclude]
    private string channel { get; } = "06";
    [JsonInclude]
    private string operationId { get; } = "49006";
    [JsonInclude]
    private string system { get; } = "E";
    [JsonInclude]
    private string customerId { get; }
    [JsonInclude]
    private string accountBranch { get; } = "700";
    [JsonInclude]
    private string currency { get; } = "070";
    [JsonInclude]
    private string productCode { get; }
    [JsonInclude]
    protected AuditData auditData { get; set; }

    protected class AuditData
    {
        public AuditData(string Userid)
        {
            userid = Userid;
        }
        [JsonInclude]
        private string userid { get; }
        [JsonInclude]
        private string branch { get; } = "700";
        [JsonInclude]
        private string subBranch { get; } = "4";
        [JsonInclude]
        private string wsid { get; } = "7004";
        [JsonInclude]
        private string trnumb { get; } = "000";
        [JsonInclude]
        private string yphresia { get; } = "90";
        [JsonInclude]
        private string authUser { get; }
    }
}


/// <summary>
/// Response object from the JBOPAC CORE API
/// imutable object - only Account and AccountIban getters here...
/// </summary>
public class JBOPACResponse
{
    [JsonPropertyName("payload")]
    public JBOAccountInfo Payload { get; set; }

    public class JBOAccountInfo
    {
        [JsonPropertyName("account")]
        public string Account { get; set; }
        [JsonPropertyName("accountIban")]
        public string AccountIban { get; set; }

        [JsonPropertyName("additionalInfo")]
        private JBOAdditionalInfo AdditionalInfo { get; set; }
    }
    private class JBOAdditionalInfo
    {
        [JsonPropertyName("system")]
        public string System { get; set; }
        [JsonPropertyName("infoCode")]
        public string InfoCode { get; set; }
        [JsonPropertyName("infoAmount")]
        public string InfoAmount { get; set; }
        [JsonPropertyName("infoGeneral")]
        public string InfoGeneral { get; set; }
        [JsonPropertyName("infoDate")]
        public DateTime InfoDate { get; set; }
    }
}
