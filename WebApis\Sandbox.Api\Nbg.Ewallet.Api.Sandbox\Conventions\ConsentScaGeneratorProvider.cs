﻿using System;
using System.Threading.Tasks;
using Nbg.NetCore.Common.Types;
using Nbg.NetCore.Sca.Filter.Abstractions.Interfaces;
using Nbg.NetCore.Sca.Filter.Abstractions.Types;

namespace Nbg.Ewallet.Api.Sandbox.Conventions;

public class ConsentScaGeneratorProvider : IScaGeneratorProvider
{
    private static readonly Guid userRegistryId = Guid.Parse("75D739E4-E489-47DF-B229-7E14021D4E8A");
    private static readonly Guid notificationSubTypeId = Guid.Parse("12943AE3-E3D8-448C-BBFB-AA3BBD8E9488");

    public async Task<ScaRequest> GetScaRequestAsync(IRequest request)
    {
        //if (!(request is Request<UpdateConsentRequest> updateConsentRequestObject))
        //    throw new BadRequestException("Invalid request object");

        //if (!(updateConsentRequestObject.Data is UpdateConsentRequest))
        //    throw new BadRequestException("Invalid payload object");

        var scaRequest = new ScaRequest
        {
            IsScaFree = false,
            UserRegistryId = userRegistryId,
            NotificationSubTypeId = notificationSubTypeId,
            SenderName = "I-BANK"
        };

        return await Task.FromResult(scaRequest);
    }
}
