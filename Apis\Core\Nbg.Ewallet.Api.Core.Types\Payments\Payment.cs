﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types;

/// <summary>
/// Debtor Telephone
/// </summary>
public class Payment
{
    /// <summary>
    /// Payment Code
    /// </summary>
    [DataMember(Name = "paymentCode")]
    public string PaymentCode { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    [DataMember(Name = "currency")]
    public string Currency { get; set; }

    /// <summary>
    /// Amount
    /// </summary>
    [DataMember(Name = "amount")]
    public decimal Amount { get; set; }
}
