using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Repository.Interfaces;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Nbg.Ewallet.Api.Implementation;

public class SubscriptionPlansService : ISubscriptionPlansService
{
    private readonly ISubscriptionApiClientService _subscriptionApiClientService;

    public SubscriptionPlansService(ISubscriptionApiClientService subscriptionApiClientService)
    {
        _subscriptionApiClientService = subscriptionApiClientService;
    }

    public async Task<List<SubscriptionPlan>> GetAvailableSubscriptionPlans()
    {
        var repoSubscriptionPlans = await _subscriptionApiClientService.GetAvailableSubscriptionTiersAsync();

        var subscriptionPlans = repoSubscriptionPlans.AvailableTiers.Select(x => new SubscriptionPlan
        {
            Id = x.Tier,
            discounts = [.. x.SubscriptionBundles.Select(y => new DiscountTransaction
            {
                TransactionType = y.TransactionType,
                Value = y.Value,
            })]
        }).ToList();

        return subscriptionPlans;
    }
}
