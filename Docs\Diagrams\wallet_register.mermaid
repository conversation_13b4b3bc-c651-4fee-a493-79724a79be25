sequenceDiagram
	autonumber
	Client->>EWallet.Proxy: /wallet/register POST
	EWallet.Proxy->>EWallet.Core: /wallet/register POST
	EWallet.Core->>Consent.Api: GetConsentAsync(consentId)
	Consent.Api->>EWallet.Core: {consent}
	EWallet.Core->>Cics: JCRAALL
	Cics->>EWallet.Core: {vatNumber, companyName}
	EWallet.Core->>EWallet.DB: INSERT
	note over EWallet.DB: DB Table:  Wallets, UserPermissions
	EWallet.DB->>EWallet.Core: {walletId}
	EWallet.Core->>EWallet.Proxy: {walletId}
	EWallet.Proxy->>Client: {walletId}