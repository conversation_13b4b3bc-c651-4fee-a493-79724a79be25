﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Nbg.Ewallet.Repository.Types;

/// <summary>
/// SubscriptionPlanType definition
/// </summary>
[JsonConverter(typeof(StringEnumConverter))]
public enum SubscriptionTier
{
    /// <summary>
    /// Trial Subscription Tier
    /// </summary>
    Free,
    /// <summary>
    /// Basic Subscription Tier
    /// </summary>
    Basic,
    /// <summary>
    /// Premium Subscription Tier
    /// </summary>
    Premium
}


/// <summary>
/// SubscriptionPlanType definition
/// </summary>
[JsonConverter(typeof(StringEnumConverter))]
public enum SubscriptionStatus
{
    /// <summary>
    /// Trial Subscription Status
    /// </summary>
    Trial,
    /// <summary>
    /// Pending Payment Subscription Status
    /// </summary>
    PendingPayment,
    /// <summary>
    /// Active Subscription Status
    /// </summary>
    Active
}

