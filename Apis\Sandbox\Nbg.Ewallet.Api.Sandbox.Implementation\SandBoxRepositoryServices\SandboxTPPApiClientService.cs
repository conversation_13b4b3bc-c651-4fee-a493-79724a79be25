﻿using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ibank.ThirdParty.Types;
using ibank.ThirdParty.Types.Core;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;
using Nbg.Ewallet.Api.Sandbox.Types;
using Nbg.Ewallet.Api.Types.Payments;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.Common.Types;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class SandboxTPPApiClientService : ITPPApiClientService
{
    private const string DEFAULT_CREDITOR_IBAN = "**********************";
    private const string DEFAULT_BIC = "ETHNGRAA";
    private const string DEFAULT_BRANCH_ID = "700";
    private const string DEFAULT_BANK_NAME = "NATIONAL BANK OF GREECE";
    private const string PAYMENTS_JSON_FILE = "paymentsListLight.json";
    private const decimal DEFAULT_NBG_COMMISSION = 0.1m;

    private readonly ISandBoxRepositoryService _sandboxRepositoryService;

    public SandboxTPPApiClientService(ISandBoxRepositoryService sandboxRepositoryService)
    {
        _sandboxRepositoryService = sandboxRepositoryService ?? throw new ArgumentNullException(nameof(sandboxRepositoryService));
    }

    public async Task<CommissionResponse> GetCommissionAsync(PaymentCoreRequest request)
    {
        ValidatePaymentRequest(request);

        var commissionInfo = new CommissionInfo
        {
            Nbg = DEFAULT_NBG_COMMISSION,
            NbgOrg = 0.0m,
            Merchant = 0.0m,
            SubAgent = 0.0m,
            ThirdParty = 0.0m
        };

        commissionInfo.Total = CalculateTotalCommission(commissionInfo);

        return new CommissionResponse { CommissionInfo = commissionInfo };
    }

    public async Task<Response<PaymentResponse>> PayAsync(PaymentCoreRequest request)
    {
        var result = new Response<PaymentResponse>();

        try
        {
            ValidatePaymentRequest(request);

            var (_, account) = await GetWalletAndBalanceAsync(request);

            if (!HasSufficientBalance(account.AvailableBalance, request.SettlementInfo.Amount))
            {
                return CreateErrorResponse("ΑΝΕΠΑΡΚΕΣ ΥΠΟΛΟΙΠΟ");
            }

            if (!await ValidatePaymentCode(request))
            {
                return CreateErrorResponse("ΛΑΘΟΣ ΚΩΔΙΚΟΣ ΠΛΗΡΩΜΗΣ Ή ΠΟΣΟ");
            }

            var creditorName = await GetCreditorName(request.PaymentOrgIdentification.Id.ToString());
            if (string.IsNullOrEmpty(creditorName))
            {
                return CreateErrorResponse("Invalid paymentOrgIdentificationId");
            }

            // Process payment
            account.AvailableBalance -= request.SettlementInfo.Amount;
            await UpdateBalanceAsync(account);

            result.Payload = await CreatePaymentResponse(request, account, creditorName);
        }
        catch (Exception ex)
        {
            result.Exception = new ResponseMessage { Description = ex.Message };
        }

        return result;
    }

    private async Task<(SandboxWallet wallet, SandboxAvailableAccount balance)> GetWalletAndBalanceAsync(PaymentCoreRequest request)
    {
        var sandbox = await _sandboxRepositoryService.GetSandBoxModel();
        var wallet = sandbox.Wallets.FirstOrDefault(x => x.WalletAccount == request.Debtor.DebtorAccount.IBAN)
                     ?? throw new ArgumentException("Wallet not found");

        var balance = sandbox.AvailableAccounts.FirstOrDefault(x => x.IBAN == wallet.WalletAccount)
                      ?? throw new ArgumentException("Wallet balance not found");

        return (wallet, balance);
    }

    private void ValidatePaymentRequest(PaymentCoreRequest request)
    {
        if (!PaymentOrgIdExists(request.PaymentOrgIdentification.Id.ToString()))
        {
            throw new InvalidOrganizationIdException();
        }

        if (!PaymentsValidator.IsValidAmount(request.SettlementInfo.Amount))
        {
            throw new InvalidPaymentCodeOrAmountException();
        }
    }

    private async Task<bool> ValidatePaymentCode(PaymentCoreRequest request)
    {
        return request.PaymentOrgIdentification.Fields.ContainsKey("PAYMENT_CODE") &&
               PaymentsValidator.IsValidPaymentCode(request.PaymentOrgIdentification.Fields["PAYMENT_CODE"]);
    }

    private decimal CalculateTotalCommission(CommissionInfo info)
    {
        return info.Nbg + info.NbgOrg + info.Merchant + info.SubAgent + info.ThirdParty;
    }

    private bool HasSufficientBalance(decimal currentBalance, decimal requestedAmount)
    {
        return currentBalance >= requestedAmount;
    }

    private async Task UpdateBalanceAsync(SandboxAvailableAccount account)
    {
        var model = await _sandboxRepositoryService.GetSandBoxModel();
        var existingBalance = model.AvailableAccounts.FirstOrDefault(x => x.IBAN == account.IBAN);
        if (existingBalance != null)
        {
            existingBalance.AvailableBalance = account.AvailableBalance;
        }

        await _sandboxRepositoryService.UpdateSandboxData(model);
    }

    private async Task<PaymentResponse> CreatePaymentResponse(PaymentCoreRequest request, SandboxAvailableAccount account, string creditorName)
    {
        var commission = await GetCommissionAsync(request);
        var paymentId = CreatePaymentIdentification(request);

        return new PaymentResponse
        {
            PaymentIdentification = paymentId,
            SettlementInfo = request.SettlementInfo,
            CommissionInfo = commission.CommissionInfo,
            Creditor = CreateCreditor(creditorName),
            Debtor = CreateDebtor(request),
            Additional = CreateAdditional(account)
        };
    }

    private PaymentIdentification CreatePaymentIdentification(PaymentCoreRequest request)
    {
        var clrSysRef = GetClrSysRef(null, null);
        return new PaymentIdentification
        {
            ClrSysRef = clrSysRef,
            EndToEndId = GetEndToEndId(SettlementMethodEnum.CARD.ToString(), clrSysRef),
            PaymentRef = CreatePaymentRef(request)
        };
    }

    private string CreatePaymentRef(PaymentCoreRequest request)
    {
        var fields = request.PaymentOrgIdentification.Fields;
        return string.Concat(
            GetFieldValue(fields, "DEBTOR_TAX_ID"),
            GetFieldValue(fields, "PAYMENT_CODE"),
            GetFieldValue(fields, "AUX_PAYMENT_CODE")
        );
    }

    private string GetFieldValue(System.Collections.Generic.Dictionary<string, string> fields, string key)
    {
        return fields.ContainsKey(key) && !string.IsNullOrEmpty(fields[key]) ? fields[key] : "";
    }

    private Creditor CreateCreditor(string creditorName)
    {
        return new Creditor
        {
            Name = creditorName,
            CreditorAccount = new CreditorAccount { IBAN = DEFAULT_CREDITOR_IBAN, PAN = null },
            CreditorBank = new CreditorBank { BIC = DEFAULT_BIC, BranchId = DEFAULT_BRANCH_ID, Name = DEFAULT_BANK_NAME }
        };
    }

    private Debtor CreateDebtor(PaymentCoreRequest request)
    {
        return new Debtor
        {
            Name = request.Debtor.Name,
            DebtorAccount = new DebtorAccount { IBAN = request.Debtor.DebtorAccount.IBAN, PAN = null },
            Telephone = request.Debtor.Telephone,
            ExtraIdentities = null
        };
    }

    private Additional CreateAdditional(SandboxAvailableAccount balance)
    {
        return new Additional
        {
            LedgerBalance = balance.LedgerBalance,
            AvailableBalance = balance.AvailableBalance,
            SendCutOffTime = GenerateSendCutOffTime(DateTime.Now).ToString()
        };
    }

    private Response<PaymentResponse> CreateErrorResponse(string message)
    {
        return new Response<PaymentResponse> { Exception = new ResponseMessage { Description = message } };
    }

    public static string GetClrSysRef(string legacyCode, string branchCode)
    {
        var sb = new StringBuilder();

        if (!string.IsNullOrWhiteSpace(legacyCode)) sb.Append(legacyCode[..5].PadRight(5, '0'));
        if (!string.IsNullOrWhiteSpace(branchCode)) sb.Append(branchCode.PadRight(3, '0'));

        sb.Append(DateTime.Now.ToString("MMddyyyy"));

        if (sb.Length < 20) sb.Append(SandBoxRandomDataHelper.GenerateRandomNumber(20 - sb.Length));

        return sb.ToString()[..20];
    }

    public static string GetEndToEndId(string type, string clearSysRef)
    {
        return type.Equals(SettlementMethodEnum.CASH.ToString(), StringComparison.InvariantCultureIgnoreCase)
            ? clearSysRef
            : SandBoxRandomDataHelper.GenerateRandomNumber(12);
    }

    public static DateTime GenerateSendCutOffTime(DateTime transactionTimestamp)
    {
        var now = DateTime.Now;
        var morningCutOff = new DateTime(now.Year, now.Month, now.Day, 8, 0, 0);
        var noonCutOff = new DateTime(now.Year, now.Month, now.Day, 14, 0, 0);
        var afternoonCutOff = new DateTime(now.Year, now.Month, now.Day, 18, 0, 0);

        if (transactionTimestamp.TimeOfDay > afternoonCutOff.TimeOfDay) return morningCutOff.AddDays(1);
        if (transactionTimestamp.TimeOfDay > noonCutOff.TimeOfDay) return afternoonCutOff;
        return transactionTimestamp.TimeOfDay > morningCutOff.TimeOfDay ? noonCutOff : DateTime.Now;
    }

    private async Task<string> GetCreditorName(string paymentOrgId)
    {
        try
        {
            var json = await File.ReadAllTextAsync(PAYMENTS_JSON_FILE, Encoding.UTF8);
            var jsonObject = JsonSerializer.Deserialize<SandboxJsonPayload>(json);

            return jsonObject?.Payload?.Payments
                ?.FirstOrDefault(p => p.Id == paymentOrgId)
                ?.Name;
        }
        catch (Exception)
        {
            return null;
        }
    }

    private bool PaymentOrgIdExists(string paymentOrgId)
    {
        string jsonFilePath = "paymentsListLight.json";

        string json = File.ReadAllText(jsonFilePath, Encoding.UTF8);
        var jsonObject = JsonSerializer.Deserialize<SandboxJsonPayload>(json);

        return jsonObject.Payload.Payments.Any(p => p.Id == paymentOrgId);
    }
}
