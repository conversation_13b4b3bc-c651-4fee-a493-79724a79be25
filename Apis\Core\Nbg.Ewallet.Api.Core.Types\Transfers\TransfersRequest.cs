﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Types.Transfers;

/// <summary>
/// TransferRequest definition
/// Add any wallet specific params here
/// </summary>
public class TransfersRequest : TransfersRequestBase
{
    /// <summary>
    /// TransactionId provided by client
    /// </summary>
    [Required]
    [DataMember(Name = "transactionId")]
    public Guid TransactionId { get; set; }
}
