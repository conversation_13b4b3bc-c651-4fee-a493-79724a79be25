﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace Nbg.Ewallet.Repository.Types.AccountApi;

[DataContract]
public class StatementRequestQueryParams
{
    /// <summary>From date to fetch statements (κινήσεις)</summary>
    [DataMember(Name = "dateFrom")]
    [Required]
    public DateTime DateFrom { get; set; }

    /// <summary>To date to fetch statements (κινήσεις)</summary>
    [DataMember(Name = "dateTo")]
    [Required]
    public DateTime DateTo { get; set; }

    [DataMember(Name = "userId")]
    public string UserId { get; set; }

    [DataMember(Name = "account")]
    public string Account { get; set; }

    [DataMember(Name = "currency")]
    public string Currency { get; set; }

}
