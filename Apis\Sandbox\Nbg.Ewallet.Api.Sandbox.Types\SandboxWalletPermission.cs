﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Sandbox.Types;

/// <summary>
/// Represents the permissions granted to a wallet for operations by another wallet.
/// </summary>
[DataContract]
public class SandboxWalletPermission
{
    /// <summary>
    /// The unique identifier for the permission.
    /// </summary>
    [DataMember(Name = "id")]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// The identifier of the wallet to which these permissions apply.
    /// </summary>
    [DataMember(Name = "walletId")]
    public Guid WalletId { get; set; }

    /// <summary>
    /// The identifier of the target wallet granted these permissions.
    /// </summary>
    [DataMember(Name = "targetwalletid")]
    public Guid TargetWalletId { get; set; }

    /// <summary>
    /// Indicates if the permission to submit is granted.
    /// </summary>
    [DataMember(Name = "submit")]
    public bool Submit { get; set; }

    /// <summary>
    /// Type of approval granted.
    /// </summary>
    [DataMember(Name = "approve")]
    public bool Approve { get; set; }

    /// <summary>
    /// Indicates if the permission to view balances is granted.
    /// </summary>
    [DataMember(Name = "balanceView")]
    public bool BalanceView { get; set; }

    /// <summary>
    /// Indicates if the permission to view transactions is granted.
    /// </summary>
    [DataMember(Name = "transactionView")]
    public bool TransactionView { get; set; }

    /// <summary>
    /// A list of transaction limits associated with this permission.
    /// </summary>
    [DataMember(Name = "limits")]
    public List<SandboxLimit> Limits { get; set; }

    /// <summary>
    /// The date when the permissions were created.
    /// </summary>
    [DataMember(Name = "creationDate")]
    public DateTime CreationDate { get; set; }

    /// <summary>
    /// The date when the permissions expire.
    /// </summary>
    [DataMember(Name = "expirationDate")]
    public DateTime ExpirationDate { get; set; }
}
