﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Nbg.Ewallet.Repository.UnitOfWork;

public static class ExceptionHandlingHelper
{
    public static async Task ExecuteAsync(Func<Task> func, ILogger logger, string errorMessage)
    {
        ArgumentNullException.ThrowIfNull(func);
        try
        {
            await func();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, errorMessage);
            throw new Exception(errorMessage, ex);
        }
    }

    public static async Task<T> ExecuteAsync<T>(Func<Task<T>> func, ILogger logger, string errorMessage)
    {
        ArgumentNullException.ThrowIfNull(func);
        try
        {
            return await func();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, errorMessage);
            throw new Exception(errorMessage, ex);
        }
    }
}
