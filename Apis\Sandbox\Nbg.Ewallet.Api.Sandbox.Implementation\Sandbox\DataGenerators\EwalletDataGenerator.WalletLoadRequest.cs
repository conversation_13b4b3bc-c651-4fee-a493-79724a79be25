﻿//using System.Collections.Generic;
//using System.Linq;
//using Nbg.Ewallet.Api.Sandbox.Types;

//namespace Nbg.Ewallet.Api.Sandbox.Implementation.Sandbox;

//public partial class EwalletDataGenerator
//{
//    private static List<SandboxWalletBalance> GenerateWalletLoadRequests(List<SandboxWallet> wallets)
//    {
//        return [.. wallets.Select(w =>
//        {
//            return new SandboxWalletBalance
//            {
//                Amount = 10000M,
//                Currency = "EUR",
//                Iban = w.WalletAccount,
//                WalletId = w.WalletId
//            };
//        })];
//    }
//}
