﻿using System.Runtime.Serialization;

namespace Nbg.Ewallet.Api.Types.Consents;

public class AuthorizeConsent
{
    /// <summary>
    /// The Id of the consent
    /// </summary>
    [DataMember(Name = "consentId")]
    public string ConsentId { get; set; }

    /// <summary>
    /// the Id of the User
    /// </summary>
    [DataMember(Name = "userId")]
    public string UserId { get; set; }

    /// <summary>
    /// The Iban specified by the user
    /// </summary>
    [DataMember(Name = "iban")]
    public string Iban { get; set; }

    /// <summary>
    /// The sms OTP number the user has keyed in
    /// </summary>
    [DataMember(Name = "tanNumber")]
    public string TanN<PERSON>ber { get; set; }

    public bool? IsSmsOtp { get; set; } = true;

    public string GetCategory() => "nbg.ewallet.consents.updateconsent";
}
