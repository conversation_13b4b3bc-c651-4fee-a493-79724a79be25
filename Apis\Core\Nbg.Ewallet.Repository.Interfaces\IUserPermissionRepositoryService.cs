﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface IUserPermissionsRepositoryService
{
    Task<RepoUserPermission?> FindOneByActiveAndUserIdAsync(string UserId);

    Task SaveAllAsync(List<RepoUserPermission> permission);

    Task<List<RepoUserPermission>> FindAllByActiveAndUserIdAndWalletIdAsync(string userid, Guid walletid);

    Task<RepoUserPermission> SaveAsync(RepoUserPermission userPermission);

    Task<List<RepoUserPermission>> FindAllByActiveAndWalletIdAndUserId(Guid walletId, string userId);

    Task<List<RepoUserPermission>> FindAllByExpiredAndWalletIdAndUserIdAsync(Guid walletId, string userId);

    Task ExpireExistingPermissions(Guid myWalletId, IEnumerable<RepoUserPermission> permissions);
}
