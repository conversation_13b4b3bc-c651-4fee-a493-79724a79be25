﻿using Nbg.Ewallet.Repository.Types.AccountApi;

namespace Nbg.Ewallet.Api.Implementation.Extensions;
public static class BatchCalculateFeesResponseExtensions
{
    public static BatchCalculateFeesResponse Add(this BatchCalculateFeesResponse @this, BatchCalculateFeesResponse batch)
    {
        if (@this == null) return batch;
        if (batch == null) return @this;

        @this.SumCommissionCurrency = SumNullableDecimals(@this.SumCommissionCurrency, batch.SumCommissionCurrency);
        @this.CreditAmountCurrency += batch.CreditAmountCurrency;
        @this.NetAmount += batch.NetAmount;
        @this.DebitNetAmount += batch.DebitNetAmount;
        @this.SumCommission += batch.SumCommission;
        @this.DebitAmount += batch.DebitAmount;
        @this.EteCommission += batch.EteCommission;
        @this.DeptExpenses += batch.DeptExpenses;
        @this.NonStpExpenses += batch.NonStpExpenses;
        @this.UrgentExpenses += batch.UrgentExpenses;
        @this.OnlineExpenses += batch.OnlineExpenses;
        @this.ExchangeProfit += batch.ExchangeProfit;

        return @this;
    }

    private static decimal? SumNullableDecimals(decimal? a, decimal? b)
    {
        if (a == null && b == null) return null;
        return (a ?? 0) + (b ?? 0);
    }
}
