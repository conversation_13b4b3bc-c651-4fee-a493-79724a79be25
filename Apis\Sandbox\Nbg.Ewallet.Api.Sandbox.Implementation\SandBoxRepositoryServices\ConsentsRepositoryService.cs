﻿using System.Threading.Tasks;
using AutoMapper;
using nbg.netcore.consent.repository.types;
using Nbg.Ewallet.Api.Sandbox.Implementation.Interfaces;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.OpenBanking.ConsentConnector;
using Nbg.OpenBanking.ConsentTypes;

namespace Nbg.Ewallet.Api.Sandbox.Implementation.SandBoxRepositoryServices;

public class ConsentsRepositoryService : IConsentsRepositoryService
{
    private readonly IMapper _mapper;
    private readonly ISandBoxRepositoryService _sandBoxRepositoryService;
    private readonly IConsentApiService _consentApiService;

    public ConsentsRepositoryService(IMapper mapper, ISandBoxRepositoryService sandboxRepositoryService, IConsentApiService consentApiService)
    {
        _mapper = mapper;
        _sandBoxRepositoryService = sandboxRepositoryService;
        _consentApiService = consentApiService;
    }

    public async Task<RepoConsentFull> CreateConsentAsync(RepoConsent consent)
    {
        var generatingConsent = _mapper.Map<Consent>(consent);
        var generatedConsent = await _consentApiService.CreateConsentAsync(generatingConsent);
        return _mapper.Map<RepoConsentFull>(generatedConsent);
    }

    public async Task<RepoConsentFull> GetConsentAsync(string consentId)
    {
        var retrievedConsent = await _consentApiService.GetConsentAsync<Consent>(consentId);
        return _mapper.Map<RepoConsentFull>(retrievedConsent);
    }

    public async Task<RepoConsentFull> UpdateConsentAsync(string consentId, RepoConsent consent)
    {
        var updatingConsent = _mapper.Map<Consent>(consent);
        var updatedConsent = await _consentApiService.UpdateConsentDataAsync(consentId, updatingConsent);
        return _mapper.Map<RepoConsentFull>(updatedConsent);
    }
}
