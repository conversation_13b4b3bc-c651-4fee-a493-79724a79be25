using System.Net.Http.Json;
using System.Text.Json;
using EWallet.Tests.Helpers;
using FluentAssertions;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Subscriptions;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using SpecFlowConfiguration;
using TechTalk.SpecFlow;

namespace EWallet.Tests.Steps;

[Binding]
public class InternalPermissionSteps
{
    private readonly HttpClient _httpClient;
    private FeatureContext _featureContext;
    private ISpecFlownfigurationService _specFlownfigurationService;

    public InternalPermissionSteps(HttpClient httpClient, FeatureContext featureContext, ISpecFlownfigurationService specFlownfigurationService)
    {
        _httpClient = httpClient;
        _featureContext = featureContext;
        _specFlownfigurationService = specFlownfigurationService;
    }

    [When(@"I give (.*) permissions with (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*)")]
    public async Task WhenIGiveUserPermissionsToUser(int internalUserId, bool admin, bool approve, bool balanceView, bool submit, bool transactionView, bool inheritsAuthorizations, string transactionType, decimal amount, string timeframe)
    {
        var setWalletUserPermissionsRequest = new SetWalletUserPermissionsRequest
        {
            Permissions = new List<UserPermission>
            {
                new UserPermission
                {
                    UserId = _specFlownfigurationService.GetConfiguration().GetUserId(internalUserId).userName,
                    BalanceView = balanceView,
                    Submit = submit,
                    TransactionView = transactionView,
                    Admin = admin,
                    Approve = approve,
                    InheritsAuthorizations = inheritsAuthorizations,
                    Limits = new List<Limit>
                    {
                        new Limit
                        {
                            Amount = amount,
                            Timeframe= TimeFrameType.YEARLY,
                            TransactionType = LimitTransactionType.WalletToWallet
                        }
                    }
                }
            },
            IsSmsOtp = true,
            TanNumber = "smsotp"
        };
        var registeredWalletId = _featureContext["LoggedUserWalletId"];
        var uri = $"wallet/{registeredWalletId}/user-permissions";
        var response = await _httpClient.PostAsJsonAsync(uri, setWalletUserPermissionsRequest);

        // just for debugging
        var reposneString = await response.Content.ReadAsStringAsync();
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var smsotp = await SmsOtpHelper.GetSmsOtp(_featureContext["LoggedUser"].ToString());
        setWalletUserPermissionsRequest.TanNumber = smsotp.Otp;
        response = await _httpClient.PostAsJsonAsync(uri, setWalletUserPermissionsRequest);

        // just for debugging
        reposneString = await response.Content.ReadAsStringAsync();
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
    }

    [Given(@"As an owner of a wallet")]
    public async Task GivenAsUserIAmAnOwnerOfAWallet()
    {
        var uri = "profile";
        var response = await _httpClient.GetAsync(uri);

        var profileResponseString = await response.Content.ReadAsStringAsync();
        var profileResponse = JsonSerializer.Deserialize<ProfileResponse>(profileResponseString);

        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        profileResponse.Should().NotBeNull();
        profileResponse.WalletId.Should().NotBeNull();
        var loggedUser = _featureContext["LoggedUser"];
        _featureContext["LoggedUserWalletId"] = profileResponse.WalletId;
    }


    [Then(@"the (.*) must have (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) permissions")]
    public async Task TheUserMustHavePermissionsOnWallet(int internalUserId, bool admin, bool approve, bool balanceView,
                                    bool submit, bool transactionView, bool inheritsAuthorizations, string transactionType, decimal amount, string timeframe)
    {

        var internaluser = _specFlownfigurationService.GetConfiguration().GetUserId(internalUserId);
        var registeredWalletId = _featureContext["LoggedUserWalletId"];

        var uri = $"wallet/{registeredWalletId}/user-permissions/{internaluser.userName}?ShowAll=false";
        var response = await _httpClient.GetAsync(uri);
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var userPermissionsResponseString = await response.Content.ReadAsStringAsync();
        var userPermissionsResponse = JsonSerializer.Deserialize<List<UserPermission>>(userPermissionsResponseString);
        userPermissionsResponse.Should().NotBeNull();
        userPermissionsResponse.Count.Should().NotBe(0);

        var userPermission = userPermissionsResponse.First();
        userPermission.Submit.Should().Be(submit);
        userPermission.Admin.Should().Be(admin);
        userPermission.Approve.Should().Be(approve);
        userPermission.BalanceView.Should().Be(balanceView);
        userPermission.TransactionView.Should().Be(transactionView);
        userPermission.InheritsAuthorizations.Should().Be(inheritsAuthorizations);
        userPermission.Limits[0].Amount.Should().Be(amount);
        userPermission.Limits[0].Timeframe.Should().Be((TimeFrameType)Enum.Parse(typeof(TimeFrameType), timeframe));
        userPermission.Limits[0].TransactionType.Should().Be((LimitTransactionType)Enum.Parse(typeof(LimitTransactionType), transactionType));
    }

    [When(@"I revoke permissions for user (.*)")]
    public async Task WhenIRevokePermissionsForUserForWallet(int userName)
    {
        var registeredWalletId = _featureContext["LoggedUserWalletId"];

        var smsotp = await SmsOtpHelper.GetSmsOtp(_featureContext["LoggedUser"].ToString());

        var userRevokePermissionsUri = $"wallet/{registeredWalletId}/user-permissions/{userName}/revoke";
        var userRevokePermissionsResponse = await _httpClient.PutAsJsonAsync(userRevokePermissionsUri, new
        {
            IsSmsOtp = true,
            //TanNumber = "11111"
            TanNumber = smsotp.Otp
        });
    }

    [Then(@"Permissions are revoked for (.*)")]
    public async Task ThenPermissionsAreRevokedForUserJerryForTheWallet(int userName)
    {

        var registeredWalletId = _featureContext["LoggedUserWalletId"];
        var userWalletPermissions = await GetUserPermissions("", false);

        foreach (var permission in userWalletPermissions)
        {
            permission.ExpirationDate.Should().BeSameDateAs(DateTime.UtcNow);
        }
    }


    [Then(@"only the active permission is retrieved for user (.*)")]
    public async Task ThenOnlyTheActivePermissionIsRetrievedForUserForWallet(int userName)
    {
        var registeredWalletId = _featureContext["LoggedUserWalletId"];

        var userPermissionsUri = $"wallet/{registeredWalletId}/user-permissions";
        var userRightsResponse = await _httpClient.GetAsync(userPermissionsUri);

        var userRightsResponseString = await userRightsResponse.Content.ReadAsStringAsync();

        var userWalletPermissions = JsonSerializer.Deserialize<UserPermissionResponse>(userRightsResponseString);

        var userPermissions = (List<UserPermissionResponse>)_featureContext["UserPermissions"];
        foreach (var perm in userPermissions)
        {
            var p = perm.Permissions.FirstOrDefault(x => x.WalletId == Guid.Parse(registeredWalletId.ToString()));
            if (p != null)
            {
                p = userWalletPermissions.Permissions.First(x => x.WalletId == Guid.Parse(registeredWalletId.ToString()));
                return;
            }
        }
    }

    [When(@"I give permissions to user (.*) and this user is me")]

    public async Task WhenIGivePermissionsToUserAndThisUserIsSelf(int userName)
    {
        var registeredWalletId = _featureContext["LoggedUserWalletId"];
        var internaluser = _specFlownfigurationService.GetConfiguration().GetUserId(userName);

        var smsopt = await SmsOtpHelper.GetSmsOtp(_featureContext["LoggedUser"].ToString());
        var setWalletUserPermissionsRequest = new SetWalletUserPermissionsRequest
        {
            Permissions = new List<UserPermission>
            {
                new UserPermission
                {

                    UserId = internaluser.userName,
                    BalanceView = true,
                    Submit = true,
                    TransactionView = true,
                    Admin = false,
                    Approve = true,
                    InheritsAuthorizations = false,
                    Limits = new List<Limit>
                    {
                        new Limit
                        {
                            Amount = 50,
                            Timeframe= TimeFrameType.YEARLY,
                            TransactionType = LimitTransactionType.WalletToWallet
                        }
                    }
                }
            }
            ,
            IsSmsOtp = true,
            //TanNumber = "11111"
            TanNumber = smsopt.Otp

        };

        var uri = $"wallet/{registeredWalletId}/user-permissions";
        var response = await _httpClient.PostAsJsonAsync(uri, setWalletUserPermissionsRequest);
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)401);
    }

    [Then(@"no permission is applied to user (.*)")]
    public async Task ThenNoPermissionAreAppliedToUserForWallet(int userName)
    {
        var registeredWalletId = _featureContext["LoggedUserWalletId"];

        var userPermissionsUri = $"wallet/{registeredWalletId}/user-permissions/{userName}";
        var userRightsResponse = await _httpClient.GetAsync(userPermissionsUri);

        var userRightsResponseString = await userRightsResponse.Content.ReadAsStringAsync();

        var userWalletPermissions = JsonSerializer.Deserialize<List<UserPermission>>(userRightsResponseString);

        userWalletPermissions.First().Should().BeNull();
    }

    //[When(@"I have permissions as user (.*)")]
    //public async Task WhenIHavePermissionsAsUserForWalle(int userName)
    //{
    //    // see this one again

    //    //var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
    //    //var existingWallet = walletsList.FirstOrDefault(x => x.WalletName == walletName);

    //    //var userPermissions = await GetUserPermissions(walletName, userName);
    //    //var permission = userPermissions.FirstOrDefault(x => x.UserId == userName
    //    //                                && x.WalletId == existingWallet.WalletId);
    //    //permission.Should().NotBeNull();
    //    return;
    //}

    [Then(@"no permission is revoked to user (.*)")]
    public async Task ThenNoPermissionIsRevokedToUserForWallet(int userName)
    {
        var permission = await GetUserPermissions("");
        permission.Should().NotBeNull();
        permission.Count.Should().BeGreaterThan(0);
    }

    [Then(@"only the active permissions are retrieved for (.*) users")]
    public async Task ThenOnlyTheActivePermissionsAreRetrievedForWalletUsers(string walletName)
    {
        var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
        var existingWallet = walletsList.FirstOrDefault(x => x.WalletName == walletName);

        var userPermissions = await GetUserPermissions(null, false);
        foreach (var p in userPermissions)
        {
            p.ExpirationDate.Should().BeOnOrAfter(DateTime.UtcNow);
        }
    }

    [Then(@"all the active permissions are retrieved")]
    public async Task ThenAllTheActivePermissionsAreRetrievedForWalletUsers()
    {
        var userPermissions = await GetUserPermissions(null, true);
        foreach (var p in userPermissions)
        {
            p.ExpirationDate.Should().BeOnOrAfter(DateTime.UtcNow);
        }
    }

    private async Task<List<UserPermission>> GetUserPermissions(string? userName = null, bool showAll = false)
    {
        var registeredWalletId = _featureContext["LoggedUserWalletId"];

        var userPermissionsUri = $"wallet/{registeredWalletId}/user-permissions";
        if (!String.IsNullOrEmpty(userName))
        {
            userPermissionsUri = $"{userPermissionsUri}/{userName}";
        }
        if (showAll)
        {
            userPermissionsUri = userPermissionsUri + "?showAll=true";
        }

        var userRightsResponse = await _httpClient.GetAsync(userPermissionsUri);
        userRightsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var userRightsResponseString = await userRightsResponse.Content.ReadAsStringAsync();
        List<UserPermission>? userWalletPermissions = null;
        if (!String.IsNullOrEmpty(userName))
        {
            userWalletPermissions = JsonSerializer.Deserialize<List<UserPermission>>(userRightsResponseString);
            return userWalletPermissions;
        }
        UserPermissionResponse UserPermissionResponse = JsonSerializer.Deserialize<UserPermissionResponse>(userRightsResponseString);
        return UserPermissionResponse.Permissions;
    }

    private async Task GivenAndIHaveRegisteredAWalletWirhName(string walletName)
    {
        var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
        var existingWallet = walletsList.FirstOrDefault(x => x.WalletName == walletName);
        if (existingWallet != null)
        {
            return;
        }

        var registerRequest = new WalletRegister { WalletName = walletName };
        var response = await _httpClient.PostAsJsonAsync("wallet/register", registerRequest);
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var walletRegistrationResponse = await response.Content.ReadAsStringAsync();
        var newWallet = JsonSerializer.Deserialize<Wallet>(walletRegistrationResponse);

        var createSubscriptionRequest = new CreateSubscriptionRequest
        {
            Tier = SubscriptionTier.Basic
        };

        var uri = $"wallet/{newWallet.WalletId}/subscription";
        var walletCreationSubscriptionResponse = await _httpClient.PostAsJsonAsync(uri, createSubscriptionRequest);
        walletCreationSubscriptionResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var createSubscriptionResponseString = await walletCreationSubscriptionResponse.Content.ReadAsStringAsync();
        var createSubscriptionResponse = JsonSerializer.Deserialize<SubscriptionResponse>(createSubscriptionResponseString);

        var subscriptions = (Dictionary<Guid, SubscriptionResponse>)_featureContext["Subscriptions"];
        subscriptions.Add(newWallet.WalletId, createSubscriptionResponse);

        var createAccountUri = $"wallet/{newWallet.WalletId}/account";
        var createAccountResponse = await _httpClient.PostAsJsonAsync(createAccountUri, new { });
        createAccountResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var walletAccountResponse = await createAccountResponse.Content.ReadAsStringAsync();
        var newWalletWithAccount = JsonSerializer.Deserialize<Wallet>(walletAccountResponse);

        walletsList.Add(newWalletWithAccount);
    }
}
