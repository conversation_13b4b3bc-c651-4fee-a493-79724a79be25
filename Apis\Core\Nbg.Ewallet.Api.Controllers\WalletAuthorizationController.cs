using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Middlewares;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;

namespace Nbg.Ewallet.Api.Controllers;

/// <summary>
/// This Controller Provides Wallet Authorization functionality.
/// </summary>
[ApiController]
[ApiExplorerSettings(GroupName = "Wallet")]
[Route("wallet")]
[Produces("application/json")]
[Consumes("application/json")]
public class WalletAuthorizationController : ControllerBase
{
    private readonly IWalletAuthorizationRequestsService _walletAuthorizationRequestsService;

    public WalletAuthorizationController(IWalletAuthorizationRequestsService walletAuthorizationRequestsService)
    {
        _walletAuthorizationRequestsService = walletAuthorizationRequestsService;
    }

    /// <summary>
    /// Requests authorization for a wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="request">The request containing authorization details.</param>
    /// <returns>A response containing authorization request details.</returns>
    /// <response code="200">Authorization request created successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(WalletAuthorizationResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPost]
    [Route("{walletId}/authorization-requests", Name = "requestauthorization")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<WalletAuthorizationResponse>> AuthorizationRequest([FromRoute] Guid walletId, WalletAuthorizationRequest request)
    {
        return await _walletAuthorizationRequestsService.RequestAuthorization(walletId, request);
    }

    /// <summary>
    /// Retrieves authorization requests for a wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="status">The status of the authorization requests to filter by.</param>
    /// <returns>A list of authorization requests for the specified wallet.</returns>
    /// <response code="200">Authorization requests retrieved successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(List<WalletAuthorizationResponse>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpGet]
    [Route("{walletId}/authorization-requests", Name = "getauthorizationrequests")]
    public async Task<ActionResult<List<WalletAuthorizationResponse>>> GetAuthorizationRequests([FromRoute] Guid walletId, [FromQuery] RequestStatus? status)
    {
        return await _walletAuthorizationRequestsService.GetAuthorizationRequests(walletId, status);
    }

    /// <summary>
    /// Rejects an authorization request for a wallet account.
    /// </summary>
    /// <param name="walletId">The wallet identifier.</param>
    /// <param name="authRequestId">The authorization request identifier.</param>
    /// <returns>The rejected authorization request.</returns>
    /// <response code="200">Authorization request rejected successfully.</response>
    /// <response code="400">If the request is invalid.</response>
    /// <response code="401">If the user is unauthorized.</response>
    /// <response code="403">If the user is forbidden from accessing this resource.</response>
    /// <response code="500">If there is an internal server error.</response>
    [ProducesResponseType(typeof(WalletAuthorizationResponse), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(typeof(ErrorResponse), 401)]
    [ProducesResponseType(typeof(ErrorResponse), 403)]
    [ProducesResponseType(typeof(ErrorResponse), 500)]
    [HttpPut]
    [Route("{walletId}/authorization-requests/{authRequestId}/reject", Name = "rejectrequestauthorization")]
    [EwalletAuthorize(AuthorizationTypes.Admin, AuthorizationTypes.RegisteredWallet)]
    public async Task<ActionResult<WalletAuthorizationResponse>> RejectAuthorizationRequest([FromRoute] Guid walletId, [FromRoute] Guid authRequestId)
    {
        return await _walletAuthorizationRequestsService.RejectAuthorizationRequest(walletId, authRequestId);
    }
}
