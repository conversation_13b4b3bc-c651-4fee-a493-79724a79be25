﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Options;
using Nbg.Ewallet.Repository.Interfaces;
using nbg.netcore.consent.interfaces;
using nbg.netcore.consent.repository.types;
using nbg.netcore.consent.types;
using Nbg.NetCore.HttpExceptions;
using Nbg.NetCore.Utilities;
using Nbg.Ewallet.Repository.Types.Configuration;

namespace nbg.ewallet.repository;

public class ConsentsRepositoryService : IConsentsRepositoryService
{
    private readonly IMapper _mapper;
    private readonly IConsentsCoreService _consentsCoreService;
    private readonly RepositorySettings _repositorySettings;

    public ConsentsRepositoryService(
        IMapper mapper,
        IConsentsCoreService consentsCoreService,
        IOptions<RepositorySettings> repositoryOptions)
    {
        _mapper = mapper;
        _consentsCoreService = consentsCoreService;
        _repositorySettings = repositoryOptions.Value;
    }

    public async Task<RepoConsentFull> CreateConsentAsync(RepoConsent consent)
    {
        consent.ApplicationConsentTemplateId = Guid.Parse(_repositorySettings.ApplicationConsentTemplateId);
        var createConsentRequest = _mapper.Map<CreateConsentRequest>(consent);
        var generatedConsent = await _consentsCoreService.GenerateConsentAsync(createConsentRequest);

        return _mapper.Map<RepoConsentFull>(generatedConsent);
    }

    public async Task<RepoConsentFull> GetConsentAsync(string consentId)
    {
        var validConsentGuid = ConvertToValidConsentGuid(consentId);
        var consent = await _consentsCoreService.GetConsentAsync(validConsentGuid);

        return _mapper.Map<RepoConsentFull>(consent);
    }

    public async Task<RepoConsentFull> UpdateConsentAsync(string consentId, RepoConsent consent)
    {
        var validConsentGuid = ConvertToValidConsentGuid(consentId);
        var updateConsentRequest = _mapper.Map<UpdateConsentRequest>(consent);
        var updatedConsent = await _consentsCoreService.UpdateConsentAsync(validConsentGuid, updateConsentRequest);

        return _mapper.Map<RepoConsentFull>(updatedConsent);
    }

    private static Guid ConvertToValidConsentGuid(string consentId)
    {
        if (consentId.Clear() == null)
            throw new BadRequestException("ConsentId is null or empty");

        if (!Guid.TryParse(consentId, out var validConsentId))
            throw new BadRequestException("ConsentId is not in GUID format");

        return validConsentId;
    }
}
