﻿using System.Threading.Tasks;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;
using Nbg.NetCore.CosmosConnector.Types.OpenSBAccount;
using Nbg.NetCore.CosmosConnector.Types.UpdIndicator;

namespace Nbg.Ewallet.Repository.Interfaces;

public interface IMainFrameConnector
{
    Task<NetCore.CosmosConnector.Types.Customer.Customer> GetCustomerDataAsync(string customerCode);
    Task<CustomerAuthorizationLevel> GetAuthorizationLevelAsync(string userId);
    Task<string> GetTaxIdFromUserProfileAsync(string userId, string customerCode);
    Task<UpdIndicator> SubmitCustomerCommissionAsync(string userId, string customerCode, long? indicationKey, long? indicationValue);
    Task<OpenSBAccount> CreateAccountAsync(string craCode, string userId, string productCode);
    Task<CustomerProductDetails> GetCustomerProductDetailsAsync(string customerCode);
    Task<string> GetAccountFromIBAN(string iban);
    Task<string> GetIBANFromAccount(string account);
}
