using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Nbg.Ewallet.Api.Sandbox;
using System.Text.Json;

namespace SandboxTest.Infrastructure
{
    public class SandboxApiFactory : WebApplicationFactory<Program>
    {
        private readonly string _sandboxId;

        public SandboxApiFactory()
        {
            _sandboxId = Guid.NewGuid().ToString();
        }

        public string SandboxId => _sandboxId;

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.Testing.json", optional: true);
            });

            builder.UseEnvironment("Testing");
        }

        public async Task InitializeSandboxAsync()
        {
            using var client = CreateClient();
            
            var requestPayload = new
            {
                sandboxId = _sandboxId
            };

            var response = await client.PostAsJsonAsync("sandbox", requestPayload);
            response.EnsureSuccessStatusCode();
        }
    }
}