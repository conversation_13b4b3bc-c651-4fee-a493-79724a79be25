﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by SpecFlow (https://www.specflow.org/).
//      SpecFlow Version:3.9.0.0
//      SpecFlow Generator Version:3.9.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
namespace EWallet.Tests.Features
{
    using TechTalk.SpecFlow;
    using System;
    using System.Linq;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public partial class WalletInternalUserPermissionsFeature : object, Xunit.IClassFixture<WalletInternalUserPermissionsFeature.FixtureData>, System.IDisposable
    {
        
        private static TechTalk.SpecFlow.ITestRunner testRunner;
        
        private static string[] featureTags = ((string[])(null));
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "InternalPermissions.feature"
#line hidden
        
        public WalletInternalUserPermissionsFeature(WalletInternalUserPermissionsFeature.FixtureData fixtureData, EWallet_Tests_XUnitAssemblyFixture assemblyFixture, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
            this.TestInitialize();
        }
        
        public static void FeatureSetup()
        {
            testRunner = TechTalk.SpecFlow.TestRunnerManager.GetTestRunner();
            TechTalk.SpecFlow.FeatureInfo featureInfo = new TechTalk.SpecFlow.FeatureInfo(new System.Globalization.CultureInfo("en-US"), "Features", "Wallet internal user permissions", null, ProgrammingLanguage.CSharp, featureTags);
            testRunner.OnFeatureStart(featureInfo);
        }
        
        public static void FeatureTearDown()
        {
            testRunner.OnFeatureEnd();
            testRunner = null;
        }
        
        public void TestInitialize()
        {
        }
        
        public void TestTearDown()
        {
            testRunner.OnScenarioEnd();
        }
        
        public void ScenarioInitialize(TechTalk.SpecFlow.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public void ScenarioStart()
        {
            testRunner.OnScenarioStart();
        }
        
        public void ScenarioCleanup()
        {
            testRunner.CollectScenarioErrors();
        }
        
        void System.IDisposable.Dispose()
        {
            this.TestTearDown();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="As an IB user, I want to give permissionss to an internal user")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "As an IB user, I want to give permissionss to an internal user")]
        [Xunit.InlineDataAttribute("0", "2", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", "true", new string[0])]
        public void AsAnIBUserIWantToGivePermissionssToAnInternalUser(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string inheritsAuthorizations, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            argumentsOfScenario.Add("inheritsAuthorizations", inheritsAuthorizations);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("As an IB user, I want to give permissionss to an internal user", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 2
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 3
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 5
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 6
    testRunner.When(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 7
    testRunner.Then(string.Format("the {0} must have {1} and {2} and {3} and {4} and {5} and {6} and {7} and {8} and" +
                            " {9} permissions", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="As an IB user, I want to retrive permissionss for an internal user")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "As an IB user, I want to retrive permissionss for an internal user")]
        [Xunit.InlineDataAttribute("0", "2", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", "true", new string[0])]
        public void AsAnIBUserIWantToRetrivePermissionssForAnInternalUser(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string inheritsAuthorizations, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            argumentsOfScenario.Add("inheritsAuthorizations", inheritsAuthorizations);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("As an IB user, I want to retrive permissionss for an internal user", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 14
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 15
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 16
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 17
    testRunner.Then(string.Format("the {0} must have {1} and {2} and {3} and {4} and {5} and {6} and {7} and {8} and" +
                            " {9} permissions", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Revoke permissions for an internal user")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "Revoke permissions for an internal user")]
        [Xunit.InlineDataAttribute("0", "2", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", "true", new string[0])]
        public void RevokePermissionsForAnInternalUser(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string inheritsAuthorizations, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            argumentsOfScenario.Add("inheritsAuthorizations", inheritsAuthorizations);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Revoke permissions for an internal user", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 24
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 25
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 26
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 27
    testRunner.When(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 28
    testRunner.And(string.Format("I revoke permissions for user {0}", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 29
    testRunner.Then(string.Format("Permissions are revoked for {0}", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Retrieve only active permissions when sowAll flag is false")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "Retrieve only active permissions when sowAll flag is false")]
        [Xunit.InlineDataAttribute("0", "2", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", "true", new string[0])]
        public void RetrieveOnlyActivePermissionsWhenSowAllFlagIsFalse(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string inheritsAuthorizations, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            argumentsOfScenario.Add("inheritsAuthorizations", inheritsAuthorizations);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Retrieve only active permissions when sowAll flag is false", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 36
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 37
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 38
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 39
    testRunner.When(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 40
    testRunner.And(string.Format("I revoke permissions for user {0} for wallet <walletName>", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 41
    testRunner.And(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 42
    testRunner.Then(string.Format("only the active permission is retrieved for user {0} for wallet <walletName>", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Fail to assign permission to self")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "Fail to assign permission to self")]
        [Xunit.InlineDataAttribute("0", "0", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void FailToAssignPermissionToSelf(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Fail to assign permission to self", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 49
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 50
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 51
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 52
    testRunner.When(string.Format("I give permissions to user {0} and this user is me", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 53
    testRunner.Then(string.Format("no permission is applied to user {0} for wallet <walletName>", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Fail to revoke permission from self")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "Fail to revoke permission from self")]
        [Xunit.InlineDataAttribute("0", "2", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", new string[0])]
        public void FailToRevokePermissionFromSelf(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Fail to revoke permission from self", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 60
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 61
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 63
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 64
    testRunner.When(string.Format("I have permissions as user {0}", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 65
    testRunner.And(string.Format("I revoke permissions for user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 66
    testRunner.Then(string.Format("no permission is revoked to user {0}", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Retrieve only active permissions for user when showAll flag is false for all wall" +
            "et users")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "Retrieve only active permissions for user when showAll flag is false for all wall" +
            "et users")]
        [Xunit.InlineDataAttribute("0", "2", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", "true", new string[0])]
        public void RetrieveOnlyActivePermissionsForUserWhenShowAllFlagIsFalseForAllWalletUsers(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string inheritsAuthorizations, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            argumentsOfScenario.Add("inheritsAuthorizations", inheritsAuthorizations);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Retrieve only active permissions for user when showAll flag is false for all wall" +
                    "et users", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 73
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 74
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 76
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 77
    testRunner.When(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 78
    testRunner.And(string.Format("I revoke permissions for user {0}", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 79
    testRunner.And(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 80
    testRunner.Then("only the active permissions are retrieved for <walletName> users", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Retrieve all active permissions for user when showAll flag is false for all walle" +
            "t users")]
        [Xunit.TraitAttribute("FeatureTitle", "Wallet internal user permissions")]
        [Xunit.TraitAttribute("Description", "Retrieve all active permissions for user when showAll flag is false for all walle" +
            "t users")]
        [Xunit.InlineDataAttribute("0", "2", "false", "true", "true", "true", "true", "WalletToWallet", "1000", "YEARLY", "true", new string[0])]
        public void RetrieveAllActivePermissionsForUserWhenShowAllFlagIsFalseForAllWalletUsers(string userId, string internalUserId, string admin, string approve, string balanceView, string submit, string transactionView, string transactionType, string amount, string timeframe, string inheritsAuthorizations, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("userId", userId);
            argumentsOfScenario.Add("internalUserId", internalUserId);
            argumentsOfScenario.Add("admin", admin);
            argumentsOfScenario.Add("approve", approve);
            argumentsOfScenario.Add("balanceView", balanceView);
            argumentsOfScenario.Add("submit", submit);
            argumentsOfScenario.Add("transactionView", transactionView);
            argumentsOfScenario.Add("transactionType", transactionType);
            argumentsOfScenario.Add("amount", amount);
            argumentsOfScenario.Add("timeframe", timeframe);
            argumentsOfScenario.Add("inheritsAuthorizations", inheritsAuthorizations);
            TechTalk.SpecFlow.ScenarioInfo scenarioInfo = new TechTalk.SpecFlow.ScenarioInfo("Retrieve all active permissions for user when showAll flag is false for all walle" +
                    "t users", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 89
 this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((TagHelper.ContainsIgnoreTag(tagsOfScenario) || TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                this.ScenarioStart();
#line 90
  testRunner.Given(string.Format("I am logged in as the user {0}", userId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Given ");
#line hidden
#line 91
    testRunner.And("As an owner of a wallet", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 92
    testRunner.When(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "When ");
#line hidden
#line 93
    testRunner.And(string.Format("I revoke permissions for user {0}", internalUserId), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 94
    testRunner.And(string.Format("I give {0} permissions with {1} and {2} and {3} and {4} and {5} and {6} and {7} a" +
                            "nd {8} and {9}", internalUserId, admin, approve, balanceView, submit, transactionView, inheritsAuthorizations, transactionType, amount, timeframe), ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "And ");
#line hidden
#line 95
    testRunner.Then("all the active permissions are retrieved", ((string)(null)), ((TechTalk.SpecFlow.Table)(null)), "Then ");
#line hidden
            }
            this.ScenarioCleanup();
        }
        
        [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
        [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : System.IDisposable
        {
            
            public FixtureData()
            {
                WalletInternalUserPermissionsFeature.FeatureSetup();
            }
            
            void System.IDisposable.Dispose()
            {
                WalletInternalUserPermissionsFeature.FeatureTearDown();
            }
        }
    }
}
#pragma warning restore
#endregion
