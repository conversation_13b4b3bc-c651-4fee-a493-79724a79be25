﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Transfers;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Records;
using Nbg.NetCore.Utilities;

namespace Nbg.Ewallet.Api.Implementation;

public class BatchTransferRequestProvider : PayTransBase, IBatchTransferRequestProvider
{
    private readonly IWalletRepositoryService _walletRepository;
    private readonly ITransactionRepositoryService _transactionRepository;
    private readonly IAccountsApiClientService _accountsApiClientService;
    private readonly ICustomerCommissionService _customerCommissionService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly IMapper _mapper;
    private readonly IIbanAccountUtility _ibanAccountUtility;
    private readonly ILogger<BatchTransferRequestProvider> _logger;

    public BatchTransferRequestProvider(IWalletRepositoryService walletRepository,
        IHttpContextRepositoryService httpContextService,
        IAuthContextService authContextService,
        ITransactionRepositoryService transactionRepository,
        IAccountsApiClientService accountsApiClientService,
        ICustomerCommissionService customerCommissionService,
        IWalletRepositoryService walletRepositoryService,
        IMapper mapper,
        IIbanAccountUtility ibanAccountUtility,
        ILogger<BatchTransferRequestProvider> logger)
        : base(httpContextService, authContextService)
    {
        _walletRepository = walletRepository;
        _transactionRepository = transactionRepository;
        _accountsApiClientService = accountsApiClientService;
        _customerCommissionService = customerCommissionService;
        _walletRepositoryService = walletRepositoryService;
        _mapper = mapper;
        _ibanAccountUtility = ibanAccountUtility;
        _logger = logger;
    }

    public BatchTransfersResponse EnrichResponse(BatchTransfersRequest request, List<RepoTransaction> transactions)
    {
        var response = new BatchTransfersResponse() { BatchId = transactions.Select(x => x.BatchId.GetValueOrDefault()).FirstOrDefault() };

        foreach (var transfer in request.Batch)
        {
            var transaction = transactions.Where(x => x.TransactionId == transfer.TransactionId).FirstOrDefault();
            var transferResponse = transfer.EnrichResponse(new TransfersResponse()
            {
                RequiresApproval = transaction.Status.Value == TransactionStatus.PENDING_APPROVAL,
                TransactionId = transaction.TransactionId
            });
            response.Transfers.Add(transferResponse);
        }

        return response;
    }

    public async Task<BatchTransferExpensesCommissionsResponse> GetCommissionAsync(BatchTransfersRequest request, Guid walletId)
    {
        (_, var customerCode, var executedAs) = GetUserAndCustomerCode();
        var response = new BatchTransferExpensesCommissionsResponse() { CommissionSum = BatchCalculateFeesResponse.CommissionSum(), Commissions = [] };

        var commissionTransactionCount = 0;
        foreach (var transfer in request.Batch)
        {
            var transactionSubType = await GetTransactionSubTypeAsync(transfer.ReceiverAcc);
            await _customerCommissionService.UpdateCustomerCommissionAsync(walletId, transactionSubType, executedAs, customerCode, commissionTransactionCount);

            var transfersCommissionRequest = transfer.ToTransferExpensesCommissionsRequest(executedAs);
            transfersCommissionRequest.UserID = executedAs;

            try
            {
                var res = await _accountsApiClientService.CalculateTransferExpensesCommissionsAsync(transfersCommissionRequest);
                var commission = _mapper.Map<BatchCalculateFeesResponse>(res);
                commission.TransactionType = transactionSubType;
                //keep the original transaction ID for reference
                commission.TransactionId = transfer.TransactionId;
                response.Commissions.Add(commission);
                response.CommissionSum.Add(commission);

                commissionTransactionCount++;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error calculating fees for wallet {WalletId}", walletId);
                response.Commissions.Add(BatchCalculateFeesResponse.ErrorFail(e.Message));
            }
        }

        return response;
    }

    public TotalAmountWithCommission GetTotalAmountWithCommission(BatchTransfersRequest request, BatchTransferExpensesCommissionsResponse commissionResponse)
    {
        var totalAmount = request.Batch.Sum(x => x.Amount) + commissionResponse.CommissionSum.SumCommission;
        return new TotalAmountWithCommission(request.Batch[0].Currency ?? "EUR", totalAmount, commissionResponse.CommissionSum.SumCommission);
    }

    public async Task<BatchTransfersResponse> HandleTransactionExecutionsAsync(BatchTransfersRequest request, List<RepoTransaction> transactions, RepoWallet wallet)
    {
        var (userId, customerCode, executedAs) = GetUserAndCustomerCode();

        await EnsureSufficientBalance(request, wallet, executedAs);

        var batchTransfersResponse = new BatchTransfersResponse()
        {
            Transfers = [],
            BatchId = transactions.Select(x => x.BatchId.GetValueOrDefault()).FirstOrDefault()
        };

        foreach (var transfer in request.Batch)
        {
            TransfersResponse transferResponse;
            var transaction = transactions.FirstOrDefault(x => x.TransactionId == transfer.TransactionId);

            try
            {
                await _customerCommissionService.UpdateCustomerCommissionAsync(wallet.WalletId, transaction.TransactionSubType, userId, customerCode);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error updating customer commission for wallet {WalletId}", wallet.WalletId);
                transferResponse = await transfer.HandleCommissionExceptionAsync(e, transaction, _transactionRepository);
                batchTransfersResponse.Transfers.Add(transferResponse);
                continue;
            }

            transfer.SYDIPEL = wallet.OwnerCustomerCode;
            transfer.IsCorporateUser = wallet.IsCorporateUser;
            transfer.UserID = executedAs;
            var response = await _accountsApiClientService.ExecuteTransactionAsync(transfer);


            transaction.ExecutedBy = userId;
            transaction.ExecutedAs = executedAs;
            transferResponse = await response.HandleResultNoExceptionAsync(transfer, transaction, _transactionRepository);

            batchTransfersResponse.Transfers.Add(transferResponse);
        }

        return batchTransfersResponse;
    }

    public async Task<List<RepoTransaction>> InitiateRepoTransactions(BatchTransfersRequest request, RepoWallet wallet, string userId)
    {
        var transactions = new List<RepoTransaction>();
        var batchId = request.BatchId;
        foreach (var transfer in request.Batch)
        {
            var transactionSubType = await transfer.GetTransactionSubType(_walletRepository);
            var transaction = transfer.ToPendingRepoTransaction(wallet, userId, transactionSubType);
            transfer.TransactionId = transaction.TransactionId;

            transaction.BatchId = batchId;
            transactions.Add(transaction);
        }

        return transactions;
    }

    public async Task<ValidateRequestResult> ValidateRequest(BatchTransfersRequest request, RepoWallet wallet)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        //check if batch is not empty
        if (request.Batch.Count == 0)
        {
            throw new EmptyBatch();
        }

        //check if in batch all payments have account IBAN
        if (request.Batch.Any(x => string.IsNullOrWhiteSpace(x.DebitAccount)))
        {
            throw new SourceAccountMissing();
        }

        //check if request has the same debit account in all payments
        var firstIban = request.Batch[0].DebitAccount;
        if (request.Batch.Any(x => x.DebitAccount != firstIban))
        {
            throw new SourceAccountNotTheSame();
        }

        foreach (var transfer in request.Batch)
        {
            transfer.ValidateAccountBICTransferType();
        }

        var (userId, _, _) = GetUserAndCustomerCode();
        var transfer2 = request.Batch.First();
        return await transfer2.ValidateAccountAvailableToUser(wallet, userId, _accountsApiClientService);
    }

    private async Task EnsureSufficientBalance(BatchTransfersRequest request, RepoWallet wallet, string userId)
    {
        var validationResult = await request.Batch[0].ValidateAccountAvailableToUser(wallet, userId, _accountsApiClientService);
        var commissionResponse = await GetCommissionAsync(request, wallet.WalletId);
        var accountBalance = await _accountsApiClientService.GetAccountBalanceAsync(new GetAccountBalanceRequest
        {
            Account = await _ibanAccountUtility.GetAccountFromIbanAsync(wallet.WalletAccount),
            UserId = userId,
            Currency = "070"
        });
        var totalAmount = request.Batch.Sum(x => x.Amount) + commissionResponse.CommissionSum.SumCommission;
        if (totalAmount > accountBalance.AvailableBalance)
        {
            _logger.LogDebug("Insufficient balance for wallet {WalletId}. Available: {Available}, Required: {Required}",
                wallet.WalletId, accountBalance.AvailableBalance, totalAmount);
            throw new InsufficientBalanceException();
        }
    }

    public async Task<TransactionSubType> GetTransactionSubTypeAsync(string receiverAccount)
    {
        var wallet = await _walletRepositoryService.FindOneByWalletAccountAsync(receiverAccount);
        if (wallet != null) return TransactionSubType.WalletToWallet;
        if (AccountHelpers.IbanIsNbg(receiverAccount)) return TransactionSubType.WalletToNBG;
        else return TransactionSubType.WalletToIBAN;
    }

    public async Task<BatchTransfersResponse> TransactionForecastExecutionsAsync(BatchTransfersRequest request, RepoWallet wallet)
    {
        var (userId, customerCode, executedAs) = GetUserAndCustomerCode();

        await EnsureSufficientBalance(request, wallet, executedAs);

        return new BatchTransfersResponse
        {
            BatchId = request.BatchId
        };
    }

    public void SetNewDebitAccount(BatchTransfersRequest request, string ibanAccount)
    {
        foreach (var transfer in request.Batch)
        {
            transfer.DebitAccount = ibanAccount;
        }
    }
}
