﻿using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.NetCore.CosmosConnector.Implementation.GetAccountIban;
using Nbg.NetCore.CosmosConnector.Implementation.GetCIFDetails;
using Nbg.NetCore.CosmosConnector.Implementation.GetCustomerProductDetails;
using Nbg.NetCore.CosmosConnector.Implementation.OpenSBAccount;
using Nbg.NetCore.CosmosConnector.Implementation.UpdIndicator;
using Nbg.NetCore.CosmosConnector.Interfaces;
using Nbg.NetCore.CosmosConnector.Types;
using Nbg.NetCore.CosmosConnector.Types.Constants;
using Nbg.NetCore.CosmosConnector.Types.Customer;
using Nbg.NetCore.CosmosConnector.Types.CustomerProductDetails;
using Nbg.NetCore.CosmosConnector.Types.GetAccountIban;
using Nbg.NetCore.CosmosConnector.Types.GetCIFDetails;
using Nbg.NetCore.CosmosConnector.Types.GetCustomerProductDetails;
using Nbg.NetCore.CosmosConnector.Types.OpenSBAccount;
using Nbg.NetCore.CosmosConnector.Types.UpdIndicator;

namespace Nbg.Ewallet.Repository;

public class CosmosConnector : IMainFrameConnector
{
    private readonly IAuditableAceConnector _aceConnector;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly ILogger<CosmosConnector> _logger;

    public CosmosConnector(
        ILogger<CosmosConnector> logger,
        IAuditableAceConnector aceConnector,
        IHttpContextRepositoryService httpContextRepositoryService)
    {
        _aceConnector = aceConnector;
        _httpContextRepositoryService = httpContextRepositoryService;
        _logger = logger;
    }

    public async Task<NetCore.CosmosConnector.Types.Customer.Customer> GetCustomerDataAsync(string customerCode)
    {
        var request = new AceRequest<GetCIFDetailsRequestPayload> { Headers = new AceRequestHeaders(), Payload = new GetCIFDetailsRequestPayload { CustomerId = customerCode } };

        AceResponse<CustomerResponse> response;
        try
        {
            response = await _aceConnector.GetCIFDetailsAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while fetching customer data for CustomerCode: {CustomerCode}", customerCode);
            throw new AceConnectorException($"An error occurred while fetching customer data for CustomerCode: {customerCode}", ex);
        }

        if (response.ErrorResponse is null) return response.Payload.ToCustomer();

        _logger.LogError("Error fetching customer data: {ErrorResponseCode} - {ErrorResponseDescription}, {ErrorResponseSystem}",
            response.ErrorResponse.Code, response.ErrorResponse.Description, response.ErrorResponse.System);
        throw new AceConnectorException($"Error fetching customer data: {response.ErrorResponse.Code} - {response.ErrorResponse.Description}");
    }

    public Task<CustomerAuthorizationLevel> GetAuthorizationLevelAsync(string userId)
    {
        CustomerAuthorizationLevel result = new()
        {
            Category = _httpContextRepositoryService.GetAuthorizationLevel(),
            Approvals = _httpContextRepositoryService.GetNumberOfApprovals()
        };

        return Task.FromResult(result);
    }

    public async Task<string> GetTaxIdFromUserProfileAsync(string userId, string customerCode)
    {
        var response = await GetCustomerDataAsync(customerCode);
        return response.BasicInfo.TaxIdentificationNumber;
    }

    public async Task<UpdIndicator> SubmitCustomerCommissionAsync(string userId, string customerCode, long? indicationKey, long? indicationValue)
    {
        ArgumentNullException.ThrowIfNull(indicationKey, nameof(indicationKey));
        ArgumentNullException.ThrowIfNull(indicationValue, nameof(indicationValue));

        var request = new AceRequest<UpdIndicatorRequestPayload>
        {
            Payload = new UpdIndicatorRequestPayload
            {
                IndicatorInfoEntryType = indicationKey.GetValueOrDefault(0).ToString("D", CultureInfo.InvariantCulture),
                IndicatorInfoEntryValue = indicationValue.GetValueOrDefault(0).ToString("D", CultureInfo.InvariantCulture),
                IndicatorInfoEntryStartDate = DateTime.Now.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture),
                CustomerId = customerCode
            }
        };
        request.Headers.BranchId = "035";
        //request.Headers.UserId = "E99999";
        request.Headers.AuditUserId = "E99999";

        AceResponse<UpdIndicatorResponsePayload> response;
        try
        {
            response = await _aceConnector.UpdIndicatorAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while Managing Customer single Indicator entry for CustomerCode: {CustomerCode}", customerCode);
            throw new AceConnectorException($"An error occurred while Managing Customer single Indicator entry for CustomerCode: {customerCode}", ex);
        }

        if (response.ErrorResponse is null)
        {
            return response.Payload.ToUpdIndicator();
        }

        if (response.ErrorResponse.SystemErrorResponse is not null
            && response.ErrorResponse.SystemErrorResponse.Any(x => x.Code == SystemErrorResponseCodes.AlreadyExistsErrorCode))
        {
            return response.Payload.ToUpdIndicator();
        }

        _logger.LogError("Managing Customer single Indicator: {ErrorResponseCode} - {ErrorResponseDescription}, {ErrorResponseSystem}",
            response.ErrorResponse.Code, response.ErrorResponse.Description, response.ErrorResponse.System);
        throw new AceConnectorException($"Managing Customer single Indicator: {response.ErrorResponse.Code} - {response.ErrorResponse.Description}");
    }

    public async Task<OpenSBAccount> CreateAccountAsync(string craCode, string userId, string productCode)
    {
        var request = new AceRequest<OpenSBAccountRequestPayload>
        {
            Payload = new OpenSBAccountRequestPayload
            {
                SchemeCode = productCode,
                AccountCurrency = "EUR",
                AccountOpeningBranch = "700",
                AccountOpeningDate = DateTime.Now.AddMonths(-4), //TODO why?!
                Customer = new NetCore.CosmosConnector.Types.OpenSBAccount.Customer { CustomerId = craCode, },
                OperationId = "49006",
            }
        };
        request.Headers.BranchId = "700";
        AceResponse<OpenSBAccountResponsePayload> response;
        try
        {
            response = await _aceConnector.OpenSBAccountAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while open a current account for CustomerCode: {CustomerCode}", userId);
            throw new AceConnectorException($"An error occurred while open a current account for CustomerCode: {userId}", ex);
        }

        if (response.ErrorResponse is null) return response.Payload.ToOpenSBAccount();

        _logger.LogError("Open a current account: {ErrorResponseCode} - {ErrorResponseDescription}, {ErrorResponseSystem}",
            response.ErrorResponse.Code, response.ErrorResponse.Description, response.ErrorResponse.System);
        throw new AceConnectorException($"Open a current account: {response.ErrorResponse.Code} - {response.ErrorResponse.Description}");
    }

    public async Task<CustomerProductDetails> GetCustomerProductDetailsAsync(string customerCode)
    {
        var request = new AceRequest<GetCustomerProductDetailsRequestPayload>()
        {
            Payload = new GetCustomerProductDetailsRequestPayload { CustomerId = customerCode }
        };

        AceResponse<GetCustomerProductDetailsResponsePayload> response;
        try
        {
            response = await _aceConnector.GetCustomerProductDetailsAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while fetching customer data for CustomerCode: {CustomerCode}", customerCode);
            throw new AceConnectorException($"An error occurred while fetching customer data for CustomerCode: {customerCode}", ex);
        }

        if (response.ErrorResponse is null) return response.Payload.ToCustomerProductDetails();

        _logger.LogError("Error fetching customer data: {ErrorResponseCode} - {ErrorResponseDescription}, {ErrorResponseSystem}",
            response.ErrorResponse.Code, response.ErrorResponse.Description, response.ErrorResponse.System);
        throw new AceConnectorException($"Error fetching customer data: {response.ErrorResponse.Code} - {response.ErrorResponse.Description}");
    }

    public async Task<string> GetAccountFromIBAN(string iban)
    {
        var payload = new GetAccountIbanRequestPayload { Iban = iban };
        var responce = await GetAccountIbanAsync(payload);
        return responce.AccountId;
    }

    public async Task<string> GetIBANFromAccount(string account)
    {
        var payload = new GetAccountIbanRequestPayload { AccountId = account };
        var responce = await GetAccountIbanAsync(payload);
        return responce.Iban;
    }

    private async Task<GetAccountIbanResponsePayload> GetAccountIbanAsync(GetAccountIbanRequestPayload payload)
    {
        var request = new AceRequest<GetAccountIbanRequestPayload>()
        {
            Payload = payload
        };

        AceResponse<GetAccountIbanResponsePayload> response;
        try
        {
            response = await _aceConnector.GetAccountIbanAsync(request);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while calling GetAccountIbanAsync");
            throw new AceConnectorException("An error occurred while calling GetAccountIbanAsync", ex);
        }

        if (response.ErrorResponse is null) return response.Payload;

        _logger.LogError("Error IBAN-Account: {ErrorResponseCode} - {ErrorResponseDescription}, {ErrorResponseSystem}",
            response.ErrorResponse.Code, response.ErrorResponse.Description, response.ErrorResponse.System);
        throw new AceConnectorException($"Error IBAN-Account: {response.ErrorResponse.Code} - {response.ErrorResponse.Description}");
    }
}
