﻿using System;
using System.Data;
using System.Threading.Tasks;

namespace Nbg.Ewallet.Repository.Interfaces.UnitOfWork;

/// <summary>
/// Manages multiple database connections and transactions.
/// The consumer is responsible for explicitly calling CommitAsync or RollbackAsync.
/// If not called, rollback will be triggered automatically on dispose.
/// </summary>
public interface IUnitOfWork : IDisposable
{
    Guid Id { get; }
    IDbConnection GetConnection(string dbKey);
    IDbTransaction GetTransaction(string dbKey);
    Task CommitPartialAsync();
    Task CommitFinalAsync();
    Task RollbackPartialAsync();
    Task RollbackFinalAsync();
    Task BeginNewTransactionAsync(string dbKey);
    Task ExecuteInTransactionAsync(string dbKey, Func<IDbConnection, IDbTransaction, Task> action, string errorMessage);
    Task<TResult> ExecuteInTransactionAsync<TResult>(string dbKey, Func<IDbConnection, IDbTransaction, Task<TResult>> action, string errorMessage);
}
