using System.Text.Json;
using System.Text.Json.Serialization;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Helper class for logging test output
    /// </summary>
    public static class TestOutputHelper
    {
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            WriteIndented = true,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        static TestOutputHelper()
        {
            JsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        /// <summary>
        /// Formats an object as JSON for logging
        /// </summary>
        public static string FormatJson<T>(T obj)
        {
            if (obj == null)
                return "null";

            try
            {
                return JsonSerializer.Serialize(obj, JsonOptions);
            }
            catch (Exception ex)
            {
                return $"Error serializing object: {ex.Message}";
            }
        }

        /// <summary>
        /// Formats an HTTP response for logging
        /// </summary>
        public static async Task<string> FormatHttpResponseAsync(HttpResponseMessage response)
        {
            if (response == null)
                return "null";

            try
            {
                var content = await response.Content.ReadAsStringAsync();
                var contentObj = JsonSerializer.Deserialize<object>(content);
                var formattedContent = JsonSerializer.Serialize(contentObj, JsonOptions);

                return $"Status: {(int)response.StatusCode} {response.StatusCode}\n" +
                       $"Headers: {string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))})\n" +
                       $"Content: {formattedContent}";
            }
            catch (Exception ex)
            {
                var content = await response.Content.ReadAsStringAsync();
                return $"Status: {(int)response.StatusCode} {response.StatusCode}\n" +
                       $"Headers: {string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))})\n" +
                       $"Content: {content}\n" +
                       $"Error formatting response: {ex.Message}";
            }
        }

        /// <summary>
        /// Formats an HTTP request for logging
        /// </summary>
        public static string FormatHttpRequest(HttpRequestMessage request)
        {
            if (request == null)
                return "null";

            try
            {
                return $"Method: {request.Method}\n" +
                       $"URL: {request.RequestUri}\n" +
                       $"Headers: {string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))})\n" +
                       $"Content: {(request.Content != null ? request.Content.ReadAsStringAsync().Result : "null")}";
            }
            catch (Exception ex)
            {
                return $"Method: {request.Method}\n" +
                       $"URL: {request.RequestUri}\n" +
                       $"Headers: {string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))})\n" +
                       $"Error formatting request: {ex.Message}";
            }
        }
    }
}
