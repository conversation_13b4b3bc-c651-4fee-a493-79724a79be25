<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Azure.Identity" Version="1.11.4" />
        <!--<PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />-->
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\Core\Nbg.Ewallet.Api.Core.Types\Nbg.Ewallet.Api.Types.csproj" />
        <ProjectReference Include="..\..\Core\Nbg.Ewallet.Repository.Types\Nbg.Ewallet.Repository.Types.csproj" />
    </ItemGroup>

</Project>