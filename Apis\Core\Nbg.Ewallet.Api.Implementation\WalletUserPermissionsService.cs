using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.NetCore.HttpExceptions;

namespace Nbg.Ewallet.Api.Implementation;

public class WalletUserPermissionsService : IWalletUserPermissionsService
{
    private readonly ILogger<WalletUserPermissionsService> _logger;
    private readonly IMapper _mapper;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly ICorporateApiClientService _corporateApiClientService;
    private readonly ILimitRepositoryService _limitRepositoryService;
    private readonly IUserPermissionsRepositoryService _userPermissionsRepositoryService;
    private readonly IUnitOfWork _unitOfWork;

    public WalletUserPermissionsService(
        ILogger<WalletUserPermissionsService> logger,
        IMapper mapper,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletRepositoryService walletRepositoryService,
        ICorporateApiClientService corporateApiClientService,
        ILimitRepositoryService limitRepositoryService,
        IUserPermissionsRepositoryService userPermissionsRepositoryService,
        IUnitOfWork unitOfWork
    )
    {
        _logger = logger;
        _mapper = mapper;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletRepositoryService = walletRepositoryService;
        _corporateApiClientService = corporateApiClientService;
        _limitRepositoryService = limitRepositoryService;
        _userPermissionsRepositoryService = userPermissionsRepositoryService;
        _unitOfWork = unitOfWork;
    }

    public async Task<List<UserPermission>> GetWalletPermissionForUser(Guid walletId, string Userid, bool showAll)
    {
        List<RepoUserPermission> repoPermissions;
        if (showAll)
        {
            repoPermissions = await _userPermissionsRepositoryService.FindAllByExpiredAndWalletIdAndUserIdAsync(walletId, Userid);
        }
        else
        {
            repoPermissions = await _userPermissionsRepositoryService.FindAllByActiveAndWalletIdAndUserId(walletId, Userid);
        }

        if (repoPermissions.Count == 0) return [];
        var permissions = _mapper.Map<List<UserPermission>>(repoPermissions);

        foreach (var perm in permissions)
        {
            var repoRermLimits = await _limitRepositoryService.RepoLimitFindAllByPermissionId(perm.Id);
            perm.Limits = _mapper.Map<List<Limit>>(repoRermLimits);
        }
        return permissions;
    }

    public async Task<UserPermissionResponse> GetWalletUserPermissions(Guid walletId, bool showAll)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);

        if (myWallet.WalletId != walletId) throw new InsufficientUserRightsException();
        List<RepoUserPermission> permissions;
        if (showAll)
        {
            permissions = await _userPermissionsRepositoryService.FindAllByExpiredAndWalletIdAndUserIdAsync(walletId, null);
        }
        else
        {
            permissions = await _userPermissionsRepositoryService.FindAllByActiveAndWalletIdAndUserId(walletId, null);
        }

        var response = new UserPermissionResponse
        {
            Permissions = _mapper.Map<List<UserPermission>>(permissions)
        };

        foreach (var perm in response.Permissions)
        {
            var repoRermLimits = await _limitRepositoryService.GetByPermissionIdAsync(perm.Id.ToString());
            perm.Limits = _mapper.Map<List<Limit>>(repoRermLimits);
        }
        return response;
    }

    public async Task<UserPermission> RevokeUserPermissionsForUser(string userId)
    {
        var myUserId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(myUserId);

        var userPermissions = await _userPermissionsRepositoryService.FindAllByActiveAndUserIdAndWalletIdAsync(userId, myWallet.WalletId);
        if (userPermissions == null || userPermissions.Count == 0) throw new BadRequestException("User has no permissions");

        foreach (var userPermission in userPermissions)
        {
            if (myWallet.OwnerUserId == userId) throw new CannotRevokeOwnPermissionsxception();
            if (userPermission.ExpirationDate < DateTime.UtcNow) continue;
            userPermission.ExpirationDate = DateTime.UtcNow;
            userPermission.RevokedBy = myUserId;

            await _corporateApiClientService.DisconnectAccountAsync(userId, myWallet.WalletAccount);

            await _userPermissionsRepositoryService.SaveAsync(userPermission);
            var permission = _mapper.Map<UserPermission>(userPermission);

            var repoLimit = await _limitRepositoryService.GetByPermissionIdAsync(userPermission.Id.ToString());
            permission.Limits = _mapper.Map<List<Limit>>(repoLimit);
            return permission;
        }
        throw new BadRequestException("User has no permissions");
    }

    public async Task<UserPermissionResponse> SetWalletUserPermissions(SetWalletUserPermissionsRequest request)
    {
        //check if different permissions are applied to the same user, this should not be allowed
        //validate one permission per user
        var duplicateUserPermissions = request.Permissions.GroupBy(x => x.UserId).FirstOrDefault(y => y.Count() > 1);
        if (duplicateUserPermissions != null)
        {
            _logger.LogError("Request with wallet Id {WalletId} tried to set multiple permissions for the same user {DuplicateUserId}",
                request.WalletId, duplicateUserPermissions.Key);
            throw new OnlyOnePermissionPerUserIsAllowedException();
        }

        //validate that only one limit is set for each transaction type
        foreach (var permission in request.Permissions)
        {
            var limitsforuser = permission.Limits;
            if (limitsforuser == null) continue;
            var duplicateLimits = limitsforuser.GroupBy(x => x.TransactionType).FirstOrDefault(y => y.Count() > 1);
            if (duplicateLimits != null)
            {
                _logger.LogError("Request with wallet Id {WalletId} tried to set multiple limits for the same transaction type {TransactionType}",
                    request.WalletId, duplicateLimits.Key);
                throw new OnlyOneTransactionTypeInLimitsIsAllowed();
            }
        }

        var userId = _httpContextRepositoryService.GetUserId();
        var myWallet = await _walletRepositoryService.FindOneByOwnerUserIdAsync(userId);
        var availableUsers = (await GetOrganizationUsers(myWallet.WalletId)).Users.Select(u => u.Userid).ToList();

        //validate that all users in the request are part of the organization
        request.Permissions.ForEach(p =>
        {
            if (availableUsers.Contains(p.UserId))
            {
                return;
            }
            _logger.LogError("User {UserId} tried to set permissions for a user {BadUser} that is not part of the organization", userId, p.UserId);
            throw new UserNotFoundException();
        });

        //TODO Fix creation/expiration date to UTC both
        var repoUserPermissions = _mapper.Map<List<RepoUserPermission>>(request.Permissions);

        foreach (var permission in repoUserPermissions)
        {
            //validate that the user is not trying to set permissions for their own wallet
            if (myWallet.OwnerUserId == permission.UserId)
            {
                _logger.LogError("User {UserId} tried to set permissions for their own wallet", userId);
                throw new PermissionToOwnWalletException();
            }

            //permission.Id = Guid.NewGuid();
            permission.WalletId = myWallet.WalletId;
            permission.Admin = false;
            permission.AssignedBy = userId;
            permission.ExpirationDate = DateTime.UtcNow.AddYears(100);
            permission.CreatedAt = DateTime.UtcNow;

            await _corporateApiClientService.ConnectAccountAsync(permission.UserId, myWallet.WalletAccount);
        }

        List<RepoLimit> repoLimits = [];
        foreach (var permission in request.Permissions)
        {
            if (permission.Limits == null) continue;

            foreach (var limit in permission.Limits)
            {
                repoLimits.Add(limit.ToRepoLimit(permission.Id));
            }
        }

        try
        {
            await _userPermissionsRepositoryService.ExpireExistingPermissions(myWallet.WalletId, repoUserPermissions);
            await _userPermissionsRepositoryService.SaveAllAsync(repoUserPermissions);
            await _limitRepositoryService.InsertUserLimitsAsync(repoLimits);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set user permissions for wallet {WalletId}, Rollback", myWallet.WalletId);

            // Rollback any changes made during the transaction
            await _unitOfWork.RollbackPartialAsync();
            throw;
        }

        var userPermissionResponse = new UserPermissionResponse();
        var permissions = await _userPermissionsRepositoryService.FindAllByActiveAndWalletIdAndUserId(myWallet.WalletId, null);
        userPermissionResponse.Permissions = _mapper.Map<List<UserPermission>>(permissions);

        foreach (var p in userPermissionResponse.Permissions)
        {
            var userlimits = repoLimits.Where(x => x.PermissionId == p.Id).ToList();
            p.Limits = _mapper.Map<List<Limit>>(userlimits);
        }

        //TODO: return only user permissions sent in the request (e.g, 3 user permissions sent, respond with 3 permissions)

        var userlist = request.Permissions.Select(x => x.UserId);

        userPermissionResponse.Permissions = userPermissionResponse.Permissions.Where(id => userlist.Contains(id.UserId)).ToList();

        return userPermissionResponse;
    }

    public async Task<AvailableUserResponse> GetOrganizationUsers(Guid walletid)
    {
        var userId = _httpContextRepositoryService.GetUserId();
        var result = await _corporateApiClientService.GetCompanyUsersAsync(userId);
        var response = new AvailableUserResponse
        {
            Users = result.UserProfiles.Select(x => new AvailableUser
            {
                Userid = x.UserId,
                Alias = x.Alias
            }).ToList()
        };
        return response;
    }
}
