﻿using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using EWallet.Tests.Helpers;
using FluentAssertions;
using Nbg.Ewallet.Api.Types;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Types;
using specflow.tests.Helpers;
using SpecFlowConfiguration;
using TechTalk.SpecFlow;

namespace EWallet.Tests.Steps;

[Binding]
public class ExternalPermissionsSteps
{
    private readonly HttpClient _httpClient;
    private FeatureContext _featureContext;
    private ISpecFlownfigurationService _specFlownfigurationService;
    public ExternalPermissionsSteps(HttpClient httpClient, FeatureContext featureContext, ISpecFlownfigurationService specFlownfigurationService)
    {
        _httpClient = httpClient;
        _featureContext = featureContext;
        _specFlownfigurationService = specFlownfigurationService;
    }

    [Then(@"I give permissions from the first to the second")]
    public async Task ThenIGivePermissionsFromTheFirstToTheSecond()
    {
        var wallets = (List<Wallet>)_featureContext["RegisteredWallets"];
        var walletpermission = new WalletPermissionsRequest
        {
            Permissions = new List<WalletPermission>
            {
                new WalletPermission
                {
                    Approve = true,
                    Submit = true,
                    BalanceView = true,
                    TransactionView = true,
                    WalletId = wallets[1].WalletId,
                    TargetWalletId = wallets[0].WalletId,
                     Limits = new List<Limit>
                    {
                        new Limit
                        {
                            Amount = 50,
                            Timeframe= TimeFrameType.YEARLY,
                            TransactionType = LimitTransactionType.WalletToWallet
                        }
                    }
                }

            }
        };
        var uri = $"wallet/{wallets[1].WalletId}/wallet-permissions";

        var response = await _httpClient.PostAsJsonAsync(uri, walletpermission);
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
    }

    [Given(@"I have registered a <targetWalletName>")]
    public void GivenIHaveRegisteredATargetWalletName()
    {
        throw new PendingStepException();
    }

    [When(@"I give permissions to (.*) with (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*)")]
    public async Task WhenIGivePermissionsFromWalletToTargetWallet(string externalUserid, bool admin, bool approve,
                      bool balanceView, bool submit, bool transactionView, string transactionType, decimal amount, string timeframe)
    {
        var uri = "profile";
        var response = await _httpClient.GetAsync(uri);

        var profileResponseString = await response.Content.ReadAsStringAsync();
        var profileResponse = JsonSerializer.Deserialize<ProfileResponse>(profileResponseString);

        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        profileResponse.Should().NotBeNull();
        profileResponse.WalletId.Should().NotBeNull();
        var loggedUser = _featureContext["LoggedUser"];
        _featureContext["LoggedUserWalletId"] = profileResponse.WalletId;

        var externalluser = _specFlownfigurationService.GetConfiguration().GetUserId(externalUserid);
        _featureContext["ExternalUserWalletId"] = externalluser.walletId;

        var setWalletExternalPermissionsRequest = new WalletPermissionsRequest
        {
            Permissions = new List<WalletPermission>
            {
                new WalletPermission
                {
                    WalletId = Guid.Parse(profileResponse.WalletId),
                    TargetWalletId = Guid.Parse(externalluser.walletId),
                    BalanceView = balanceView,
                    Submit = submit,
                    TransactionView = transactionView,
                    Approve = approve,
                    Limits = new List<Limit>
                    {
                        new Limit
                        {
                            Amount = amount,
                            Timeframe= TimeFrameType.YEARLY,
                            TransactionType = LimitTransactionType.WalletToWallet
                        }
                    }
                }
            },
            IsSmsOtp = true,
            TanNumber = "smsotp"
        };

        uri = $"wallet/{profileResponse.WalletId}/wallet-permissions";
        var permissionsResponse = await _httpClient.PostAsJsonAsync(uri, setWalletExternalPermissionsRequest);
        permissionsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var smsotp = await SmsOtpHelper.GetSmsOtp(_featureContext["LoggedUser"].ToString());
        setWalletExternalPermissionsRequest.TanNumber = smsotp.Otp;

        uri = $"wallet/{profileResponse.WalletId}/wallet-permissions";
        permissionsResponse = await _httpClient.PostAsJsonAsync(uri, setWalletExternalPermissionsRequest);
        permissionsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

    }

    [Then(@"the external wallet must have (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*)")]
    public async Task ThenTheExternalExternalWalletNameMustHavePermissions(bool admin, bool approve,
         bool balanceView, bool submit, bool transactionView, string transactionType, decimal amount, string timeframe)
    {
        var walletPermissions = await GetWalletPermissions(Guid.Parse(_featureContext["LoggedUserWalletId"].ToString()),
                                                                             Guid.Parse(_featureContext["ExternalUserWalletId"].ToString()), false);
        walletPermissions[0].CreationDate.Should().BeOnOrBefore(DateTime.UtcNow);
        walletPermissions[0].ExpirationDate.Should().BeAfter(DateTime.UtcNow);
        walletPermissions[0].Approve.Should().Be(approve);
        walletPermissions[0].Submit.Should().Be(submit);
        // more should BE()s here to check Limmits etc
    }

    [When(@"I revoke permissions for (.*) for user (.*)")]
    public async Task WhenIRevokePermissionsForWalletForUser(string walletName, string userId)
    {
        var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
        var wallet = walletsList.FirstOrDefault(x => x.WalletName == walletName);
        var uri = $"wallet/{wallet.WalletId}/wallet-permissions/{wallet.WalletId}/revoke";
        var response = await _httpClient.PutAsJsonAsync(uri, new { });
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
    }

    [Then(@"I cannot revoke the permissions as (.*) for (.*)")]
    public async Task ThenICannotRevokeThePermissionsAs(string userId, string externalUserId)
    {
        var config = _specFlownfigurationService.GetConfiguration();

        var users = config.Environments.SandBox.Users;
        switch (config.Environment)
        {
            case "SandBox":
                {
                    var user = config.GetUserId(userId);

                    var token = await AuthCodeAuthorizationHelper.GetAuthCodeAuthorizationHeaderAsync(user.userName, user.passWord);
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                }
                break;
            case "SandBoxDirect":
                {
                    var user = config.Environments.SandBoxDirect.Users;
                    //remove old headers if any
                    _httpClient.DefaultRequestHeaders.Remove("UserId");
                    _httpClient.DefaultRequestHeaders.Remove("Customer-Code");

                    _httpClient.DefaultRequestHeaders.Add("UserId", userId);
                    //_httpClient.DefaultRequestHeaders.Add("Customer-Code", config.GetUser(userId).customerCode/*"0028049802"*/);
                    _httpClient.DefaultRequestHeaders.Add("Customer-Code", config.GetUserId(userId).customerCode/*"0028049802"*/);
                }
                break;
            case "Core":
                {
                    var user = config.Environments.Core.Users;
                    //remove old headers if any
                    _httpClient.DefaultRequestHeaders.Remove("UserId");
                    _httpClient.DefaultRequestHeaders.Remove("Customer-Code");

                    _httpClient.DefaultRequestHeaders.Add("UserId", userId);
                    //_httpClient.DefaultRequestHeaders.Add("Customer-Code", config.GetUser(userId).customerCode/*"0028049802"*/);
                    _httpClient.DefaultRequestHeaders.Add("Customer-Code", config.GetUserId(userId).customerCode/*"0028049802"*/);
                }
                break;
        }

        var walletId = config.GetUserId(userId).walletId;
        var externalWalletId = config.GetUserId(externalUserId).walletId;

        var uri = $"wallet/{walletId}/wallet-permissions/{externalWalletId}/revoke";
        var response = await _httpClient.PutAsJsonAsync(uri, new { });
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)400);
    }

    [When(@"I give permissions from (.*) to the same wallet with (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*) and (.*)")]
    public async Task WhenIGivePermissionsFromFirstRegisteredWalletToTheSameWithFalseAndTrueAndTrueAndTrueAndTrueAndWalletToWalletAndAndYEARLY(string userId, bool admin, bool approve,
                                            bool balanceView, bool submit, bool transactionView, string transactionType, decimal amount, string timeframe)
    {
        var uri = "profile";
        var response = await _httpClient.GetAsync(uri);

        var profileResponseString = await response.Content.ReadAsStringAsync();
        var profileResponse = JsonSerializer.Deserialize<ProfileResponse>(profileResponseString);

        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        profileResponse.Should().NotBeNull();
        profileResponse.WalletId.Should().NotBeNull();
        var loggedUser = _featureContext["LoggedUser"];
        _featureContext["LoggedUserWalletId"] = profileResponse.WalletId;

        var setWalletExternalPermissionsRequest = new WalletPermissionsRequest
        {
            Permissions = new List<WalletPermission>
            {
                new WalletPermission
                {
                    WalletId = Guid.Parse(profileResponse.WalletId.ToString()),
                    TargetWalletId = Guid.Parse(profileResponse.WalletId.ToString()),
                    BalanceView = balanceView,
                    Submit = submit,
                    TransactionView = transactionView,
                    Approve = approve,
                    Limits = new List<Limit>
                    {
                        new Limit
                        {
                            Amount = amount,
                            Timeframe= TimeFrameType.YEARLY,
                            TransactionType = LimitTransactionType.WalletToWallet
                        }
                    }
                }
            },
            IsSmsOtp = true,
            TanNumber = "smsotp"
        };

        uri = $"wallet/{profileResponse.WalletId}/wallet-permissions";
        var permissionsResponse = await _httpClient.PostAsJsonAsync(uri, setWalletExternalPermissionsRequest);
        permissionsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var smsotp = await SmsOtpHelper.GetSmsOtp(_featureContext["LoggedUser"].ToString());
        setWalletExternalPermissionsRequest.TanNumber = smsotp.Otp;

        uri = $"wallet/{profileResponse.WalletId}/wallet-permissions";
        permissionsResponse = await _httpClient.PostAsJsonAsync(uri, setWalletExternalPermissionsRequest);

        permissionsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)401);
    }


    [Then(@"no persissions are assigned the for (.*)")]
    public async Task ThenNoPersissionsAreAssignedTheForFirstRegisteredWallet(string userId)
    {
        // no need for this step
        // we got 401 from the previous step
    }


    [When(@"I have permissions from (.*) to (.*)")]
    public async Task WhenIHavePermissionsFromFirstRegisteredWalletToExternalWalletName(string userId, string externalUserId)
    {
        var uri = "profile";
        var response = await _httpClient.GetAsync(uri);

        var profileResponseString = await response.Content.ReadAsStringAsync();
        var profileResponse = JsonSerializer.Deserialize<ProfileResponse>(profileResponseString);

        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        profileResponse.Should().NotBeNull();
        profileResponse.WalletId.Should().NotBeNull();
        var loggedUser = _featureContext["LoggedUser"];
        _featureContext["LoggedUserWalletId"] = profileResponse.WalletId;

        var externalluser = _specFlownfigurationService.GetConfiguration().GetUserId(externalUserId);
        _featureContext["ExternalUserWalletId"] = externalluser.walletId;

        var walletId = _featureContext["LoggedUserWalletId"].ToString();
        var targetWalletId = _featureContext["ExternalUserWalletId"].ToString();

        var walletpermission = new WalletPermissionsRequest
        {
            Permissions = new List<WalletPermission>
            {
                new WalletPermission
                {
                    Approve = true,
                    Submit = true,
                    BalanceView = true,
                    TransactionView = true,
                    WalletId = Guid.Parse(walletId),
                    TargetWalletId = Guid.Parse(targetWalletId),
                    Limits = new List<Limit>
                    {
                        new Limit
                        {
                            Amount = 50,
                            Timeframe= TimeFrameType.YEARLY,
                            TransactionType = LimitTransactionType.WalletToWallet
                        }
                    }
                }
            },
            IsSmsOtp = true,
            TanNumber = "smsotp"
        };
        uri = $"wallet/{walletId}/wallet-permissions";

        var permissionsResponse = await _httpClient.PostAsJsonAsync(uri, walletpermission);
        permissionsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var smsotp = await SmsOtpHelper.GetSmsOtp(_featureContext["LoggedUser"].ToString());
        walletpermission.TanNumber = smsotp.Otp;

        uri = $"wallet/{profileResponse.WalletId}/wallet-permissions";
        permissionsResponse = await _httpClient.PostAsJsonAsync(uri, walletpermission);
        permissionsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);

        var walletPermissions = await GetWalletPermissions(Guid.Parse(walletId), Guid.Parse(targetWalletId));
        var permission = walletPermissions.FirstOrDefault(x => x.WalletId == Guid.Parse(walletId)
                                        && x.TargetWalletId == Guid.Parse(targetWalletId));
        permission.Should().NotBeNull();
    }

    [When(@"I revoke permissions from (.*) to (.*)")]
    public async Task WhenIRevokePermissionsFromFirstRegisteredWalletToExternalWalletName(string userId, string externalUserId)
    {
        var response = await RevokeExternalWalletPermissions(userId, externalUserId);
        response.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
    }

    [Then(@"the permission is revoked from (.*) to (.*)")]
    public async Task ThenThePermissionIsRevokedFromFirstRegisteredWalletToExternalWalletName(string walletName, string targetWalletName)
    {
        var walletId = _featureContext["LoggedUserWalletId"].ToString();
        var targetWalletId = _featureContext["ExternalUserWalletId"].ToString();

        var reponse = await GetWalletPermissions(Guid.Parse(walletId), Guid.Parse(targetWalletId));
        reponse.Count().Should().Be(0);
    }

    private async Task<HttpResponseMessage> RevokeExternalWalletPermissions(string userId, string external)
    {
        var walletId = _featureContext["LoggedUserWalletId"].ToString();
        var targetWalletId = _featureContext["ExternalUserWalletId"].ToString();

        var uri = $"wallet/{walletId}/wallet-permissions/{targetWalletId}/revoke";
        var response = await _httpClient.PutAsJsonAsync(uri, new
        {
            IsSmsOtp = true,
            TanNumber = "smsotp"
        });

        // just for debugging
        //var profileResponseString = await response.Content.ReadAsStringAsync();
        var smsotp = await SmsOtpHelper.GetSmsOtp(_featureContext["LoggedUser"].ToString());

        uri = $"wallet/{walletId}/wallet-permissions/{targetWalletId}/revoke";
        response = await _httpClient.PutAsJsonAsync(uri, new
        {
            IsSmsOtp = true,
            TanNumber = smsotp.Otp
        });

        return response;
    }

    [Then(@"Only the Active permissions are retrieved for all wallets for (.*)")]
    public async Task ThenOnlyTheActivePermissionsAreRetrievedForAllWalletsForFirstRegisteredWallet(string userId)
    {
        var walletId = _featureContext["LoggedUserWalletId"].ToString();

        var response = await GetWalletPermissions(Guid.Parse(walletId));
        // checks here
        response.Count().Should().Be(1);
        response.ElementAt(0).ExpirationDate.Should().BeAfter(DateTime.UtcNow);
    }

    //[Then(@"All permissions are retrieved for all wallets for (.*)")]
    //public async Task ThenAllPermissionsAreRetrievedForAllWalletsForFirstRegisteredWallet(string walletName)
    //{
    //    var walletsList = (List<Wallet>)_featureContext["RegisteredWallets"];
    //    var wallet = walletsList.FirstOrDefault(x => x.WalletName == walletName);
    //    var response = await GetWalletPermissions(wallet.WalletId, null, true);
    //    response.Count.Should().Be(2);
    //}

    [Then(@"Only the Active permissions are retrieved for (.*) and (.*)")]
    public async Task ThenOnlyTheActivePermissionsAreRetrievedForFirstRegisteredWalletAndExternalWalletName(string userId, string externalUserId)
    {
        var walletId = _featureContext["LoggedUserWalletId"].ToString();
        var targetWalletId = _featureContext["ExternalUserWalletId"].ToString();

        var response = await GetWalletPermissions(Guid.Parse(walletId), Guid.Parse(targetWalletId), false);
        response.Count().Should().Be(1);
        response.ElementAt(0).ExpirationDate.Should().BeAfter(DateTime.UtcNow);
    }

    [Then(@"All permissions are retrieved for (.*) and (.*)")]
    public async Task ThenAllPermissionsAreRetrievedForFirstRegisteredWalletAndExternalWalletName(string walletName, string targetWalletName)
    {
        var walletId = _featureContext["LoggedUserWalletId"].ToString();
        var targetWalletId = _featureContext["ExternalUserWalletId"].ToString();


        var response = await GetWalletPermissions(Guid.Parse(walletId), Guid.Parse(targetWalletId), true);
        foreach (var permission in response)
        {
            permission.WalletId.Should().Be(walletId);
            permission.TargetWalletId.Should().Be(targetWalletId);
        }
    }

    private async Task<List<WalletPermission>> GetWalletPermissions(Guid walletId, Guid? targetWallet = null, bool showAll = false)
    {
        string walletPermissionsUri;
        if (targetWallet != null)
        {
            walletPermissionsUri = $"wallet/{walletId.ToString()}/wallet-permissions/{targetWallet.ToString()}";
        }
        else
        {
            walletPermissionsUri = $"wallet/{walletId.ToString()}/wallet-permissions";
        }

        if (showAll)
        {
            walletPermissionsUri = walletPermissionsUri + "?showAll=true";
        }

        var walletRightsResponse = await _httpClient.GetAsync(walletPermissionsUri);
        walletRightsResponse.StatusCode.Should().Be((System.Net.HttpStatusCode)200);
        var walletPermissionsResponseString = await walletRightsResponse.Content.ReadAsStringAsync();

        var walletPermissions = JsonSerializer.Deserialize<List<WalletPermission>>(walletPermissionsResponseString);
        return walletPermissions;
    }
}
