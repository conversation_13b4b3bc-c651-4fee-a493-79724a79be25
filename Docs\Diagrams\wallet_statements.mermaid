sequenceDiagram
	autonumber
	Client->>EWallet.Proxy: /wallet/{walletId}/statements GET
	EWallet.Proxy->>EWallet.Core: /wallet/{walletId}/statements GET
    EWallet.Core->>EWallet.DB: GET
    note over EWallet.DB: DB Table:  Wallets
    EWallet.DB->>EWallet.Core: {Wallet}
	EWallet.Core->>BigDataAppsQa: statements/getStatements/ POST
	BigDataAppsQa->>EWallet.Core: {statements}
	EWallet.Core->>EWallet.Proxy: {statements}
	EWallet.Proxy->>Client: {statements}